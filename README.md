This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

## Video Generation Features

This application includes advanced AI-powered video generation capabilities using cutting-edge models from Replicate and Runway ML:

### Generation Modes
- **Text-to-Video**: Generate videos from text prompts using models like Google's Veo 2, Haiper Video 2, and Lu<PERSON>.
- **Image-to-Video**: Animate still images into videos using models like Runway Gen-3A, <PERSON><PERSON>, and Wan I2V.
- **Video Editing**: Apply style transfer and transformations to existing videos.
- **Video Upscaling**: Enhance video resolution and quality using Real-ESRGAN and AnimeSR.
- **Audio Generation**: Add AI-generated audio to videos based on text prompts.

### Supported Models
- Replicate models: Google Veo 2, Haiper, Luma Ray series, Wan series, Kling series, and more.
- Runway ML models: Gen-3A Turbo for both text-to-video and image-to-video generation.

### Environment Variables
To use these features, you need to set the following environment variables:
- `REPLICATE_API_TOKEN`: Your Replicate API token
- `RUNWAYML_API_SECRET`: Your Runway ML API key

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
