import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  /* config options here */
  eslint: {
    // Disable ESLint during production builds
    ignoreDuringBuilds: true,
  },
  webpack: (config, { isServer }) => {
    // Handle Node.js specific modules
    if (!isServer) {
      // Handle fs, aws-sdk, mock-aws-s3, and nock which are only used on the server
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        'fs.realpath': false,
        'aws-sdk': false,
        'mock-aws-s3': false,
        nock: false,
      };
    }
    return config;
  },
};

export default withNextIntl(nextConfig);
