{"name": "astrostudio-ai", "version": "0.1.0", "private": true, "description": "AstroStudio AI - Advanced AI-powered content generation platform with multi-model chat, image generation, and subscription management", "keywords": ["ai", "nextjs", "typescript", "openai", "anthropic", "image-generation", "video-generation", "music-generation", "podcast-generation", "chat", "subscription", "stripe"], "author": "AstroStudio AI Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "clean": "rm -rf .next out", "analyze": "ANALYZE=true npm run build", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@anthropic-ai/sdk": "^0.10.0", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/client-ses": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@cartesia/cartesia-js": "^2.1.10", "@deepgram/sdk": "^3.11.2", "@fal-ai/client": "^1.5.0", "@ffprobe-installer/ffprobe": "^2.1.2", "@heroicons/react": "^2.2.0", "@runwayml/sdk": "^1.4.4", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.7.0", "@supabase/supabase-js": "^2.49.1", "@types/ffmpeg-static": "^3.0.3", "@types/fluent-ffmpeg": "^2.1.27", "@types/js-cookie": "^3.0.6", "@upstash/redis": "^1.28.4", "@vercel/analytics": "^1.1.1", "@vercel/blob": "^0.27.3", "@vercel/kv": "^0.2.4", "@vercel/speed-insights": "^1.0.2", "ai": "^4.3.16", "audioconcat": "^0.1.4", "axios": "^1.8.1", "bcrypt": "^5.1.1", "date-fns": "^3.3.1", "dotenv": "^16.4.7", "elevenlabs": "^1.53.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^12.17.0", "jose": "^5.10.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.486.0", "neo4j-driver": "^5.28.1", "next": "^15.3.3", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "nodemailer": "^7.0.3", "ogl": "^1.0.11", "openai": "^4.28.0", "playht": "^0.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^9.1.0", "react-spinners": "^0.13.8", "react-syntax-highlighter": "^5.8.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "replicate": "^1.0.1", "sonner": "^2.0.1", "stripe": "^17.7.0", "swc": "^1.0.11", "together-ai": "^0.13.0", "uuid": "^11.1.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.2.0", "postcss": "^8.4.35", "tailwindcss": "^4", "typescript": "^5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "npm@10.0.0"}