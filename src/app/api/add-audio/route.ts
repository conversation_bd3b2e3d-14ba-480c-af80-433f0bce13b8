import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

// Initialize Replicate
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY ?? '',
});

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { 
      video, 
      prompt, 
      negative_prompt, 
      duration, 
      seed, 
      num_steps, 
      cfg_strength 
    } = body;

    console.log(`Starting audio generation for video with prompt: ${prompt}`);

    // Prepare prediction input
    const input = {
      video: video,  // URL to the video
      prompt: prompt,
      negative_prompt: negative_prompt ?? 'music',
      duration: duration ?? 8,
      seed: seed ?? -1,
      num_steps: num_steps ?? 25,
      cfg_strength: cfg_strength ?? 4.5
    };

    // Create prediction using mmaudio model
    const prediction = await replicate.predictions.create({
      version: 'zsxkib/mmaudio:4b9f801a167b1f6cc2db6ba7ffdeb307630bf411841d4e8300e63ca992de0be9',
      input: input,
    });

    // Wait for prediction to complete
    let result;
    if (prediction.id) {
      result = await waitForPrediction(prediction.id);
    } else {
      throw new Error('Failed to create audio generation prediction');
    }

    // Return video URL with audio in response
    return NextResponse.json({ 
      videoUrl: result.output, 
      message: 'Audio added to video successfully'
    });

  } catch (error: any) {
    console.error('Error adding audio to video:', error);
    return NextResponse.json(
      { error: error.message ?? 'Failed to add audio to video' },
      { status: 500 }
    );
  }
}

// Helper function to wait for prediction to complete
async function waitForPrediction(id: string) {
  let prediction = await replicate.predictions.get(id);
  
  // Poll until the prediction is complete
  while (prediction.status !== 'succeeded' && prediction.status !== 'failed') {
    await new Promise(resolve => setTimeout(resolve, 1000));
    prediction = await replicate.predictions.get(id);
  }
  
  if (prediction.status === 'failed') {
    throw new Error(prediction.error ? String(prediction.error) : 'Prediction failed with unknown error');
  }
  
  return prediction;
} 