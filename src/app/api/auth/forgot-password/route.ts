import { NextResponse } from 'next/server';
import { executeQuery, executeWrite } from '@/lib/neo4j';
import { sendPasswordResetEmail } from '@/lib/email';
import crypto from 'crypto';
import { z } from 'zod';

// Rate limiting storage (in production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Validation schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
  locale: z.string().optional().default('en'),
});

// Generate a secure random token
function generateResetToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Rate limiting function
function checkRateLimit(email: string): boolean {
  const now = Date.now();
  const key = `forgot-password:${email}`;
  const limit = rateLimitMap.get(key);

  if (!limit || now > limit.resetTime) {
    // Reset or create new limit
    rateLimitMap.set(key, { count: 1, resetTime: now + 3600000 }); // 1 hour
    return true;
  }

  if (limit.count >= 3) {
    return false; // Rate limited
  }

  limit.count++;
  return true;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Validate input
    const validationResult = forgotPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { email, locale } = validationResult.data;

    // Check rate limiting
    if (!checkRateLimit(email)) {
      return NextResponse.json(
        { success: false, error: 'RATE_LIMITED' },
        { status: 429 }
      );
    }

    // Check if user exists
    const query = `
      MATCH (u:User {email: $email})
      RETURN u.id AS id, u.email AS email, u.name AS name
    `;

    const results = await executeQuery(query, { email });

    // We should not indicate whether a user exists or not for security reasons
    // So we return success even if the user doesn't exist
    if (results.length === 0) {
      console.log(`Password reset requested for non-existent email: ${email}`);
      return NextResponse.json({ success: true });
    }

    const user = results[0].toObject();
    const resetToken = generateResetToken();
    const resetTokenExpiry = Date.now() + 3600000; // 1 hour from now

    // Store the reset token in the database
    const updateQuery = `
      MATCH (u:User {id: $id})
      SET u.resetToken = $resetToken, u.resetTokenExpiry = $resetTokenExpiry
      RETURN u.id AS id
    `;

    await executeWrite(updateQuery, {
      id: user.id,
      resetToken,
      resetTokenExpiry
    });

    // Send the password reset email with locale support
    const emailSent = await sendPasswordResetEmail(email, resetToken, locale, user.name);

    if (!emailSent) {
      console.error(`Failed to send password reset email to ${email}`);
      return NextResponse.json(
        { success: false, error: 'SEND_FAILED' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Password reset error:', error);
    return NextResponse.json(
      { success: false, error: 'INTERNAL_ERROR' },
      { status: 500 }
    );
  }
}