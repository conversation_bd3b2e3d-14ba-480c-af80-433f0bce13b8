import { NextResponse } from 'next/server';
import { executeQuery } from '@/lib/neo4j';
import crypto from 'crypto';
import * as jose from 'jose';

// Secret key for JWT
const JWT_SECRET = process.env.JWT_SECRET ?? 'astrostudio-secret-key';
// Convert to TextEncoder for jose
const secret = new TextEncoder().encode(JWT_SECRET);

// Helper function to verify password
function verifyPassword(storedPassword: string, suppliedPassword: string): boolean {
  const [salt, storedHash] = storedPassword.split(':');
  const suppliedHash = crypto.pbkdf2Sync(suppliedPassword, salt, 1000, 64, 'sha512').toString('hex');
  return storedHash === suppliedHash;
}

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find user by email
    const query = `
      MATCH (u:User {email: $email})
      RETURN u.id AS id, u.name AS name, u.email AS email, u.password AS password, u.role AS role
    `;
    
    const results = await executeQuery(query, { email });
    
    if (results.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    const user = results[0].toObject();
    
    // Verify password
    if (!verifyPassword(user.password, password)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Create JWT token using jose instead of jsonwebtoken
    const token = await new jose.SignJWT({ 
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('7d')
      .sign(secret);

    // Create response with authentication token
    const response = NextResponse.json({
      success: true,
      message: 'Login successful',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });

    // Set cookie on the response
    response.cookies.set({
      name: 'auth_token',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
      sameSite: 'lax' // Changed from strict to lax for better redirect support
    });
    
    console.log('Auth cookie set successfully');

    return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, error: 'Authentication failed' },
      { status: 500 }
    );
  }
} 