import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import * as jose from 'jose';

export async function GET(req: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 9);
  console.log(`[${requestId}] Auth check request received`);
  
  try {
    // Log request headers for debugging
    const headerNames = Array.from(req.headers.keys());
    console.log(`[${requestId}] Request headers:`, headerNames);
    
    // Try to get session from NextAuth
    console.log(`[${requestId}] Getting NextAuth session...`);
    const session = await getServerSession(authOptions);
    
    if (session && session.user) {
      console.log(`[${requestId}] User authenticated via NextAuth: ${session.user.email}`);
      return NextResponse.json({ 
        user: session.user,
        provider: 'next-auth',
      });
    }
    
    // If NextAuth session is not available, try to get from token
    console.log(`[${requestId}] NextAuth session not found, trying JWT token...`);
    
    // Check for a JWT token in request cookies directly
    const cookieString = req.headers.get('cookie') || '';
    const authToken = getCookieValue(cookieString, 'next-auth.session-token');
    const secureAuthToken = getCookieValue(cookieString, '__Secure-next-auth.session-token');
    
    const token = authToken || secureAuthToken;
    
    if (!token) {
      console.error(`[${requestId}] No authentication token found in cookies`);
      return new NextResponse(
        JSON.stringify({ error: 'Not authenticated', details: 'No session or token found' }),
        { status: 401 }
      );
    }
    
    // Try to decode the token to get user info
    try {
      console.log(`[${requestId}] Decoding JWT token...`);
      const secret = new TextEncoder().encode(process.env.NEXTAUTH_SECRET || '');
      const { payload } = await jose.jwtVerify(token, secret);
      
      if (payload && payload.sub) {
        console.log(`[${requestId}] User authenticated via JWT: ${payload.email || payload.sub}`);
        return NextResponse.json({
          user: {
            id: payload.sub,
            name: payload.name,
            email: payload.email,
          },
          provider: 'jwt',
        });
      }
    } catch (tokenError) {
      console.error(`[${requestId}] Token validation error:`, tokenError);
    }
    
    // If we get here, no valid session was found
    console.error(`[${requestId}] No valid authentication found`);
    return new NextResponse(
      JSON.stringify({ 
        error: 'Not authenticated', 
        details: 'Session expired or invalid',
        redirectUrl: '/login',
      }),
      { status: 401 }
    );
  } catch (error: any) {
    console.error(`[${requestId}] Auth check error:`, error);
    return new NextResponse(
      JSON.stringify({ 
        error: 'Authentication error', 
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      }),
      { status: 500 }
    );
  }
}

// Helper function to get cookie value from cookie string
function getCookieValue(cookieString: string, name: string): string | null {
  const match = cookieString.match(new RegExp(`(^| )${name}=([^;]+)`));
  return match ? match[2] : null;
} 