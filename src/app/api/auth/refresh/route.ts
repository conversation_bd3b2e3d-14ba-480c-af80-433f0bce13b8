import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import * as jose from 'jose';

// Secret key for JWT
const JWT_SECRET = process.env.JWT_SECRET ?? 'astrostudio-secret-key';
// Convert to TextEncoder for jose
const secret = new TextEncoder().encode(JWT_SECRET);

/**
 * Refresh the authentication session if possible
 * This endpoint attempts to refresh the user's session by:
 * 1. Checking if there's a valid NextAuth session
 * 2. If not, attempting to refresh using a refresh token
 * 3. As a last resort, checking if there's a valid JWT token that can be extended
 */
export async function POST(req: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 9);
  console.log(`[${requestId}] Session refresh request received`);
  
  try {
    // Log request headers for debugging
    const headerNames = Array.from(req.headers.keys());
    console.log(`[${requestId}] Request headers:`, headerNames);
    
    // First check if the user already has a valid session
    console.log(`[${requestId}] Checking for existing NextAuth session...`);
    const session = await getServerSession(authOptions);
    
    if (session && session.user) {
      console.log(`[${requestId}] User already has valid session: ${session.user.email}`);
      return NextResponse.json({ 
        success: true,
        message: 'Session already valid',
        user: session.user,
      });
    }
    
    // No valid session, try to refresh from token
    console.log(`[${requestId}] No valid session, attempting refresh...`);
    
    // Get token from cookie
    const cookieString = req.headers.get('cookie') || '';
    const authToken = getCookieValue(cookieString, 'next-auth.session-token');
    const secureAuthToken = getCookieValue(cookieString, '__Secure-next-auth.session-token');
    
    const token = authToken || secureAuthToken;
    
    if (!token) {
      console.error(`[${requestId}] No token found for refresh`);
      return NextResponse.json(
        { 
          success: false, 
          error: 'No token found',
          redirectUrl: '/login',
        },
        { status: 401 }
      );
    }
    
    // Try to verify and decode the token
    try {
      console.log(`[${requestId}] Verifying token for refresh...`);
      const secret = new TextEncoder().encode(process.env.NEXTAUTH_SECRET || '');
      const { payload } = await jose.jwtVerify(token, secret);
      
      if (!payload || !payload.sub) {
        throw new Error('Invalid token payload');
      }
      
      // Create a fresh JWT token
      console.log(`[${requestId}] Creating fresh token for user: ${payload.email || payload.sub}`);
      
      // Set cookie with new session token
      const response = NextResponse.json({
        success: true,
        message: 'Session refreshed successfully',
        user: {
          id: payload.sub,
          name: payload.name,
          email: payload.email,
        },
      });
      
      // Here would normally be the code to set a new session cookie
      // Since we're using NextAuth, the proper way would be to trigger a new sign in
      // For this example, we're just responding with success and suggesting a redirect
      
      return response;
    } catch (tokenError) {
      console.error(`[${requestId}] Token refresh error:`, tokenError);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid or expired token',
          message: 'Please sign in again',
          redirectUrl: '/login',
        },
        { status: 401 }
      );
    }
  } catch (error: any) {
    console.error(`[${requestId}] Session refresh error:`, error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Session refresh failed',
        message: error.message,
        redirectUrl: '/login',
      },
      { status: 500 }
    );
  }
}

// Helper function to get cookie value from cookie string
function getCookieValue(cookieString: string, name: string): string | null {
  const match = cookieString.match(new RegExp(`(^| )${name}=([^;]+)`));
  return match ? match[2] : null;
} 