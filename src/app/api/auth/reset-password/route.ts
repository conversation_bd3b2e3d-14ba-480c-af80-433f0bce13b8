import { NextResponse } from 'next/server';
import { executeQuery, executeWrite } from '@/lib/neo4j';
import { z } from 'zod';
import bcrypt from 'bcrypt';

// Validation schema
const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

// Helper function to hash password using bcrypt
async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Validate input
    const validationResult = resetPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { token, password } = validationResult.data;

    // Find user with this reset token
    const query = `
      MATCH (u:User)
      WHERE u.resetToken = $token AND u.resetTokenExpiry > $currentTime
      RETURN u.id AS id, u.email AS email
    `;

    const currentTime = Date.now();
    const results = await executeQuery(query, { token, currentTime });

    if (results.length === 0) {
      // Check if token exists but is expired
      const expiredTokenQuery = `
        MATCH (u:User)
        WHERE u.resetToken = $token
        RETURN u.id AS id
      `;

      const expiredResults = await executeQuery(expiredTokenQuery, { token });

      if (expiredResults.length > 0) {
        return NextResponse.json(
          { success: false, error: 'TOKEN_EXPIRED' },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: 'TOKEN_INVALID' },
        { status: 400 }
      );
    }

    const user = results[0].toObject();
    const hashedPassword = await hashPassword(password);

    // Update password and clear reset token
    const updateQuery = `
      MATCH (u:User {id: $id})
      SET u.password = $password, u.resetToken = NULL, u.resetTokenExpiry = NULL
      RETURN u.id AS id
    `;

    await executeWrite(updateQuery, {
      id: user.id,
      password: hashedPassword
    });

    console.log(`Password successfully reset for user: ${user.email}`);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Password reset error:', error);
    return NextResponse.json(
      { success: false, error: 'RESET_FAILED' },
      { status: 500 }
    );
  }
}