import { NextResponse } from 'next/server';
import { executeWrite } from '@/lib/neo4j';
import crypto from 'crypto';

// Helper function to hash password
function hashPassword(password: string): string {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
}

export async function POST(request: Request) {
  try {
    const { name, email, password } = await request.json();

    // Validate input
    if (!name || !email || !password) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if email is valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = hashPassword(password);

    // Check if user already exists
    const checkQuery = `
      MATCH (u:User {email: $email})
      RETURN u
    `;
    
    const existingUser = await executeWrite(checkQuery, { email });
    
    if (existingUser.length > 0) {
      return NextResponse.json(
        { success: false, error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Create the user
    const createQuery = `
      CREATE (u:User {
        id: randomUUID(),
        name: $name,
        email: $email,
        password: $hashedPassword,
        role: 'user',
        createdAt: datetime(),
        updatedAt: datetime()
      })
      RETURN u.id AS id, u.name AS name, u.email AS email, u.role AS role
    `;

    const result = await executeWrite(createQuery, {
      name,
      email,
      hashedPassword
    });

    const user = result[0].toObject();

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Signup error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create user' },
      { status: 500 }
    );
  }
} 