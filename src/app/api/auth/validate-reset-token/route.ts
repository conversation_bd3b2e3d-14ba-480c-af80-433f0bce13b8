import { NextResponse } from 'next/server';
import { executeQuery } from '@/lib/neo4j';

export async function GET(request: Request) {
  try {
    // Get token from URL
    const url = new URL(request.url);
    const token = url.searchParams.get('token');
    
    if (!token) {
      return NextResponse.json(
        { valid: false, error: 'Token is required' },
        { status: 400 }
      );
    }

    // Check if token exists and is not expired
    const query = `
      MATCH (u:User)
      WHERE u.resetToken = $token AND u.resetTokenExpiry > $currentTime
      RETURN count(u) as count
    `;
    
    const currentTime = Date.now();
    const results = await executeQuery(query, { token, currentTime });
    
    const count = results[0].toObject().count;
    
    if (count === 0) {
      return NextResponse.json(
        { valid: false, error: 'Invalid or expired token' },
        { status: 400 }
      );
    }

    return NextResponse.json({ valid: true });
  } catch (error) {
    console.error('Token validation error:', error);
    return NextResponse.json(
      { valid: false, error: 'Failed to validate token' },
      { status: 500 }
    );
  }
} 