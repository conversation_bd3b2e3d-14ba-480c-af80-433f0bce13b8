import { NextResponse } from 'next/server';
import { executeQuery } from '@/lib/neo4j';
import crypto from 'crypto';

// Helper function to verify password
function verifyPassword(storedPassword: string, suppliedPassword: string): boolean {
  try {
    const [salt, storedHash] = storedPassword.split(':');
    if (!salt || !storedHash) {
      console.error('Invalid password format in database');
      return false;
    }
    const suppliedHash = crypto.pbkdf2Sync(suppliedPassword, salt, 1000, 64, 'sha512').toString('hex');
    return storedHash === suppliedHash;
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
}

export async function POST(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 9);
  console.log(`[${requestId}] User validation request received`);
  
  try {
    const { email, password } = await request.json();
    console.log(`[${requestId}] Validating user with email: ${email}`);

    // Validate input
    if (!email || !password) {
      console.error(`[${requestId}] Missing email or password`);
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find user by email
    const query = `
      MATCH (u:User {email: $email})
      RETURN u.id AS id, u.name AS name, u.email AS email, u.password AS password, u.role AS role
    `;
    
    console.log(`[${requestId}] Executing Neo4j query to find user`);
    try {
      const results = await executeQuery(query, { email });
      
      console.log(`[${requestId}] Query returned ${results.length} results`);
      
      if (results.length === 0) {
        console.error(`[${requestId}] No user found with email: ${email}`);
        return NextResponse.json(
          { success: false, error: 'Invalid email or password' },
          { status: 401 }
        );
      }

      const user = results[0].toObject();
      console.log(`[${requestId}] User found: ${user.email}`);
      
      // Verify password
      console.log(`[${requestId}] Verifying password`);
      if (!verifyPassword(user.password, password)) {
        console.error(`[${requestId}] Password verification failed for user: ${email}`);
        return NextResponse.json(
          { success: false, error: 'Invalid email or password' },
          { status: 401 }
        );
      }

      console.log(`[${requestId}] Authentication successful for user: ${email}`);
      
      // Return user data for NextAuth
      return NextResponse.json({
        success: true,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role || 'user'
        }
      });
    } catch (dbError) {
      console.error(`[${requestId}] Database error during user lookup:`, dbError);
      return NextResponse.json(
        { success: false, error: 'Database error during authentication' },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error(`[${requestId}] Authentication error:`, error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Authentication failed', 
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 