import { NextRequest, NextResponse } from 'next/server';
// import { getServerSession } from 'next-auth';
// import { authOptions } from '@/lib/auth';
import togetherAI from '@/lib/together-ai';
import replicateAI from '@/lib/replicate';

export const runtime = 'edge';

// Check if API keys are available
const TOGETHER_API_KEY = process.env.TOGETHER_API_KEY;
const REPLICATE_API_KEY = process.env.REPLICATE_API_KEY;

export async function POST(request: NextRequest) {
  try {
    console.log('Chat API called');
    
    // Skip authentication temporarily for testing
    // const session = await getServerSession(authOptions);
    // if (!session || !session.user) {
    //   return NextResponse.json(
    //     { success: false, error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }
    
    // Parse the request body
    const body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    const { model: modelId, messages, message, temperature, max_tokens } = body;
    console.log('Extracted model ID:', modelId);
    console.log('Messages count:', messages?.length || 0);
    console.log('Direct message:', message?.substring(0, 100) + '...' || 'None');
    
    if (!modelId) {
      console.log('Error: Model ID is required');
      return NextResponse.json(
        { success: false, error: 'Model ID is required' },
        { status: 400 }
      );
    }

    // Get the user message - either from messages array or direct message
    let lastUserMessage: string;
    
    if (message && typeof message === 'string') {
      // Direct message format (used by podcast generation)
      lastUserMessage = message;
    } else if (messages && Array.isArray(messages) && messages.length > 0) {
      // Messages array format (used by chat interface)
      const userMessage = messages.filter(msg => msg.role === 'user').pop()?.content;
      if (!userMessage) {
        console.log('Error: No user message found in messages array');
        return NextResponse.json(
          { success: false, error: 'No user message found' },
          { status: 400 }
        );
      }
      lastUserMessage = userMessage;
    } else {
      console.log('Error: Either messages array or message string is required');
      return NextResponse.json(
        { success: false, error: 'Either messages array or message string is required' },
        { status: 400 }
      );
    }
    
    console.log('Final user message:', lastUserMessage?.substring(0, 100) + '...');
    
    if (!lastUserMessage) {
      console.log('Error: No user message content found');
      return NextResponse.json(
        { success: false, error: 'No user message content found' },
        { status: 400 }
      );
    }
    
    // Set default options
    const options = {
      temperature: temperature ?? 0.7,
      max_tokens: max_tokens ?? 4000
    };
    console.log('Using options:', options);
    
    // Process the message based on the selected model
    if (modelId === 'together-ai/deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free') {
      console.log('Using TogetherAI DeepSeek model');
      // DeepSeek model via Together AI
      try {
        const response = await togetherAI.sendMessageToDeepSeek(lastUserMessage, options);
        console.log('TogetherAI response received');
        
        return NextResponse.json({
          success: true,
          message: response.message
        });
      } catch (error: any) {
        console.error('Error with Together AI DeepSeek model:', error);
        return NextResponse.json(
          { success: false, error: error.message || 'Error with Together AI' },
          { status: 500 }
        );
      }
    } 
    else if (modelId === 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free' || modelId === 'together-ai/meta-llama/Llama-3.3-70B-Instruct-Turbo-Free') {
      console.log('Using TogetherAI Llama 3.3 model');
      // Llama 3.3 model via Together AI
      try {
        const response = await togetherAI.sendMessageToLlama(lastUserMessage, options);
        console.log('TogetherAI Llama response received');
        
        return NextResponse.json({
          success: true,
          message: response.message
        });
      } catch (error: any) {
        console.error('Error with Together AI Llama 3.3 model:', error);
        return NextResponse.json(
          { success: false, error: error.message || 'Error with Together AI' },
          { status: 500 }
        );
      }
    }
    else if (modelId === 'replicate/anthropic/claude-3.7-sonnet') {
      console.log('Using Replicate Claude 3.7 model');
      // Claude 3.7 model via Replicate
      try {
        const response = await replicateAI.sendMessageToClaude(lastUserMessage, options);
        console.log('Replicate response received');
        
        return NextResponse.json({
          success: true,
          message: response.message
        });
      } catch (error: any) {
        console.error('Error with Claude 3.7 on Replicate:', error);
        return NextResponse.json(
          { success: false, error: error.message || 'Error with Replicate' },
          { status: 500 }
        );
      }
    }
    else if (modelId === 'claude-3-5-sonnet-20241022' || modelId === 'replicate/anthropic/claude-3-5-sonnet-20241022') {
      console.log('Using Replicate Claude 3.5 Sonnet model');
      // Claude 3.5 Sonnet model via Replicate
      try {
        const response = await replicateAI.sendMessageToClaude(lastUserMessage, options);
        console.log('Replicate Claude 3.5 Sonnet response received');
        
        return NextResponse.json({
          success: true,
          message: response.message,
          response: response.message // Add response field for compatibility
        });
      } catch (error: any) {
        console.error('Error with Claude 3.5 Sonnet on Replicate:', error);
        return NextResponse.json(
          { success: false, error: error.message || 'Error with Replicate' },
          { status: 500 }
        );
      }
    }
    else {
      console.log(`Unsupported model ID: ${modelId}`);
      // Unsupported model
      return NextResponse.json(
        { success: false, error: `Unsupported model ID: ${modelId}. Only DeepSeek, Llama 3.3, Claude 3.7, and Claude 3.5 Sonnet are supported.` },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}