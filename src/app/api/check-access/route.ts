import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { FeatureAccess, hasFeatureAccess, USAGE_LIMITS, MODEL_ACCESS, hasImageGenerationModelAccess } from '@/lib/permissions';
import { getUserDailyUsage } from '@/lib/usage';
import { checkSubscriptionStatus } from '@/lib/subscription-utils';
import { SubscriptionService } from '@/lib/services/subscription-service';

// Cache for subscription validation results
// This significantly reduces database load
const validationCache = new Map<string, {
  data: any;
  timestamp: number;
}>();

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { allowed: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    if (!userId) {
      return NextResponse.json(
        { allowed: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }
    
    // Get the feature to check access for
    const searchParams = request.nextUrl.searchParams;
    const featureParam = searchParams.get('feature');
    const modelId = searchParams.get('modelId');
    
    if (!featureParam) {
      return NextResponse.json(
        { allowed: false, error: 'Missing feature parameter' },
        { status: 400 }
      );
    }
    
    // Check if the feature is valid
    if (!Object.values(FeatureAccess).includes(featureParam as FeatureAccess)) {
      return NextResponse.json(
        { allowed: false, error: 'Invalid feature' },
        { status: 400 }
      );
    }
    
    const feature = featureParam as FeatureAccess;
    
    // Get user's subscription info from cookies or database
    const { isSubscribed, subscriptionType } = await checkSubscriptionStatus(userId);
    const userPlan = subscriptionType || 'free';
    
    // Special case for free models 
    if (modelId && (modelId === 'black-forest-labs/FLUX.1-schnell-Free' || modelId === 'google/upscaler')) {
      // Always allow access to free models regardless of subscription
      return NextResponse.json({ 
        allowed: true, 
        reason: 'free_model',
        currentUsage: 0,
        dailyLimit: Infinity,
        usageRemaining: Infinity,
        plan: userPlan
      });
    }
    
    // Special case for image generation with free tier
    if (feature === FeatureAccess.IMAGE_GENERATION) {
      // Use our new function to check access to the specific model
      const hasAccess = hasImageGenerationModelAccess(userPlan, modelId || '');
      
      // If it's free tier, add additional access conditions
      if (userPlan === 'free') {
        // Only allow basic models for free tier
        const basicModels = MODEL_ACCESS.BASIC;
        if (hasAccess) {
          return NextResponse.json({ 
            allowed: true, 
            reason: 'free_tier_basic_models',
            currentUsage: await getUserDailyUsage(userId, feature),
            dailyLimit: USAGE_LIMITS.free.IMAGE_GENERATION.daily,
            usageRemaining: Math.max(0, USAGE_LIMITS.free.IMAGE_GENERATION.daily - await getUserDailyUsage(userId, feature)),
            plan: userPlan,
            allowedModels: basicModels
          });
        } else {
          return NextResponse.json({ 
            allowed: false, 
            reason: 'free_tier_model_restriction',
            plan: userPlan,
            feature: feature,
            allowedModels: basicModels
          });
        }
      }
      
      // For Standard and Premium, provide unrestricted access with appropriate usage limits
      if (hasAccess && (userPlan === 'Standard' || userPlan === 'Premium')) {
        const planKey = userPlan.toLowerCase() as keyof typeof USAGE_LIMITS;
        const planLimits = USAGE_LIMITS[planKey];
        const dailyLimit = planLimits?.IMAGE_GENERATION?.daily || 0;
        const currentUsage = await getUserDailyUsage(userId, feature);
        const usageRemaining = Math.max(0, dailyLimit - currentUsage);
        
        return NextResponse.json({
          allowed: true,
          reason: 'premium_or_standard_plan',
          currentUsage,
          dailyLimit,
          usageRemaining,
          plan: userPlan
        });
      }
    }
    
    // Check if the user has access to this feature
    const allowed = hasFeatureAccess(userPlan, feature);
    
    // If not allowed, return early
    if (!allowed) {
      return NextResponse.json({ 
        allowed: false, 
        reason: 'plan_restriction',
        plan: userPlan,
        feature: feature
      });
    }
    
    // Check usage limits
    const currentUsage = await getUserDailyUsage(userId, feature);
    
    // Get the usage limits for this feature based on the user's plan
    const planKey = userPlan.toLowerCase() as keyof typeof USAGE_LIMITS;
    const planLimits = USAGE_LIMITS[planKey] || USAGE_LIMITS.free;
    const featureKey = feature.toLowerCase() as keyof typeof planLimits;
    
    // Default to 0 if no limit is defined
    const dailyLimit = planLimits[featureKey]?.daily || 0;
    const usageRemaining = Math.max(0, dailyLimit - currentUsage);
    
    // Check if within limits
    const withinLimits = currentUsage < dailyLimit;
    
    return NextResponse.json({
      allowed: withinLimits,
      reason: withinLimits ? 'allowed' : 'usage_limit',
      currentUsage,
      dailyLimit,
      usageRemaining,
      plan: userPlan
    });
  } catch (error) {
    console.error('Error checking feature access:', error);
    return NextResponse.json(
      { allowed: false, error: 'Failed to check access' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id || !session?.user?.email) {
      return NextResponse.json({ 
        success: false, 
        message: 'Authentication required',
        subscription: null,
        isSubscribed: false
      }, { status: 401 });
    }
    
    // Parse the request body
    const body = await request.json();
    const { feature } = body;
    
    // Check cache first
    const cacheKey = `${session.user.id}:${feature || 'general'}`;
    const cachedData = validationCache.get(cacheKey);
    const now = Date.now();
    
    if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
      // Use cached data
      console.log('Using cached subscription data for validation');
      return NextResponse.json({
        success: true,
        subscription: cachedData.data,
        isSubscribed: cachedData.data?.status === 'active' || cachedData.data?.status === 'trialing',
        hasAccess: checkFeatureAccess(feature, cachedData.data?.type, cachedData.data?.status)
      });
    }
    
    // Try to get subscription from session first (fastest)
    if (session.user.subscription) {
      const subscription = {
        type: session.user.subscription.type,
        status: session.user.subscription.status,
        currentPeriodEnd: session.user.subscription.currentPeriodEnd
      };
      
      // Cache the result
      validationCache.set(cacheKey, {
        data: subscription,
        timestamp: now
      });
      
      return NextResponse.json({
        success: true,
        subscription,
        isSubscribed: subscription.status === 'active' || subscription.status === 'trialing',
        hasAccess: checkFeatureAccess(feature, subscription.type, subscription.status)
      });
    }
    
    // Get subscription from Stripe directly (source of truth)
    let subscription = null;
    
    try {
      // Try Stripe validation first (faster and more reliable)
      subscription = await SubscriptionService.validateSubscriptionFromStripe(session.user.email);
      
      // If no subscription in Stripe, check Neo4j as fallback
      if (!subscription) {
        subscription = await SubscriptionService.getUserSubscription(session.user.id);
      }
    } catch (error) {
      console.error('Error validating subscription:', error);
    }
    
    // Cache the result
    validationCache.set(cacheKey, {
      data: subscription,
      timestamp: now
    });
    
    // Return the result
    return NextResponse.json({
      success: true,
      subscription,
      isSubscribed: subscription?.status === 'active' || subscription?.status === 'trialing',
      hasAccess: checkFeatureAccess(feature, subscription?.type, subscription?.status)
    });
  } catch (error) {
    console.error('Error in subscription validation API:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Internal server error',
      subscription: null,
      isSubscribed: false,
      hasAccess: false
    }, { status: 500 });
  }
}

// Check if a user has access to a specific feature based on subscription
function checkFeatureAccess(feature: string, subscriptionType?: string, subscriptionStatus?: string): boolean {
  // If no feature specified, just check if subscription is active
  if (!feature) {
    return subscriptionStatus === 'active' || subscriptionStatus === 'trialing';
  }
  
  // If no subscription info, assume no access
  if (!subscriptionType || !subscriptionStatus) {
    return false;
  }
  
  // Subscription must be active or in trial
  if (subscriptionStatus !== 'active' && subscriptionStatus !== 'trialing') {
    return false;
  }
  
  // Free tier features
  const freeFeatures = ['FREE_TEXT_GENERATION', 'FREE_IMAGE_GENERATION'];
  if (freeFeatures.includes(feature)) {
    return true;
  }
  
  // Standard tier features
  if (subscriptionType === 'Standard' || subscriptionType === 'standard') {
    const standardFeatures = [
      'TEXT_GENERATION',
      'IMAGE_GENERATION',
      'IMAGE_EDITING',
    ];
    return standardFeatures.includes(feature);
  }
  
  // Premium tier has access to all features
  if (subscriptionType === 'Premium' || subscriptionType === 'premium') {
    return true;
  }
  
  return false;
} 