import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { checkSubscriptionStatus } from '@/lib/subscription-utils';
import { FeatureAccess, MODEL_ACCESS } from '@/lib/permissions';

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      console.log('Access check: Unauthorized request');
      return NextResponse.json(
        { allowed: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    if (!userId) {
      console.log('Access check: Invalid user session (no ID)');
      return NextResponse.json(
        { allowed: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }
    
    // Get the model to check access for
    const searchParams = request.nextUrl.searchParams;
    const modelId = searchParams.get('modelId');
    
    if (!modelId) {
      console.log('Access check: Missing modelId parameter');
      return NextResponse.json(
        { allowed: false, error: 'Missing modelId parameter' },
        { status: 400 }
      );
    }
    
    console.log(`Access check for user ${userId.substring(0, 8)}... for model ${modelId}`);
    
    // Always allow access to free models
    if (modelId === 'black-forest-labs/FLUX.1-schnell-Free' || modelId === 'google/upscaler') {
      console.log(`Access check: Free model access granted to ${modelId}`);
      return NextResponse.json({ 
        allowed: true, 
        reason: 'free_model',
        subscriptionType: 'free',
        hasFullAccess: false
      });
    }
    
    // Check subscription status from cookies first, then database if needed
    const { isSubscribed, subscriptionType } = await checkSubscriptionStatus(userId);
    
    // Premium and Standard users have full access to all models
    const hasFullAccess = isSubscribed && (subscriptionType === 'Premium' || subscriptionType === 'Standard');
    
    if (hasFullAccess) {
      console.log(`Access check: Full access granted for ${subscriptionType} subscription`);
      return NextResponse.json({
        allowed: true,
        reason: 'paid_subscription',
        subscriptionType,
        hasFullAccess
      });
    }
    
    // Free users only have access to basic models
    const isBasicModel = MODEL_ACCESS.BASIC.includes(modelId);
    
    if (isBasicModel) {
      console.log(`Access check: Basic model access granted to free tier user`);
    } else {
      console.log(`Access check: Access denied for free tier user to non-basic model ${modelId}`);
    }
    
    return NextResponse.json({
      allowed: isBasicModel,
      reason: isBasicModel ? 'free_tier_basic_model' : 'requires_subscription',
      subscriptionType,
      hasFullAccess,
      allowedModels: MODEL_ACCESS.BASIC
    });
    
  } catch (error) {
    console.error('Error checking image model access:', error);
    return NextResponse.json(
      { allowed: false, error: 'Failed to check access' },
      { status: 500 }
    );
  }
} 