import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createClient } from '@vercel/kv';

// Interface for the redirect info stored in KV
interface RedirectInfo {
  redirectUrl: string;
  subscriptionType: string;
  timestamp: number;
}

// KV client for temporary data storage
let kvClient: ReturnType<typeof createClient> | null = null;

function getKvClient() {
  if (!kvClient) {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      console.error('Missing KV store credentials');
      throw new Error('KV storage is not properly configured');
    }
    
    kvClient = createClient({
      url: process.env.KV_REST_API_URL,
      token: process.env.KV_REST_API_TOKEN,
    });
  }
  
  return kvClient;
}

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }
    
    // Check for payment success key in KV store
    const kv = getKvClient();
    const redirectKey = `payment_success:${userId}`;
    const redirectInfo = await kv.get<RedirectInfo | null>(redirectKey);
    
    if (!redirectInfo) {
      return NextResponse.json({ success: false });
    }
    
    // Delete the key after reading it (one-time use)
    await kv.del(redirectKey);
    
    // Return success with redirect URL
    return NextResponse.json({
      success: true,
      redirectUrl: redirectInfo.redirectUrl || '/dashboard',
      subscriptionType: redirectInfo.subscriptionType || 'standard',
      timestamp: redirectInfo.timestamp
    });
  } catch (error) {
    console.error('Error checking payment success:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check payment status' },
      { status: 500 }
    );
  }
} 