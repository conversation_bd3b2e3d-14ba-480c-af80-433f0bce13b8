import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { checkAndIncrementUsage } from '@/lib/usage';
import { FeatureAccess } from '@/lib/permissions';
import { getUserSubscription } from '@/lib/neo4j';

// ElevenLabs API endpoint
const ELEVENLABS_API_ENDPOINT = 'https://api.elevenlabs.io/v1';
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY || '';

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }
    
    // Get form data
    const formData = await req.formData();
    const audioFile = formData.get('audio') as File;
    const voiceName = formData.get('name') as string;
    const description = formData.get('description') as string;
    
    // Validate required parameters
    if (!audioFile) {
      return NextResponse.json(
        { success: false, error: 'Missing audio file' },
        { status: 400 }
      );
    }
    
    if (!voiceName) {
      return NextResponse.json(
        { success: false, error: 'Missing voice name' },
        { status: 400 }
      );
    }
    
    // Get user subscription
    const subscription = await getUserSubscription(userId);
    const userPlan = subscription?.type || 'free';
    
    // Check access and usage limits for voice cloning
    const accessCheck = await checkAndIncrementUsage(
      userId,
      userPlan,
      'voice_cloning' as FeatureAccess
    );
    
    if (!accessCheck.allowed) {
      // Check if it's due to feature restriction or usage limit
      const reason = accessCheck.currentUsage > 0 
        ? "You've reached your daily limit for voice cloning. Please upgrade your plan or try again tomorrow."
        : "Your current subscription plan doesn't include access to voice cloning. Please upgrade to continue.";
        
      return NextResponse.json(
        { 
          success: false, 
          error: reason,
          subscription: {
            plan: userPlan,
            currentUsage: accessCheck.currentUsage,
          }
        },
        { status: 403 }
      );
    }
    
    // Convert File to ArrayBuffer
    const arrayBuffer = await audioFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Clone voice using ElevenLabs API
    try {
      // Create form data for the API request
      const apiFormData = new FormData();
      apiFormData.append('name', voiceName);
      apiFormData.append('description', description || `Cloned voice for ${voiceName}`);
      
      // Create a new File object from the buffer
      const fileBlob = new Blob([buffer], { type: audioFile.type });
      apiFormData.append('files', fileBlob, audioFile.name);
      
      // Make the API request
      const response = await fetch(`${ELEVENLABS_API_ENDPOINT}/voices/add`, {
        method: 'POST',
        headers: {
          'xi-api-key': ELEVENLABS_API_KEY,
        },
        body: apiFormData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`ElevenLabs API error: ${JSON.stringify(errorData)}`);
      }
      
      const responseData = await response.json();
      
      // Get updated usage after generation
      let usageRemaining = null;
      try {
        const { getUserDailyUsage } = await import('@/lib/usage');
        const { USAGE_LIMITS } = await import('@/lib/permissions');
        
        const currentUsage = await getUserDailyUsage(userId, 'voice_cloning' as FeatureAccess);
        const planLimits = USAGE_LIMITS[userPlan as keyof typeof USAGE_LIMITS] || USAGE_LIMITS.free;
        const featureKey = 'voice_cloning' as keyof typeof planLimits;
        const dailyLimit = planLimits[featureKey]?.daily || 0;
        
        usageRemaining = Math.max(0, dailyLimit - currentUsage);
      } catch (error) {
        console.error('Error getting updated usage:', error);
      }
      
      return NextResponse.json({
        success: true,
        voiceId: responseData.voice_id,
        name: voiceName,
        usageRemaining
      });
      
    } catch (error: any) {
      console.error('ElevenLabs voice cloning error:', error);
      return NextResponse.json(
        { 
          success: false, 
          error: error.message || 'Failed to clone voice',
          details: error.toString()
        },
        { status: 500 }
      );
    }
    
  } catch (error: any) {
    console.error('Error cloning voice:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to clone voice',
        details: error.toString()
      },
      { status: 500 }
    );
  }
} 