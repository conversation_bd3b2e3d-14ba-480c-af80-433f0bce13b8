import { NextRequest, NextResponse } from 'next/server';

// For handling image processing
import sharp from 'sharp';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// Helper function to check if URL is from Replicate
function isReplicateUrl(url: string): boolean {
  return url.includes('replicate.delivery') || url.includes('replicate.com');
}

// Helper function to convert data URL to buffer
function dataURLToBuffer(dataURL: string): Buffer {
  // Extract the base64 data from the URL
  const matches = dataURL.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);
  
  if (!matches || matches.length !== 3) {
    throw new Error('Invalid data URL format');
  }
  
  const data = matches[2];
  return Buffer.from(data, 'base64');
}

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    const { imageUrl, format, quality = 1.0, filename = 'image' } = body;
    
    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }
    
    if (!format) {
      return NextResponse.json(
        { error: 'Format is required' },
        { status: 400 }
      );
    }
    
    // Validate format
    if (!['webp', 'jpg', 'jpeg', 'png', 'svg'].includes(format)) {
      return NextResponse.json(
        { error: 'Invalid format. Supported formats: webp, jpg, png, svg' },
        { status: 400 }
      );
    }
    
    let imageBuffer: ArrayBuffer | SharedArrayBuffer;
    let originalContentType = '';
    
    // Handle data URLs
    if (imageUrl.startsWith('data:')) {
      console.log('Processing data URL for image conversion');
      try {
        const buffer = dataURLToBuffer(imageUrl);
        imageBuffer = buffer.buffer.slice(
          buffer.byteOffset,
          buffer.byteOffset + buffer.byteLength
        );
        
        // Extract content type from data URL
        const matches = imageUrl.match(/^data:([A-Za-z-+/]+);base64,/);
        originalContentType = matches?.[1] || '';
      } catch (error) {
        console.error('Error processing data URL:', error);
        return NextResponse.json(
          { error: 'Invalid data URL format' },
          { status: 400 }
        );
      }
    } else {
      // Fetch the image from URL
      console.log('Fetching image from URL for conversion:', imageUrl);
      
      // Special handling for Replicate URLs
      const fetchHeaders: Record<string, string> = {
        'Accept': 'image/*',
        'User-Agent': 'Mozilla/5.0 (compatible; AstroStudio/1.0)'
      };
      
      // Add specific headers for Replicate if needed
      if (isReplicateUrl(imageUrl)) {
        console.log('Detected Replicate URL for conversion, using optimized headers');
        fetchHeaders['Accept'] = 'image/png,image/jpeg,image/webp,image/*;q=0.8';
      }
      
      const imageResponse = await fetch(imageUrl, {
        headers: fetchHeaders,
        // Add timeout for better error handling
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });
      
      if (!imageResponse.ok) {
        console.error(`Failed to fetch image for conversion: ${imageResponse.status} ${imageResponse.statusText}`);
        return NextResponse.json(
          { error: `Failed to fetch image: ${imageResponse.statusText}` },
          { status: 500 }
        );
      }
      
      // Get the original content type
      originalContentType = imageResponse.headers.get('content-type') ?? '';
      console.log('Original content type for conversion:', originalContentType);
      imageBuffer = await imageResponse.arrayBuffer();
      console.log('Image buffer size for conversion:', imageBuffer.byteLength);
    }
    
    // Special handling for Replicate images - they are typically already in PNG format
    // If the image is already in the requested format, pass it through without conversion
    const isAlreadyCorrectFormat = (
      (format === 'webp' && originalContentType === 'image/webp') ||
      ((format === 'jpg' || format === 'jpeg') && originalContentType === 'image/jpeg') ||
      (format === 'png' && (originalContentType === 'image/png' || isReplicateUrl(imageUrl)))
    );
    
    if (isAlreadyCorrectFormat) {
      console.log('Image already in requested format or Replicate PNG, passing through');
      // Just pass through the original image with force-download headers
      const sanitizedFilename = filename.replace(/[^a-z0-9]/gi, '-').toLowerCase();
      const finalFilename = `${sanitizedFilename}.${format}`;
      
      // Ensure content type is set correctly for Replicate images
      const finalContentType = originalContentType || (format === 'png' ? 'image/png' : originalContentType);
      
      return new NextResponse(Buffer.from(imageBuffer as ArrayBuffer), {
        headers: {
          'Content-Type': finalContentType,
          'Content-Disposition': `attachment; filename="${finalFilename}"`,
          'Content-Length': Buffer.from(imageBuffer).length.toString(),
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'X-Content-Type-Options': 'nosniff'
        },
      });
    }
    
    // Process the image using sharp
    let processedImage;
    
    // Handle different formats
    switch (format) {
      case 'jpg':
      case 'jpeg':
        processedImage = await sharp(Buffer.from(imageBuffer))
          .jpeg({ quality: Math.floor(quality * 100) })
          .toBuffer();
        break;
        
      case 'png':
        processedImage = await sharp(Buffer.from(imageBuffer))
          .png()
          .toBuffer();
        break;
        
      case 'webp':
        processedImage = await sharp(Buffer.from(imageBuffer))
          .webp({ quality: Math.floor(quality * 100) })
          .toBuffer();
        break;
        
      case 'svg':
        // SVG is not directly supported by sharp for conversion
        // We'd need a different library for SVG or return an error
        return NextResponse.json(
          { error: 'SVG conversion is not supported yet' },
          { status: 400 }
        );
        
      default:
        return NextResponse.json(
          { error: 'Unsupported format' },
          { status: 400 }
        );
    }
    
    // Set appropriate content type
    const contentType = format === 'jpg' || format === 'jpeg' 
      ? 'image/jpeg' 
      : format === 'png' 
        ? 'image/png' 
        : 'image/webp';
    
    // Sanitize filename
    const sanitizedFilename = filename.replace(/[^a-z0-9]/gi, '-').toLowerCase();
    const finalFilename = `${sanitizedFilename}.${format}`;
    
    // Return the processed image with force-download headers
    return new NextResponse(processedImage as unknown as BodyInit, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${finalFilename}"`,
        'Content-Length': processedImage.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Content-Type-Options': 'nosniff'
      },
    });
    
  } catch (error) {
    console.error('Error converting image:', error);
    return NextResponse.json(
      { error: 'Failed to convert image' },
      { status: 500 }
    );
  }
}