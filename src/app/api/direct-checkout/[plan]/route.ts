import { NextResponse } from 'next/server';
import { createGuestCheckoutSession } from '@/lib/actions/subscription-actions';

// This route creates a direct checkout link for marketing purposes
export async function GET(request: Request) {
  try {
    // Extract the plan from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const plan = pathParts[pathParts.length - 1]; // Get the last part of the path
    
    if (!plan || (plan !== 'standard' && plan !== 'premium')) {
      return NextResponse.json(
        { error: 'Invalid plan type' },
        { status: 400 }
      );
    }
    
    // Convert plan string to the correct format for createGuestCheckoutSession
    const planType = plan === 'standard' ? 'Standard' : 'Premium';
    
    // Create checkout session without requiring email
    // Email will be collected during the Stripe checkout
    const result = await createGuestCheckoutSession(planType);
    
    if (result?.url) {
      // Redirect directly to the checkout URL
      return NextResponse.redirect(result.url);
    } else {
      return NextResponse.json(
        { error: 'Failed to create checkout link' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error generating direct checkout link:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 