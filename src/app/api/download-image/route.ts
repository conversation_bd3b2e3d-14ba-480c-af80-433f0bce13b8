import { NextRequest, NextResponse } from "next/server";

/**
 * API endpoint to download images from various sources
 * This is useful for saving images that might have CORS restrictions
 */
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// Helper function to convert data URL to buffer
function dataURLToBuffer(dataURL: string): Buffer {
  try {
    // Extract the base64 data from the URL
    const matches = dataURL.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);
    
    if (!matches || matches.length !== 3) {
      throw new Error('Invalid data URL format');
    }
    
    const data = matches[2];
    return Buffer.from(data, 'base64');
  } catch (error) {
    console.error('Error converting data URL to buffer:', error);
    throw error;
  }
}

// Helper function to check if URL is from Replicate
function isReplicateUrl(url: string): boolean {
  return url.includes('replicate.delivery') || url.includes('replicate.com');
}

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    const { imageUrl, format = 'png' } = body;
    
    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }
    
    let imageBuffer: ArrayBuffer | SharedArrayBuffer;
    let contentType = 'application/octet-stream';
    
    // Handle data URLs
    if (imageUrl.startsWith('data:')) {
      console.log('Processing data URL');
      try {
        const buffer = dataURLToBuffer(imageUrl);
        imageBuffer = buffer.buffer.slice(
          buffer.byteOffset,
          buffer.byteOffset + buffer.byteLength
        );
        
        // Extract content type from data URL
        const matches = imageUrl.match(/^data:([A-Za-z-+/]+);base64,/);
        contentType = matches?.[1] ?? contentType;
      } catch (error) {
        console.error('Error processing data URL:', error);
        return NextResponse.json(
          { error: 'Invalid data URL format' },
          { status: 400 }
        );
      }
    } else {
      // Fetch the image from URL
      console.log('Fetching image from URL:', imageUrl);
      
      // Special handling for Replicate URLs
      const fetchHeaders: Record<string, string> = {
        'Accept': 'image/*',
        'User-Agent': 'Mozilla/5.0 (compatible; AstroStudio/1.0)'
      };
      
      // Add specific headers for Replicate if needed
      if (isReplicateUrl(imageUrl)) {
        console.log('Detected Replicate URL, using optimized headers');
        fetchHeaders['Accept'] = 'image/png,image/jpeg,image/webp,image/*;q=0.8';
      }
      
      try {
        const imageResponse = await fetch(imageUrl, {
          headers: fetchHeaders,
          // Add timeout for better error handling
          signal: AbortSignal.timeout(30000) // 30 second timeout
        });
        
        if (!imageResponse.ok) {
          console.error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
          return NextResponse.json(
            { error: `Failed to fetch image: ${imageResponse.statusText}` },
            { status: 500 }
          );
        }
        
        // Get the content type
        contentType = imageResponse.headers.get('content-type') || 'image/png';
        console.log('Image content type:', contentType);
        
        imageBuffer = await imageResponse.arrayBuffer();
        console.log('Image buffer size:', imageBuffer.byteLength);
        
      } catch (error) {
        console.error('Error fetching image:', error);
        return NextResponse.json(
          { error: 'Failed to fetch image' },
          { status: 500 }
        );
      }
    }
    
    // Ensure we have a valid content type for images
    if (!contentType.startsWith('image/')) {
      contentType = 'image/png'; // Default to PNG for unknown types
    }
    
    // Convert to base64 for JSON response
    const base64 = Buffer.from(imageBuffer).toString('base64');
    const dataUrl = `data:${contentType};base64,${base64}`;
    
    // Return the blob as a data URL
    return NextResponse.json({
      dataUrl,
      contentType,
      success: true,
      size: imageBuffer.byteLength
    });
    
  } catch (error) {
    console.error('Error downloading image:', error);
    return NextResponse.json(
      { error: 'Failed to download image', success: false },
      { status: 500 }
    );
  }
}