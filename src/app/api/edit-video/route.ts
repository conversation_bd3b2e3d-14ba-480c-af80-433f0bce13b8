import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

// Initialize Replicate
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY || '',
});

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { model, prompt, negative_prompt, width, height, video_url } = body;

    console.log(`Starting video editing with model: ${model}`);

    // Prepare prediction input
    let input: Record<string, any> = {
      prompt: prompt,
    };

    // Add optional parameters if provided
    if (negative_prompt) input.negative_prompt = negative_prompt;
    if (video_url) {
      // Different models use different parameter names for the input video
      if (model === 'stability-ai/stable-video-diffusion-vid2vid') {
        input.video = video_url;
      } else {
        input.video_url = video_url;
      }
    }

    // Handle model-specific parameters
    switch (model) {
      case 'stability-ai/stable-video-diffusion-vid2vid':
        // Stable Video Diffusion specific parameters
        input.num_frames = 25; // Default value
        input.motion_bucket_id = 40; // Default motion amount
        break;
      case 'google/video-stylizer':
        // Google Video Stylizer specific parameters
        input.strength = 0.85; // Default style strength
        break;
      case 'topaz/video-enhance-ai':
        // Video Enhance AI specific parameters
        input.resolution = 'hd'; // Default resolution
        break;
      case 'ai-forever/sora-video-restoration':
        // Sora specific parameters
        break;
      default:
        // Default parameters for other models
        break;
    }

    // Create prediction
    const prediction = await replicate.predictions.create({
      version: model,
      input: input,
    });

    // Wait for prediction to complete
    let result;
    if (prediction.id) {
      result = await waitForPrediction(prediction.id);
    } else {
      throw new Error('Failed to create prediction');
    }

    // Return video URL in response
    return NextResponse.json({ 
      videoUrl: result.output, 
      message: 'Video edited successfully'
    });

  } catch (error: any) {
    console.error('Error editing video:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to edit video' },
      { status: 500 }
    );
  }
}

// Helper function to wait for prediction to complete
async function waitForPrediction(id: string) {
  let prediction = await replicate.predictions.get(id);
  
  // Poll until the prediction is complete
  while (prediction.status !== 'succeeded' && prediction.status !== 'failed') {
    await new Promise(resolve => setTimeout(resolve, 1000));
    prediction = await replicate.predictions.get(id);
  }
  
  if (prediction.status === 'failed') {
    throw new Error(prediction.error ? String(prediction.error) : 'Prediction failed with unknown error');
  }
  
  return prediction;
} 