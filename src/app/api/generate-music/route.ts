import { NextRequest, NextResponse } from 'next/server';
import { put } from '@vercel/blob';
import { uploadFile } from '@/lib/storage';
import { fal } from '@fal-ai/client';

// Initialize fal.ai client
fal.config({
  credentials: process.env.FAL_KEY || '',
});

// Define the request body interface for fal.ai models
interface GenerateMusicRequest {
  model: string;
  provider: string;
  userId?: string;
  saveToLibrary?: boolean;
  
  // Common parameters
  prompt?: string;
  duration?: number;
  seed?: number;
  
  // ACE-Step specific parameters
  tags?: string;
  lyrics?: string;
  instrumental?: boolean;
  number_of_steps?: number;
  scheduler?: 'euler' | 'heun';
  guidance_type?: 'cfg' | 'apg' | 'cfg_star';
  granularity_scale?: number;
  guidance_interval?: number;
  guidance_interval_decay?: number;
  guidance_scale?: number;
  minimum_guidance_scale?: number;
  tag_guidance_scale?: number;
  lyric_guidance_scale?: number;
  
  // Stable Audio specific parameters
  seconds_start?: number;
  seconds_total?: number;
  steps?: number;
  
  // MMAudio V2 specific parameters
  negative_prompt?: string;
  num_steps?: number;
  cfg_strength?: number;
  mask_away_clip?: boolean;
}

export async function POST(req: NextRequest) {
  try {
    // Parse request
    const body: GenerateMusicRequest = await req.json();
    
    // Validate request
    if (!body.model) {
      return NextResponse.json(
        { message: 'Missing required field: model' },
        { status: 400 }
      );
    }
    
    // Validation for fal.ai models
    if (body.model === 'fal-ai/ace-step') {
      if (!body.tags && !body.prompt) {
        return NextResponse.json(
          { message: 'For ACE-Step model, either tags or prompt must be provided' },
          { status: 400 }
        );
      }
    } else if (body.model === 'fal-ai/ace-step/prompt-to-audio') {
      if (!body.prompt) {
        return NextResponse.json(
          { message: 'For ACE-Step prompt-to-audio model, prompt is required' },
          { status: 400 }
        );
      }
    } else if (['fal-ai/stable-audio', 'fal-ai/mmaudio-v2/text-to-audio', 'fal-ai/lyria2', 'cassetteai/music-generator'].includes(body.model)) {
      if (!body.prompt) {
        return NextResponse.json(
          { message: 'Prompt is required for this model' },
          { status: 400 }
        );
      }
    } else if (['fal-ai/ace-step/audio-outpaint', 'fal-ai/ace-step/audio-inpaint'].includes(body.model)) {
      if (!body.prompt) {
        return NextResponse.json(
          { message: 'Prompt is required for audio processing models' },
          { status: 400 }
        );
      }
    }

    let audioUrl;
    let generationId;
    
    console.log(`Generating music with model: ${body.model}, provider: ${body.provider}`);

    try {
      // Route to the appropriate generation function based on the model
      if (body.model === 'fal-ai/ace-step') {
        ({ audioUrl, generationId } = await generateWithAceStep(body));
      } else if (body.model === 'fal-ai/ace-step/prompt-to-audio') {
        ({ audioUrl, generationId } = await generateWithAceStepPrompt(body));
      } else if (body.model === 'fal-ai/stable-audio') {
        ({ audioUrl, generationId } = await generateWithStableAudio(body));
      } else if (body.model === 'fal-ai/mmaudio-v2/text-to-audio') {
        ({ audioUrl, generationId } = await generateWithMMAudio(body));
      } else if (body.model === 'fal-ai/lyria2') {
        ({ audioUrl, generationId } = await generateWithLyria2(body));
      } else if (body.model === 'cassetteai/music-generator') {
        ({ audioUrl, generationId } = await generateWithCassetteAI(body));
      } else if (body.model === 'fal-ai/ace-step/audio-outpaint') {
        ({ audioUrl, generationId } = await generateWithAudioOutpaint(body));
      } else if (body.model === 'fal-ai/ace-step/audio-inpaint') {
        ({ audioUrl, generationId } = await generateWithAudioInpaint(body));
      } else {
        return NextResponse.json(
          { message: `Unsupported model: ${body.model}` },
          { status: 400 }
        );
      }
      
      // Save to user's library if requested
      if (body.saveToLibrary && body.userId) {
        await saveToUserLibrary(
          body.userId,
          audioUrl,
          body.lyrics ?? body.prompt ?? '',
          body.model,
          body.tags,
          body.lyrics,
          generationId
        );
      }
      
      return NextResponse.json({
        audioUrl,
        generationId,
        message: 'Music successfully generated'
      });
      
    } catch (error: any) {
      console.error('Provider API error:', error);
      return NextResponse.json(
        { message: `Provider API error: ${error.message || 'Unknown error'}` },
        { status: 500 }
      );
    }
    
  } catch (error: any) {
    console.error('Music generation error:', error);
    return NextResponse.json(
      { message: `Server error: ${error.message}` },
      { status: 500 }
    );
  }
}

// Generate music using the ACE-Step model via fal.ai
async function generateWithAceStep(body: GenerateMusicRequest) {
  console.log('Generating with ACE-Step model via fal.ai');
  
  // Prepare the input for the fal.ai API
  const input: Record<string, any> = {};
  
  // Required parameters
  if (body.tags) {
    input.tags = body.tags;
  }
  
  // Optional parameters
  if (body.lyrics) {
    input.lyrics = body.lyrics;
  }
  
  // Set defaults and optional parameters
  input.duration = body.duration || 60;
  input.number_of_steps = body.number_of_steps || 27;
  input.scheduler = body.scheduler || 'euler';
  input.guidance_type = body.guidance_type || 'apg';
  input.granularity_scale = body.granularity_scale || 10;
  input.guidance_interval = body.guidance_interval || 0.5;
  input.guidance_interval_decay = body.guidance_interval_decay || 0;
  input.guidance_scale = body.guidance_scale || 15;
  input.minimum_guidance_scale = body.minimum_guidance_scale || 3;
  input.tag_guidance_scale = body.tag_guidance_scale || 5;
  input.lyric_guidance_scale = body.lyric_guidance_scale || 1.5;
  
  if (body.seed !== undefined) {
    input.seed = body.seed;
  }

  console.log('Sending request to fal.ai with input:', JSON.stringify(input));
  
  try {
    const result = await fal.subscribe('fal-ai/ace-step', {
      input: input,
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === 'IN_PROGRESS') {
          console.log('Generation in progress:', update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    console.log('fal.ai result:', result);

    // Extract the audio URL from the result
    const audioUrl = result.data?.audio_url?.url || result.data?.audio?.url;
    
    if (!audioUrl) {
      throw new Error('No valid audio URL found in fal.ai response');
    }

    return {
      audioUrl,
      generationId: result.requestId || `ace-step-${Date.now()}`
    };
  } catch (error: any) {
    console.error('fal.ai API error:', error);
    throw new Error(`fal.ai API error: ${error.message || 'Unknown error'}`);
  }
}

// Generate music using the ACE-Step prompt-to-audio model via fal.ai
async function generateWithAceStepPrompt(body: GenerateMusicRequest) {
  console.log('Generating with ACE-Step prompt-to-audio model via fal.ai');
  
  const input: Record<string, any> = {
    prompt: body.prompt!,
    instrumental: body.instrumental || false,
    duration: body.duration || 60,
    number_of_steps: body.number_of_steps || 27,
    scheduler: body.scheduler || 'euler',
    guidance_type: body.guidance_type || 'apg',
    granularity_scale: body.granularity_scale || 10,
    guidance_interval: body.guidance_interval || 0.5,
    guidance_interval_decay: body.guidance_interval_decay || 0,
    guidance_scale: body.guidance_scale || 15,
    minimum_guidance_scale: body.minimum_guidance_scale || 3,
    tag_guidance_scale: body.tag_guidance_scale || 5,
    lyric_guidance_scale: body.lyric_guidance_scale || 1.5,
  };
  
  if (body.seed !== undefined) {
    input.seed = body.seed;
  }

  console.log('Sending request to fal.ai with input:', JSON.stringify(input));
  
  try {
    const result = await fal.subscribe('fal-ai/ace-step/prompt-to-audio', {
      input: input,
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === 'IN_PROGRESS') {
          console.log('Generation in progress:', update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    const audioUrl = result.data?.audio_url?.url || result.data?.audio?.url;
    
    if (!audioUrl) {
      throw new Error('No valid audio URL found in fal.ai response');
    }

    return {
      audioUrl,
      generationId: result.requestId || `ace-step-prompt-${Date.now()}`
    };
  } catch (error: any) {
    console.error('fal.ai API error:', error);
    throw new Error(`fal.ai API error: ${error.message || 'Unknown error'}`);
  }
}

// Generate music using the Stable Audio model via fal.ai
async function generateWithStableAudio(body: GenerateMusicRequest) {
  console.log('Generating with Stable Audio model via fal.ai');
  
  const input: Record<string, any> = {
    prompt: body.prompt!,
    seconds_total: body.seconds_total || body.duration || 30,
    steps: body.steps || 100,
  };
  
  if (body.seconds_start !== undefined) {
    input.seconds_start = body.seconds_start;
  }

  console.log('Sending request to fal.ai with input:', JSON.stringify(input));
  
  try {
    const result = await fal.subscribe('fal-ai/stable-audio', {
      input: {
        prompt: input.prompt,
        seconds_total: input.seconds_total,
        steps: input.steps,
        seconds_start: input.seconds_start,
      },
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === 'IN_PROGRESS') {
          console.log('Generation in progress:', update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    const audioUrl = result.data?.audio_file?.url;
    
    if (!audioUrl) {
      throw new Error('No valid audio URL found in fal.ai response');
    }

    return {
      audioUrl,
      generationId: result.requestId || `stable-audio-${Date.now()}`
    };
  } catch (error: any) {
    console.error('fal.ai API error:', error);
    throw new Error(`fal.ai API error: ${error.message || 'Unknown error'}`);
  }
}

// Generate music using the MMAudio V2 model via fal.ai
async function generateWithMMAudio(body: GenerateMusicRequest) {
  console.log('Generating with MMAudio V2 model via fal.ai');
  
  const input: Record<string, any> = {
    prompt: body.prompt!,
    negative_prompt: body.negative_prompt || '',
    num_steps: body.num_steps || 25,
    duration: body.duration || 8,
    cfg_strength: body.cfg_strength || 4.5,
  };
  
  if (body.seed !== undefined) {
    input.seed = body.seed;
  }
  
  if (body.mask_away_clip !== undefined) {
    input.mask_away_clip = body.mask_away_clip;
  }

  console.log('Sending request to fal.ai with input:', JSON.stringify(input));
  
  try {
    const result = await fal.subscribe('fal-ai/mmaudio-v2/text-to-audio', {
      input: {
        prompt: input.prompt,
        negative_prompt: input.negative_prompt,
        num_steps: input.num_steps,
        duration: input.duration,
        cfg_strength: input.cfg_strength,
        seed: input.seed,
        mask_away_clip: input.mask_away_clip
      },
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === 'IN_PROGRESS') {
          console.log('Generation in progress:', update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    const audioUrl = result.data?.audio?.url;
    
    if (!audioUrl) {
      throw new Error('No valid audio URL found in fal.ai response');
    }

    return {
      audioUrl,
      generationId: result.requestId || `mmaudio-${Date.now()}`
    };
  } catch (error: any) {
    console.error('fal.ai API error:', error);
    throw new Error(`fal.ai API error: ${error.message || 'Unknown error'}`);
  }
}

// Generate music using the Lyria 2 model via fal.ai
async function generateWithLyria2(body: GenerateMusicRequest) {
  console.log('Generating with Lyria 2 model via fal.ai');
  
  const input: Record<string, any> = {
    prompt: body.prompt!,
    duration: body.duration || 30,
  };
  
  if (body.seed !== undefined) {
    input.seed = body.seed;
  }

  console.log('Sending request to fal.ai with input:', JSON.stringify(input));
  
  try {
    const result = await fal.subscribe('fal-ai/lyria2', {
      input: input,
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === 'IN_PROGRESS') {
          console.log('Generation in progress:', update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    const audioUrl = result.data?.audio?.url || result.data?.audio_file?.url;
    
    if (!audioUrl) {
      throw new Error('No valid audio URL found in fal.ai response');
    }

    return {
      audioUrl,
      generationId: result.requestId || `lyria2-${Date.now()}`
    };
  } catch (error: any) {
    console.error('fal.ai API error:', error);
    throw new Error(`fal.ai API error: ${error.message || 'Unknown error'}`);
  }
}

// Generate music using the CassetteAI Music Generator model via fal.ai
async function generateWithCassetteAI(body: GenerateMusicRequest) {
  console.log('Generating with CassetteAI Music Generator model via fal.ai');
  
  const input: Record<string, any> = {
    prompt: body.prompt!,
    duration: body.duration || 60,
  };
  
  if (body.seed !== undefined) {
    input.seed = body.seed;
  }

  console.log('Sending request to fal.ai with input:', JSON.stringify(input));
  
  try {
    const result = await fal.subscribe('cassetteai/music-generator', {
      input: input,
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === 'IN_PROGRESS') {
          console.log('Generation in progress:', update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    const audioUrl = result.data?.audio?.url || result.data?.audio_file?.url;
    
    if (!audioUrl) {
      throw new Error('No valid audio URL found in fal.ai response');
    }

    return {
      audioUrl,
      generationId: result.requestId || `cassetteai-${Date.now()}`
    };
  } catch (error: any) {
    console.error('fal.ai API error:', error);
    throw new Error(`fal.ai API error: ${error.message || 'Unknown error'}`);
  }
}

// Generate music using the ACE-Step Audio Outpaint model via fal.ai
async function generateWithAudioOutpaint(body: GenerateMusicRequest) {
  console.log('Generating with ACE-Step Audio Outpaint model via fal.ai');
  
  const input: Record<string, any> = {
    prompt: body.prompt!,
    duration: body.duration || 200,
    number_of_steps: body.number_of_steps || 27,
    scheduler: body.scheduler || 'euler',
    guidance_type: body.guidance_type || 'apg',
    granularity_scale: body.granularity_scale || 10,
    guidance_interval: body.guidance_interval || 0.5,
    guidance_interval_decay: body.guidance_interval_decay || 0,
    guidance_scale: body.guidance_scale || 15,
    minimum_guidance_scale: body.minimum_guidance_scale || 3,
  };
  
  if (body.tags) {
    input.tags = body.tags;
  }
  
  if (body.seed !== undefined) {
    input.seed = body.seed;
  }

  console.log('Sending request to fal.ai with input:', JSON.stringify(input));
  
  try {
    const result = await fal.subscribe('fal-ai/ace-step/audio-outpaint', {
      input: input,
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === 'IN_PROGRESS') {
          console.log('Generation in progress:', update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    const audioUrl = result.data?.audio_url?.url || result.data?.audio?.url;
    
    if (!audioUrl) {
      throw new Error('No valid audio URL found in fal.ai response');
    }

    return {
      audioUrl,
      generationId: result.requestId || `audio-outpaint-${Date.now()}`
    };
  } catch (error: any) {
    console.error('fal.ai API error:', error);
    throw new Error(`fal.ai API error: ${error.message || 'Unknown error'}`);
  }
}

// Generate music using the ACE-Step Audio Inpaint model via fal.ai
async function generateWithAudioInpaint(body: GenerateMusicRequest) {
  console.log('Generating with ACE-Step Audio Inpaint model via fal.ai');
  
  const input: Record<string, any> = {
    prompt: body.prompt!,
    duration: body.duration || 120,
    number_of_steps: body.number_of_steps || 27,
    scheduler: body.scheduler || 'euler',
    guidance_type: body.guidance_type || 'apg',
    granularity_scale: body.granularity_scale || 10,
    guidance_interval: body.guidance_interval || 0.5,
    guidance_interval_decay: body.guidance_interval_decay || 0,
    guidance_scale: body.guidance_scale || 15,
    minimum_guidance_scale: body.minimum_guidance_scale || 3,
  };
  
  if (body.tags) {
    input.tags = body.tags;
  }
  
  if (body.seed !== undefined) {
    input.seed = body.seed;
  }

  console.log('Sending request to fal.ai with input:', JSON.stringify(input));
  
  try {
    const result = await fal.subscribe('fal-ai/ace-step/audio-inpaint', {
      input: input,
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === 'IN_PROGRESS') {
          console.log('Generation in progress:', update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    const audioUrl = result.data?.audio_url?.url || result.data?.audio?.url;
    
    if (!audioUrl) {
      throw new Error('No valid audio URL found in fal.ai response');
    }

    return {
      audioUrl,
      generationId: result.requestId || `audio-inpaint-${Date.now()}`
    };
  } catch (error: any) {
    console.error('fal.ai API error:', error);
    throw new Error(`fal.ai API error: ${error.message || 'Unknown error'}`);
  }
}

// Function to save generated music to user's library
async function saveToUserLibrary(
  userId: string,
  audioUrl: string,
  prompt: string,
  model: string,
  tags?: string,
  lyrics?: string,
  generationId?: string
) {
  try {
    // Save the track info to the user's library in database
    // This would typically involve a database call
    
    // For demonstration, we're just logging it
    console.log(`Saving track to user ${userId}'s library:`, {
      audioUrl,
      prompt,
      model,
      tags,
      lyrics,
      generationId
    });
    
    // In a real implementation, we would make a database insert call here
    return true;
  } catch (error) {
    console.error('Error saving to library:', error);
    return false;
  }
}