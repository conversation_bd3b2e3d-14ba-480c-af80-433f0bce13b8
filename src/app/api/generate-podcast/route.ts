import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

import { getUserSubscription } from '@/lib/neo4j';
import { fal } from '@fal-ai/client';

// Configure Fal AI
fal.config({
  credentials: process.env.FAL_KEY
});

// Define interfaces for request data
interface PodcastRequestData {
  model: string;
  text: string;
  voice1?: string;
  voice2?: string;
  voice?: string;
}

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const body = await req.json() as PodcastRequestData;
    
    // Log the incoming request for debugging
    console.log('POST request received with model:', body.model);
    
    // Get user subscription
    const subscription = await getUserSubscription(userId);
    const userPlan = subscription?.type ?? 'free';
    
    // Validate required parameters
    if (!body.model) {
      return NextResponse.json(
        { success: false, error: 'Missing model parameter' },
        { status: 400 }
      );
    }
    
    if (!body.text) {
      return NextResponse.json(
        { success: false, error: 'Missing text parameter' },
        { status: 400 }
      );
    }
    
    // Handle PlayDialog generation
    if (body.model === 'replicate-play-dialog') {
      const audioUrl = await generateWithPlayDialog(body);
      return NextResponse.json({
        success: true,
        audioUrl: audioUrl,
        message: 'PlayDialog audio successfully generated'
      });
    }

    // Handle PlayDialog generation
    if (body.model === 'resemble-ai/chatterboxhd/text-to-speech') {
      const audioUrl = await generateWithChatterboxHD(body);
      return NextResponse.json({
        success: true,
        audioUrl: audioUrl,
        message: 'Chatterbox HD audio successfully generated'
      });
    }
    
    // Handle ElevenLabs generation
    if (body.model === 'elevenlabs-multilingual-v2') {
      const audioUrl = await generateWithElevenLabs(body);
      return NextResponse.json({
        success: true,
        audioUrl: audioUrl,
        message: 'ElevenLabs audio successfully generated'
      });
    }
    
    // If no valid model is provided
    return NextResponse.json(
      { success: false, error: 'Invalid model specified' },
      { status: 400 }
    );
    
  } catch (error: any) {
    console.error('Error in POST function:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}

// Function to generate audio with PlayDialog via Fal AI
async function generateWithPlayDialog(body: PodcastRequestData) {
  try {
    console.log('Submitting PlayDialog request to Fal AI:', body.text);

    const result = await fal.subscribe("fal-ai/playai/tts/dialog", {
      input: {
        input: body.text,
        voices: [
          {
            voice: body.voice1 ?? "Jennifer (English (US)/American)",
            turn_prefix: "Voice1: "
          },
          {
            voice: body.voice2 ?? "Furio (English (IT)/Italian)",
            turn_prefix: "Voice2: "
          }
        ],
        response_format: "url",
      },
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === "IN_PROGRESS") {
          update.logs.map((log) => log.message).forEach(console.log);
        }
      },
    });

    console.log('PlayDialog result:', result.data);
    console.log('PlayDialog requestId:', result.requestId);

    // Extract the audio URL from the result
    const audioUrl = result.data;
    if (!audioUrl) {
      throw new Error('No audio URL returned from PlayDialog');
    }

    return audioUrl;
  } catch (error: any) {
    console.error('Error generating audio with PlayDialog via Fal AI:', error);
    throw new Error(`Failed to generate audio: ${error.message}`);
  }
}

// Function to generate audio with ElevenLabs via Fal AI
async function generateWithElevenLabs(body: PodcastRequestData) {
  try {
    console.log('Submitting ElevenLabs request to Fal AI:', body.text);

    const result = await fal.subscribe("fal-ai/elevenlabs/tts/multilingual-v2", {
      input: {
        text: body.text,
        voice: body.voice ?? "Rachel",
      },
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === "IN_PROGRESS") {
          update.logs.map((log) => log.message).forEach(console.log);
        }
      },
    });

    console.log('ElevenLabs result:', result);
    console.log('ElevenLabs requestId:', result.requestId);

    // Extract the audio URL from the result
    const audioUrl = result;
    if (!audioUrl) {
      throw new Error('No audio URL returned from ElevenLabs');
    }

    return audioUrl;
  } catch (error: any) {
    console.error('Error generating audio with ElevenLabs via Fal AI:', error);
    throw new Error(`Failed to generate audio: ${error.message}`);
  }
}

// Function to generate audio with chatterbox via Fal AI
async function generateWithChatterboxHD(body: PodcastRequestData) {
  try {
    console.log('Submitting ElevenLabs request to Fal AI:', body.text);

    const result = await fal.subscribe("resemble-ai/chatterboxhd/text-to-speech", {
      input: {
        text: body.text,
        voice: body.voice ?? "Aurora",
      },
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === "IN_PROGRESS") {
          update.logs.map((log) => log.message).forEach(console.log);
        }
      },
    }); 
    console.log('Chatterbox HD result:', result.data);
    console.log('Chatterbox HD requestId:', result.requestId);

    // Extract the audio URL from the result
    const audioUrl = result.data;
    if (!audioUrl) {
      throw new Error('No audio URL returned from Chatterbox HD');
    }

    return audioUrl;
  } catch (error: any) {
    console.error('Error generating audio with Chatterbox HD via Fal AI:', error);
    throw new Error(`Failed to generate audio: ${error.message}`);
  }
}