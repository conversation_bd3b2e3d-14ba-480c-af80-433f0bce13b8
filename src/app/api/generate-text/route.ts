import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import togetherAI from '@/lib/together-ai';

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    const { prompt, speakerName } = body;
    
    if (!prompt) {
      return NextResponse.json(
        { error: 'Missing prompt' },
        { status: 400 }
      );
    }
    
    console.log(`Generating text for prompt of length ${prompt.length} using DeepSeek-R1-Distill-Llama-70B-free`);
    
    // Format the prompt for the LLM
    const formattedPrompt = `You are writing content for a podcast. 
Speaker: ${speakerName || 'Host'}
Task: ${prompt}

Write a natural-sounding script for the podcast speaker. Keep the tone conversational and engaging. 
Don't include any speaker labels or formatting, just the text the speaker would say.

Response:`;
    
    // Call Together AI API with the DeepSeek model
    const response = await togetherAI.sendMessageToDeepSeek(formattedPrompt, {
      max_tokens: 1000,
      temperature: 0.7
    });
    
    // Extract the generated text from the response
    const generatedText = response.message.trim();
    
    if (!generatedText) {
      throw new Error('No text was generated');
    }
    
    return NextResponse.json({
      generatedText,
      model: "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free"
    });
    
  } catch (error: any) {
    console.error('Error generating text:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to generate text' },
      { status: 500 }
    );
  }
} 