import { NextRequest, NextResponse } from 'next/server';
import RunwayML from '@runwayml/sdk';

// Initialize Runway client
const runwayClient = new RunwayML({
  apiKey: process.env.RUNWAYML_API_SECRET || '',
});

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { 
      prompt, 
      negative_prompt, 
      width, 
      height, 
      duration, 
      fps, 
      promptImage, 
      mode 
    } = body;

    console.log(`Starting video generation with Runway: ${mode}`);

    // Create a task based on mode
    let task;
    if (mode === 'image-to-video' && promptImage) {
      // Image to video task
      task = await runwayClient.imageToVideo.create({
        model: 'gen3a_turbo',
        promptImage: promptImage,
        promptText: prompt,
        // Negative prompt is handled differently or might not be supported
      });
    } else {
      // Text to video task - using the image-to-video endpoint with a placeholder image
      // The SDK requires promptImage even for text-to-video
      // In a real implementation, you'd use a default "blank" image or handle differently
      task = await runwayClient.imageToVideo.create({
        model: 'gen3a_turbo',
        promptImage: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==', // Tiny 1x1 transparent PNG
        promptText: prompt,
      });
    }

    const taskId = task.id;
    console.log(`Task created with ID: ${taskId}`);

    // Poll for task completion
    let result;
    try {
      result = await pollForCompletion(taskId);
    } catch (err) {
      console.error('Error polling for task completion:', err);
      throw new Error('Failed to complete video generation task');
    }

    // Extract video URL from the API response
    // Using any type to handle the unknown structure
    let videoUrl = '';
    
    // Type assertion to treat result as any type to avoid linter errors
    const resultAny = result as any;
    
    try {
      if (resultAny && resultAny.output) {
        if (Array.isArray(resultAny.output)) {
          videoUrl = resultAny.output[0] || '';
        } else if (typeof resultAny.output === 'object') {
          videoUrl = resultAny.output.url || resultAny.output.videoUrl || '';
        } else if (typeof resultAny.output === 'string') {
          videoUrl = resultAny.output;
        }
      } else if (resultAny && resultAny.url) {
        videoUrl = resultAny.url;
      }
      
      if (!videoUrl) {
        console.warn('Could not extract video URL from Runway API response:', resultAny);
      }
    } catch (e) {
      console.error('Error extracting video URL from response:', e);
    }

    // Return video URL in response
    return NextResponse.json({ 
      videoUrl: videoUrl,
      message: 'Video generated successfully with Runway'
    });

  } catch (error: any) {
    console.error('Error generating video with Runway:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to generate video with Runway' },
      { status: 500 }
    );
  }
}

// Helper function to poll for task completion
async function pollForCompletion(taskId: string) {
  // Initial poll delay
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  let task = await runwayClient.tasks.retrieve(taskId);
  let attempts = 0;
  const maxAttempts = 30; // 5 minutes max (10s * 30)
  
  while (!['SUCCEEDED', 'FAILED'].includes(task.status) && attempts < maxAttempts) {
    // Wait for ten seconds before polling again
    await new Promise(resolve => setTimeout(resolve, 10000));
    task = await runwayClient.tasks.retrieve(taskId);
    attempts++;
    
    console.log(`Polling attempt ${attempts}, status: ${task.status}`);
  }
  
  if (task.status === 'FAILED') {
    // Handle the error message safely
    throw new Error('Task failed: Check Runway logs for details');
  }
  
  if (attempts >= maxAttempts) {
    throw new Error('Task timed out after 5 minutes');
  }
  
  return task;
} 