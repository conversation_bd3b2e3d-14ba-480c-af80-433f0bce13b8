import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

// Initialize Replicate
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN ?? '',
});

// Model versions mapping
const MODEL_VERSIONS: Record<string, string> = {
  'luma/ray-2-720p': 'luma/ray-2-720p',
  'luma/ray-2-540p': 'luma/ray-2-540p',
  'luma/ray-flash-2-540p': 'luma/ray-flash-2-540p',
  'luma/ray-flash-2-720p': 'luma/ray-flash-2-720p',
  'wavespeedai/wan-2.1-t2v-720p': 'wavespeedai/wan-2.1-t2v-720p',
  'wavespeedai/wan-2.1-t2v-480p': 'wavespeedai/wan-2.1-t2v-480p',
  'wavespeedai/wan-2.1-i2v-720p': 'wavespeedai/wan-2.1-i2v-720p',
  'wavespeedai/wan-2.1-i2v-480p': 'wavespeedai/wan-2.1-i2v-480p',
  'kwaivgi/kling-v1.6-pro': 'kwaivgi/kling-v1.6-pro',
  'kwaivgi/kling-v1.6-standard': 'kwaivgi/kling-v1.6-standard',
  'google/veo-2': 'google/veo-2',
  'haiper-ai/haiper-video-2': 'haiper-ai/haiper-video-2'
};

export async function POST(req: NextRequest) {
  try {
    // In a production app, you would validate authentication and permissions here
    // For now, we'll skip this step to focus on the core functionality

    // Parse request body
    const body = await req.json();
    const { model, prompt, negative_prompt, width, height, duration, fps, start_image, image, mode } = body;

    console.log(`Starting video generation with model: ${model}`);

    // Get the model version from our mapping
    const modelVersion = MODEL_VERSIONS[model];
    if (!modelVersion) {
      throw new Error(`Model version not found for model: ${model}`);
    }

    // Prepare the input parameters
    const input: Record<string, any> = {
      prompt,
      negative_prompt,
    };

    // Add resolution parameters if provided
    if (width) input.width = width;
    if (height) input.height = height;

    // Add duration and fps parameters
    // Ensure minimum of 81 frames for WaveSpeed models
    const calculatedFrames = Math.max(81, Math.floor(duration * (fps ?? 30)));
    input.num_frames = calculatedFrames;
    if (fps) input.fps = fps;

    // Add image parameters for image-to-video mode
    if (mode === 'image-to-video') {
      if (start_image) {
        // Ensure the image is a valid URI
        input.start_image = start_image.startsWith('http') ? start_image : `data:image/jpeg;base64,${start_image}`;
      }
      if (image) {
        // Ensure the image is a valid URI
        input.image = image.startsWith('http') ? image : `data:image/jpeg;base64,${image}`;
      }
    }

    // Create the prediction
    const prediction = await replicate.predictions.create({
      version: modelVersion,
      input,
    });

    // Wait for prediction to complete
    let result;
    if (prediction.id) {
      result = await waitForPrediction(prediction.id);
    } else {
      throw new Error('Failed to create prediction');
    }

    // Return video URL in response
    return NextResponse.json({ 
      videoUrl: result.output, 
      message: 'Video generated successfully',
      preview: {
        type: 'video',
        url: result.output,
        width: width ?? 768,
        height: height ?? 432,
        aspectRatio: '16:9'
      }
    });

  } catch (error: any) {
    console.error('Error generating video:', error);
    return NextResponse.json(
      { error: error.message ?? 'Failed to generate video' },
      { status: 500 }
    );
  }
}

// Helper function to wait for prediction to complete
async function waitForPrediction(id: string) {
  let prediction = await replicate.predictions.get(id);
  
  // Poll until the prediction is complete
  while (prediction.status !== 'succeeded' && prediction.status !== 'failed') {
    await new Promise(resolve => setTimeout(resolve, 1000));
    prediction = await replicate.predictions.get(id);
  }
  
  if (prediction.status === 'failed') {
    throw new Error(prediction.error ? String(prediction.error) : 'Prediction failed with unknown error');
  }
  
  return prediction;
} 