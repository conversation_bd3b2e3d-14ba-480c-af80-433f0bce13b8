import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { checkAndIncrementUsage } from '@/lib/usage';
import { FeatureAccess } from '@/lib/permissions';
import { getUserSubscription } from '@/lib/neo4j';

// ElevenLabs API endpoint
const ELEVENLABS_API_ENDPOINT = 'https://api.elevenlabs.io/v1';
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY || '';

// Claude API endpoint
const CLAUDE_API_ENDPOINT = 'https://api.anthropic.com/v1/messages';
const CLAUDE_API_KEY = process.env.CLAUDE_API_KEY || '';

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user session' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { prompt, voiceId, useClaudeForEnhancement } = body;
    
    // Validate required parameters
    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Missing prompt parameter' },
        { status: 400 }
      );
    }
    
    if (!voiceId) {
      return NextResponse.json(
        { success: false, error: 'Missing voiceId parameter' },
        { status: 400 }
      );
    }
    
    // Get user subscription
    const subscription = await getUserSubscription(userId);
    const userPlan = subscription?.type || 'free';
    
    // Check access and usage limits for voice generation
    const accessCheck = await checkAndIncrementUsage(
      userId,
      userPlan,
      'voice_generation' as FeatureAccess
    );
    
    if (!accessCheck.allowed) {
      // Check if it's due to feature restriction or usage limit
      const reason = accessCheck.currentUsage > 0 
        ? "You've reached your daily limit for voice generation. Please upgrade your plan or try again tomorrow."
        : "Your current subscription plan doesn't include access to voice generation. Please upgrade to continue.";
        
      return NextResponse.json(
        { 
          success: false, 
          error: reason,
          subscription: {
            plan: userPlan,
            currentUsage: accessCheck.currentUsage,
          }
        },
        { status: 403 }
      );
    }
    
    // Process the prompt with Claude if enhancement is requested
    let finalText = prompt;
    if (useClaudeForEnhancement) {
      try {
        const claudeResponse = await fetch(CLAUDE_API_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': CLAUDE_API_KEY,
            'anthropic-version': '2023-06-01'
          },
          body: JSON.stringify({
            model: 'claude-3-sonnet-20240229',
            max_tokens: 1000,
            messages: [
              {
                role: 'user',
                content: `Enhance the following text to make it more natural and conversational for text-to-speech. 
                Keep the same meaning but make it sound more like natural speech with appropriate pauses, emphasis, 
                and conversational flow: "${prompt}"`
              }
            ]
          })
        });
        
        if (!claudeResponse.ok) {
          throw new Error(`Claude API error: ${await claudeResponse.text()}`);
        }
        
        const claudeData = await claudeResponse.json();
        finalText = claudeData.content[0].text;
      } catch (error: any) {
        console.error('Error enhancing text with Claude:', error);
        // Continue with original text if Claude enhancement fails
      }
    }
    
    // Map voice ID to ElevenLabs voice ID
    const elevenlabsVoiceId = mapVoiceIdToElevenLabsId(voiceId);
    
    // Prepare request data for ElevenLabs
    const requestData = {
      text: finalText,
      model_id: 'eleven_turbo_v2',
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.75
      }
    };
    
    // Make API request to ElevenLabs
    const response = await fetch(`${ELEVENLABS_API_ENDPOINT}/text-to-speech/${elevenlabsVoiceId}`, {
      method: 'POST',
      headers: {
        'xi-api-key': ELEVENLABS_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`ElevenLabs API error: ${errorText}`);
    }
    
    // Get audio data
    const audioBuffer = await response.arrayBuffer();
    const base64Audio = Buffer.from(audioBuffer).toString('base64');
    
    // Create a data URL for the audio
    const audioUrl = `data:audio/mp3;base64,${base64Audio}`;
    
    // Get updated usage after generation
    let usageRemaining = null;
    try {
      const { getUserDailyUsage } = await import('@/lib/usage');
      const { USAGE_LIMITS } = await import('@/lib/permissions');
      
      const currentUsage = await getUserDailyUsage(userId, 'voice_generation' as FeatureAccess);
      const planLimits = USAGE_LIMITS[userPlan as keyof typeof USAGE_LIMITS] || USAGE_LIMITS.free;
      const featureKey = 'voice_generation' as keyof typeof planLimits;
      const dailyLimit = planLimits[featureKey]?.daily || 0;
      
      usageRemaining = Math.max(0, dailyLimit - currentUsage);
    } catch (error) {
      console.error('Error getting updated usage:', error);
    }
    
    return NextResponse.json({
      success: true,
      audioUrl: audioUrl,
      enhancedText: finalText !== prompt ? finalText : undefined,
      usageRemaining
    });
    
  } catch (error: any) {
    console.error('Error generating voice:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to generate voice',
        details: error.toString()
      },
      { status: 500 }
    );
  }
}

// Helper function to map voice IDs to ElevenLabs voice IDs
function mapVoiceIdToElevenLabsId(voiceId: string): string {
  // This would be replaced with actual ElevenLabs voice IDs
  const voiceMap: Record<string, string> = {
    'v1': '21m00Tcm4TlvDq8ikWAM', // Adam
    'v2': 'EXAVITQu4vr4xnSDxMaL', // Rachel
    'v3': 'ODq5zmih8GrVes37Dizd', // James
    'v4': 'pNInz6obpgDQGcFmaJgB', // Sophia
    'v5': 'yoZ06aMxZJJ28mfd3POQ', // Matthew
    'v6': 'jBpfuIE2acCO8z3wKNLl', // Olivia
    'v7': 'g9S4tYJQQUq1LJKcVrrW', // Noah
    'v8': 'flq6f7yk4E4fJM5XTYuZ', // Ava
    'v9': 'XrExE9yKIg1WjnnlVkGX', // Kai
    'v10': 'SOYHLrjzK2X1ezoPC6cr', // Charlie
  };
  
  return voiceMap[voiceId] || '21m00Tcm4TlvDq8ikWAM'; // Default to Adam if not found
} 