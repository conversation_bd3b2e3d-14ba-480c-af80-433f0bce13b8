import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import Replicate from "replicate";
import { SubscriptionService } from '@/lib/services/subscription-service';

// Initialize API clients
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY ?? '',
});

// Cache for subscription validation results
const validationCache = new Map<string, {
  data: any;
  timestamp: number;
}>();

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 9);
  const requestTime = Date.now();
  
  try {
    // Validate user authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || !session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized or invalid session' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const userEmail = session.user.email;
    const shortUserId = userId.substring(0, 8);
    
    // Parse request body
    const body = await request.json();
    const { 
      model,
      provider = "Replicate",
      image,
      prompt = "",
      negative_prompt = "",
      feature 
    } = body;
    
    if (!model) {
      return NextResponse.json(
        { error: 'Missing required model parameter' },
        { status: 400 }
      );
    }

    if (!image) {
      return NextResponse.json(
        { error: 'Missing required image parameter' },
        { status: 400 }
      );
    }
    
    console.log(`[${requestId}] User ${shortUserId}... requesting image editing for model: ${model}`);
    
    // Check subscription status using cached data if available
    const cacheKey = `subscription:${userId}`;
    const cachedData = validationCache.get(cacheKey);
    const now = Date.now();
    
    let subscription;
    
    if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
      // Use cached data
      subscription = cachedData.data;
      console.log(`[${requestId}] Using cached subscription data for user ${shortUserId}`);
    } else {
      // Try Stripe validation first (faster and more reliable)
      subscription = await SubscriptionService.validateSubscriptionFromStripe(userEmail);
      
      // If no subscription in Stripe, check Neo4j as fallback
      if (!subscription) {
        subscription = await SubscriptionService.getUserSubscription(userId);
      }
      
      // Cache the result
      if (subscription) {
        validationCache.set(cacheKey, {
          data: subscription,
          timestamp: now
        });
      }
    }
    
    // Process with Replicate
    let editedImageUrl = '';
    
    if (provider === "Replicate") {
      // Handle different types of image editing models
      if (model === "cjwbw/rembg") {
        // Background removal - no prompt needed
        const result = await replicate.run(
          "cjwbw/rembg:fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003",
          {
            input: {
              image: image
            }
          }
        );
        
        // Handle the result based on its type
        if (typeof result === 'string') {
          editedImageUrl = result;
        } else if (Array.isArray(result) && result.length > 0) {
          editedImageUrl = result[0];
        } else if (result && typeof result === 'object' && 'output' in result) {
          const output = (result as any).output;
          editedImageUrl = Array.isArray(output) ? output[0] : output;
        }
      } else {
        // General image editing models
        const input: any = {
          image: image
        };
        
        // Add prompt if provided
        if (prompt) {
          input.prompt = prompt;
        }
        
        // Add negative prompt if provided
        if (negative_prompt) {
          input.negative_prompt = negative_prompt;
        }
        
        const result = await replicate.run(model, { input });
        
        // Handle different output formats
        if (Array.isArray(result)) {
          editedImageUrl = result[0];
        } else if (typeof result === 'string') {
          editedImageUrl = result;
        } else if (result && typeof result === 'object' && 'output' in result) {
          // Some models return { output: [url] } or { output: url }
          const output = (result as any).output;
          editedImageUrl = Array.isArray(output) ? output[0] : output;
        }
      }
    } else {
      return NextResponse.json(
        { error: `Unsupported provider: ${provider}` },
        { status: 400 }
      );
    }
    
    // Calculate and log total request time
    const totalTime = Date.now() - requestTime;
    console.log(`[${requestId}] Processed image in ${totalTime}ms for user ${shortUserId}...`);
    
    // Return the result
    return NextResponse.json({
      imageUrl: editedImageUrl,
      requestId,
      provider,
      processingTime: totalTime,
      subscription: subscription ? {
        type: subscription.type,
        status: subscription.status
      } : null
    });
    
  } catch (error: any) {
    console.error(`Error editing image:`, error);
    
    return NextResponse.json(
      { 
        error: error.message || 'Failed to edit image',
        requestId
      },
      { status: 500 }
    );
  }
} 