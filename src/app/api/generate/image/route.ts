import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { FeatureAccess } from '@/lib/permissions';
import { SubscriptionService } from '@/lib/services/subscription-service';
import Replicate from "replicate";
import Together from "together-ai";

// Initialize API clients
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY ?? '',
});

const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY,
});

// Cache for subscription validation results
const validationCache = new Map<string, {
  data: any;
  timestamp: number;
}>();

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 9);
  const requestTime = Date.now();
  
  try {
    // Validate user authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || !session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized or invalid session' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const userEmail = session.user.email;
    const shortUserId = userId.substring(0, 8);
    
    // Parse request body
    const body = await request.json();
    const { 
      model, 
      prompt, 
      negativePrompt = "", 
      width = 1024, 
      height = 1024, 
      seed,
      num_images = 1, 
      feature = "IMAGE_GENERATION"
    } = body;
    
    if (!model || !prompt) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    console.log(`[${requestId}] User ${shortUserId}... requesting image generation for model: ${model}`);
    
    // Check subscription status using cached data if available
    const cacheKey = `subscription:${userId}`;
    const cachedData = validationCache.get(cacheKey);
    const now = Date.now();
    
    let subscription;
    
    if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
      // Use cached data
      subscription = cachedData.data;
      console.log(`[${requestId}] Using cached subscription data for user ${shortUserId}`);
    } else {
      // Try Stripe validation first (faster and more reliable)
      subscription = await SubscriptionService.validateSubscriptionFromStripe(userEmail);
      
      // If no subscription in Stripe, check Neo4j as fallback
      if (!subscription) {
        subscription = await SubscriptionService.getUserSubscription(userId);
      }
      
      // Cache the result
      if (subscription) {
        validationCache.set(cacheKey, {
          data: subscription,
          timestamp: now
        });
      }
    }
    
    // Determine which provider to use
    let provider = body.provider || "Replicate";
    
    // Auto-detect provider based on model ID if not specified
    if (!body.provider) {
      if (model.includes('together-ai') || model.startsWith('black-forest-labs/FLUX')) {
        provider = "Together AI";
      }
    }
    
    console.log(`[${requestId}] Using provider: ${provider}`);
    
    // Generate the image with the appropriate provider
    let images: string[] = [];
    
    if (provider === "Replicate") {
      // Prepare input for Replicate
      const input = {
        prompt,
        negative_prompt: negativePrompt,
        width,
        height,
        num_outputs: num_images,
        ...(seed !== undefined ? { seed } : {})
      };
      
      // Call Replicate API
      const result = await replicate.run(model, { input });
      
      // Format the result with improved Replicate response handling
      console.log(`Processing Replicate result type:`, typeof result);
      console.log(`Raw Replicate result structure:`, JSON.stringify(result, null, 2).substring(0, 500));

      if (Array.isArray(result)) {
        images = result.filter(url => typeof url === 'string' && url.trim().length > 0);
      } else if (typeof result === 'string' && result.trim().length > 0) {
        images = [result];
      } else if (result && typeof result === 'object') {
        const resultObj = result as Record<string, any>;

        // Handle Replicate's most common response format first
        if (resultObj.output) {
          if (typeof resultObj.output === 'string' && resultObj.output.trim().length > 0) {
            images = [resultObj.output];
            console.log(`Found Replicate output string: ${resultObj.output.substring(0, 100)}...`);
          } else if (Array.isArray(resultObj.output)) {
            const validUrls = resultObj.output.filter(url => typeof url === 'string' && url.trim().length > 0);
            if (validUrls.length > 0) {
              images = validUrls;
              console.log(`Found Replicate output array with ${images.length} images`);
            }
          }
        }
      }

      console.log(`Final processed images: ${images.length} found`);
      if (images.length === 0) {
        console.log(`WARNING: No images extracted from Replicate response`);
        console.log(`Full raw response:`, JSON.stringify(result, null, 2));
      }
    } 
    else if (provider === "Together AI") {
      // Prepare input for Together AI
      const input: any = {
        model,
        prompt,
        negative_prompt: negativePrompt,
        width,
        height,
        n: num_images,
        response_format: 'url'
      };
      
      // Add seed if provided
      if (seed !== undefined) {
        input.seed = seed;
      }
      
      // Call Together AI API
      const response = await together.images.create(input);
      
      // Format the result
      images = response.data.map(item => item.url ?? '');
    }
    else {
      return NextResponse.json(
        { error: `Unsupported provider: ${provider}` },
        { status: 400 }
      );
    }
    
    // Calculate and log total request time
    const totalTime = Date.now() - requestTime;
    console.log(`[${requestId}] Generated ${images.length} images in ${totalTime}ms for user ${shortUserId}...`);
    
    // Return the result
    return NextResponse.json({
      images,
      requestId,
      provider,
      processingTime: totalTime,
      subscription: subscription ? {
        type: subscription.type,
        status: subscription.status
      } : null
    });
    
  } catch (error: any) {
    console.error(`Error generating image:`, error);
    
    return NextResponse.json(
      { 
        error: error.message || 'Failed to generate image',
        requestId
      },
      { status: 500 }
    );
  }
} 