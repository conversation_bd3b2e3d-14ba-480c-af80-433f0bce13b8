import { NextRequest, NextResponse } from 'next/server';
import Replicate from "replicate";
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { incrementUserUsage, getUserDailyUsage } from '@/lib/usage';
import { FeatureAccess, USAGE_LIMITS } from '@/lib/permissions';
import { SubscriptionService } from '@/lib/services/subscription-service';

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY ?? '',
});

// Cache for subscription validation results
const validationCache = new Map<string, {
  data: any;
  timestamp: number;
}>();

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 9);
  
  try {
    // Validate user authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || !session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const userEmail = session.user.email;
    
    // Parse request body
    const body = await request.json();
    const { model, prompt, negativePrompt, width, height, seed, samples, feature, input_images, image } = body;
    
    console.log(`[${requestId}] Replicate generation request for model: ${model}`);
    
    // Track usage
    if (feature) {
      await incrementUserUsage(userId, feature as FeatureAccess);
    }
    
    // Call Replicate API
    const input: any = {
      prompt: prompt,
      negative_prompt: negativePrompt || "",
      width: width || 1024,
      height: height || 1024,
      num_outputs: samples || 1,
      seed: seed || Math.floor(Math.random() * 1000000)
    };

    // Add image input support for image-to-image generation
    if (input_images && input_images.length > 0) {
      // For models that support multiple images, use input_images
      if (model.includes('gpt-image') || model.includes('openai')) {
        input.input_images = input_images;
      } else {
        // For most Replicate models, use the first image as 'image' parameter
        input.image = input_images[0];
      }
      console.log(`[${requestId}] Added image input for image-to-image generation`);
    } else if (image) {
      // Support single image parameter as well
      input.image = image;
      console.log(`[${requestId}] Added single image input for image-to-image generation`);
    }
    
    const result = await replicate.run(model, { input });
    console.log(`[${requestId}] Replicate API call successful`);
    
    // Log the raw result to better understand its structure
    console.log(`[${requestId}] Raw Replicate result:`, JSON.stringify(result).substring(0, 500) + '...');
    
    // Format result for frontend
    let images: string[] = [];
    
    // Handle different response formats from Replicate models
    if (Array.isArray(result)) {
      // Standard format where result is an array of image URLs
      images = result;
      console.log(`[${requestId}] Found array result with ${images.length} images`);
    } else if (typeof result === 'string') {
      // Some models return a single string URL
      images = [result];
      console.log(`[${requestId}] Found string result: ${(result as string).substring(0, 100)}...`);
    } else if (result && typeof result === 'object') {
      // Some models return an object with an output field (e.g., Ideogram v2)
      const resultObj = result as Record<string, any>;
      console.log(`[${requestId}] Result is an object with keys:`, Object.keys(resultObj));
      
      if (Array.isArray(resultObj.output)) {
        images = resultObj.output;
        console.log(`[${requestId}] Found output array with ${images.length} images`);
      } else if (typeof resultObj.output === 'string') {
        images = [resultObj.output];
        console.log(`[${requestId}] Found output string: ${resultObj.output.substring(0, 100)}...`);
      } else if (resultObj.images && Array.isArray(resultObj.images)) {
        images = resultObj.images;
        console.log(`[${requestId}] Found images array with ${images.length} images`);
      }
      
      // If we still don't have images, try a direct approach
      if (images.length === 0) {
        // Try to extract any URL-like string from the result
        const stringified = JSON.stringify(result);
        const urlMatches = stringified.match(/(https?:\/\/[^\s"']+)/g);
        if (urlMatches && urlMatches.length > 0) {
          console.log(`[${requestId}] Found URLs by regex extraction:`, urlMatches);
          images = urlMatches;
        }
      }
    }
    
    console.log(`[${requestId}] Final processed Replicate result:`, images);
    
    // Get updated usage stats
    let usage = null;
    if (feature) {
      // Get subscription from cache or fresh validation
      const cacheKey = `subscription:${userId}`;
      const cachedData = validationCache.get(cacheKey);
      const now = Date.now();
      
      let userPlan = 'free';
      
      if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
        // Use cached data
        userPlan = cachedData.data.type.toLowerCase();
      } else {
        // Try Stripe validation first (faster and more reliable)
        const subscription = await SubscriptionService.validateSubscriptionFromStripe(userEmail);
        
        if (subscription) {
          userPlan = subscription.type.toLowerCase();
          
          // Cache the result
          validationCache.set(cacheKey, {
            data: subscription,
            timestamp: now
          });
        } else {
          // Fallback to Neo4j only if needed
          const neoSubscription = await SubscriptionService.getUserSubscription(userId);
          if (neoSubscription) {
            userPlan = neoSubscription.type.toLowerCase();
            
            // Cache the result
            validationCache.set(cacheKey, {
              data: neoSubscription,
              timestamp: now
            });
          }
        }
      }
      
      const currentUsage = await getUserDailyUsage(userId, feature as FeatureAccess);
      const planLimits = USAGE_LIMITS[userPlan as keyof typeof USAGE_LIMITS] || USAGE_LIMITS.free;
      const featureKey = feature.toLowerCase() as keyof typeof planLimits;
      const dailyLimit = planLimits[featureKey]?.daily || 0;
      const remaining = Math.max(0, dailyLimit - currentUsage);
      
      usage = {
        current: currentUsage,
        limit: dailyLimit,
        remaining
      };
    }
    
    return NextResponse.json({
      images,
      requestId,
      usage
    });
    
  } catch (error: any) {
    console.error(`[${requestId}] Replicate API error:`, error);
    return NextResponse.json(
      { error: error.message || "Error generating image with Replicate" },
      { status: 500 }
    );
  }
} 