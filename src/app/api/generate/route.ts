import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { FeatureAccess, MODEL_ACCESS } from '@/lib/permissions';
import { incrementUserUsage, getUserDailyUsage } from '@/lib/usage';
import Replicate from "replicate";
import Together from "together-ai";
import { SubscriptionService } from '@/lib/services/subscription-service';

// Initialize API clients
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY ?? '',
});

const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY,
});

// Cache for subscription validation results
// This significantly reduces database load
const validationCache = new Map<string, {
  data: any;
  timestamp: number;
}>();

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 9);
  const requestTime = Date.now();
  
  try {
    // Validate user authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || !session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized or invalid session' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const userEmail = session.user.email;
    const shortUserId = userId.substring(0, 8);
    
    // Parse request body
    const body = await request.json();
    const { 
      model, 
      prompt, 
      negativePrompt = "", 
      width = 1024, 
      height = 1024, 
      seed, 
      samples = 1, 
      feature,
      skipUsageTracking = true, // New parameter to skip usage tracking
      input_images = [], // Array of image URLs for image-to-image generation
      openai_api_key // OpenAI API key for GPT image model
    } = body;
    
    if (!model || !prompt) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    console.log(`[${requestId}] User ${shortUserId}... requesting image generation for model: ${model}`);
    
    // 1. Determine if this is a free model
    const isFreeModel = model === 'black-forest-labs/FLUX.1-schnell-Free';
    
    // 2. Check if the user has access to this model (unless it's a free model)
    if (!isFreeModel) {
      // Check cache first for subscription data
      const cacheKey = `subscription:${userId}`;
      const cachedData = validationCache.get(cacheKey);
      const now = Date.now();
      
      let subscription;
      let subscriptionType = 'free';
      let hasFullAccess = false;
      
      if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
        // Use cached data
        subscription = cachedData.data;
        console.log(`[${requestId}] Using cached subscription data for user ${shortUserId}`);
      } else {
        // Try Stripe validation first (faster and more reliable)
        subscription = await SubscriptionService.validateSubscriptionFromStripe(userEmail);
        
        // If no subscription in Stripe, check Neo4j as fallback
        if (!subscription) {
          subscription = await SubscriptionService.getUserSubscription(userId);
        }
        
        // Cache the result
        if (subscription) {
          validationCache.set(cacheKey, {
            data: subscription,
            timestamp: now
          });
        }
      }
      
      // Determine subscription type and access level
      if (subscription) {
        subscriptionType = subscription.type;
        hasFullAccess = subscription.status === 'active' || subscription.status === 'trialing';
      }
      
      console.log(`[${requestId}] User ${shortUserId}... has ${subscriptionType} subscription, active: ${hasFullAccess}`);
      
      // If the user doesn't have full access, they can only use free models
      if (!hasFullAccess) {
        return NextResponse.json(
          { error: 'Your current subscription does not allow access to this model' },
          { status: 403 }
        );
      }
      
      // If it's a premium model, make sure the user has a premium subscription
      const isPremiumModel = MODEL_ACCESS.PREMIUM.includes(model) && 
                            !MODEL_ACCESS.ADVANCED.includes(model);
      
      if (isPremiumModel && subscriptionType.toLowerCase() !== 'premium') {
        return NextResponse.json(
          { error: 'This model requires a Premium subscription' },
          { status: 403 }
        );
      }
    }
    
    // 3. Track usage (always track, but only enforce limits for paid models)
    // Skip usage tracking if skipUsageTracking is true
    if (feature && !skipUsageTracking) {
      try {
        await incrementUserUsage(userId, feature as FeatureAccess);
      } catch (usageError) {
        console.warn(`[${requestId}] Failed to track usage, but continuing with image generation:`, usageError);
        // Continue with the request even if usage tracking fails
      }
    } else if (skipUsageTracking) {
      console.log(`[${requestId}] Skipping usage tracking as requested`);
    }
    
    // 4. Determine which provider to use
    let provider = body.provider;
    if (!provider) {
      // Auto-detect provider based on model ID
      if (model.includes('together-ai') || model.startsWith('black-forest-labs/FLUX')) {
        provider = "Together AI";
      } else {
        provider = "Replicate";
      }
    }
    
    console.log(`[${requestId}] Using provider: ${provider}`);
    
    // 5. Generate the image with the appropriate provider
    let images: string[] = [];
    
    // Variables for storing raw responses
    let rawReplicateResponse: any = null;
    
    if (provider === "Replicate") {
      // Prepare input for Replicate
      let input: any = {
        prompt,
        negative_prompt: negativePrompt,
        width,
        height,
        num_outputs: samples,
        ...(seed !== undefined ? { seed } : {})
      };

      console.log(`[${requestId}] 🔍 DEBUG: Prepared Replicate input:`, JSON.stringify(input, null, 2));
      
      // Special handling for GPT image model
      if (model === 'openai/gpt-image-1') {
        if (!openai_api_key) {
          return NextResponse.json(
            { error: 'OpenAI API key is required for GPT image model' },
            { status: 400 }
          );
        }
        input.openai_api_key = openai_api_key;
        
        // Add input images if provided
        if (input_images && input_images.length > 0) {
          input.input_images = input_images;
        }
      } else if (input_images && input_images.length > 0) {
        // For other models that support image input, add the first image
        input.image = input_images[0];
      }
      
      try {
        // Declare variables that might be used in different scopes
        let prediction: any = null;
        let result: any = null;
        
        // For Ideogram models, we need to use the prediction API instead of run
        if (model.includes('ideogram')) {
          console.log(`[${requestId}] Using prediction API for Ideogram model`);
          
          // Create prediction
          prediction = await replicate.predictions.create({
            version: model,
            input
          });
          
          console.log(`[${requestId}] Prediction created, id: ${prediction.id}`);
          
          // Poll for the result (with timeout)
          let finalPrediction = null;
          let attempts = 0;
          const maxAttempts = 30; // 30 attempts * 2 seconds = 60 seconds max wait
          
          while (!finalPrediction?.output && attempts < maxAttempts) {
            await new Promise(r => setTimeout(r, 2000)); // Wait 2 seconds between polls
            attempts++;
            console.log(`[${requestId}] Polling prediction (attempt ${attempts}/${maxAttempts})...`);
            finalPrediction = await replicate.predictions.get(prediction.id);
          }
          
          console.log(`[${requestId}] Prediction status: ${finalPrediction?.status}`);
          console.log(`[${requestId}] Raw prediction result:`, JSON.stringify(finalPrediction).substring(0, 500) + '...');
          
          if (finalPrediction?.output) {
            if (Array.isArray(finalPrediction.output)) {
              images = finalPrediction.output;
            } else if (typeof finalPrediction.output === 'string') {
              images = [finalPrediction.output];
            }
            console.log(`[${requestId}] Extracted ${images.length} image(s) from prediction output`);
          } else {
            console.warn(`[${requestId}] No output in prediction result`);
          }
          
          // Store raw response for debugging
          rawReplicateResponse = finalPrediction;
        } else {
          // Regular model - use run API
          console.log(`[${requestId}] 🔍 DEBUG: About to call replicate.run with:`);
          console.log(`[${requestId}] 🔍 DEBUG: model:`, model);
          console.log(`[${requestId}] 🔍 DEBUG: input:`, JSON.stringify(input, null, 2));
          console.log(`[${requestId}] 🔍 DEBUG: REPLICATE_API_KEY exists:`, !!process.env.REPLICATE_API_KEY);
          console.log(`[${requestId}] 🔍 DEBUG: REPLICATE_API_KEY length:`, process.env.REPLICATE_API_KEY?.length || 0);

          try {
            result = await replicate.run(model, { input });
            console.log(`[${requestId}] 🔍 DEBUG: replicate.run completed successfully`);
          } catch (replicateError: any) {
            console.error(`[${requestId}] ❌ ERROR: replicate.run failed:`, replicateError);
            console.error(`[${requestId}] ❌ ERROR: replicate.run error message:`, replicateError.message);
            console.error(`[${requestId}] ❌ ERROR: replicate.run error stack:`, replicateError.stack);
            throw replicateError;
          }

          // Log the raw result to better understand its structure
          console.log(`[${requestId}] Raw Replicate result:`, JSON.stringify(result).substring(0, 500) + '...');
          console.log(`[${requestId}] 🔍 DEBUG: result type:`, typeof result);
          console.log(`[${requestId}] 🔍 DEBUG: result is array:`, Array.isArray(result));
          console.log(`[${requestId}] 🔍 DEBUG: result keys:`, result && typeof result === 'object' ? Object.keys(result) : 'N/A');
          
          // Format the result based on different possible response formats
          console.log(`[${requestId}] Processing Replicate result type:`, typeof result);
          console.log(`[${requestId}] Raw Replicate result structure:`, JSON.stringify(result, null, 2).substring(0, 1000));

          if (Array.isArray(result)) {
            // Standard format where result is an array of image URLs
            images = result.filter(url => typeof url === 'string' && url.trim().length > 0);
            console.log(`[${requestId}] Found array result with ${images.length} images`);
          } else if (typeof result === 'string' && result.trim().length > 0) {
            // Some models return a single string URL
            images = [result];
            console.log(`[${requestId}] Found string result: ${(result as string).substring(0, 100)}...`);
          } else if (result && typeof result === 'object') {
            // Handle Replicate's specific response format - most common case
            const resultObj = result as Record<string, any>;
            console.log(`[${requestId}] Result is an object with keys:`, Object.keys(resultObj));

            // PRIORITY: Handle Replicate's most common response format first
            if (resultObj.output) {
              if (typeof resultObj.output === 'string' && resultObj.output.trim().length > 0) {
                images = [resultObj.output];
                console.log(`[${requestId}] Found Replicate output string: ${resultObj.output.substring(0, 100)}...`);
              } else if (Array.isArray(resultObj.output)) {
                const validUrls = resultObj.output.filter(url => typeof url === 'string' && url.trim().length > 0);
                if (validUrls.length > 0) {
                  images = validUrls;
                  console.log(`[${requestId}] Found Replicate output array with ${images.length} images`);
                }
              }
            }

            // If no images found in output, try other possible paths
            if (images.length === 0) {
              const possiblePaths = [
                'images',
                'data',
                'urls',
                'image_urls',
                'generated_images'
              ];

              for (const path of possiblePaths) {
                if (resultObj[path]) {
                  if (Array.isArray(resultObj[path])) {
                    const validUrls = resultObj[path].filter(url => typeof url === 'string' && url.trim().length > 0);
                    if (validUrls.length > 0) {
                      images = validUrls;
                      console.log(`[${requestId}] Found ${path} array with ${images.length} images`);
                      break;
                    }
                  } else if (typeof resultObj[path] === 'string' && resultObj[path].trim().length > 0) {
                    images = [resultObj[path]];
                    console.log(`[${requestId}] Found ${path} string: ${resultObj[path].substring(0, 100)}...`);
                    break;
                  }
                }
              }
            }

            // If still no images found, check for nested structures
            if (images.length === 0) {
              const nestedPossiblePaths = [
                'output',
                'images',
                'data',
                'urls',
                'image_urls',
                'generated_images'
              ];

              for (const key of Object.keys(resultObj)) {
                const value = resultObj[key];
                if (value && typeof value === 'object' && !Array.isArray(value)) {
                  // Check nested objects
                  for (const nestedPath of nestedPossiblePaths) {
                    if (value[nestedPath]) {
                      if (Array.isArray(value[nestedPath])) {
                        const validUrls = value[nestedPath].filter(url => typeof url === 'string' && url.trim().length > 0);
                        if (validUrls.length > 0) {
                          images = validUrls;
                          console.log(`[${requestId}] Found nested ${key}.${nestedPath} array with ${images.length} images`);
                          break;
                        }
                      } else if (typeof value[nestedPath] === 'string' && value[nestedPath].trim().length > 0) {
                        images = [value[nestedPath]];
                        console.log(`[${requestId}] Found nested ${key}.${nestedPath} string`);
                        break;
                      }
                    }
                  }
                  if (images.length > 0) break;
                }
              }
            }
          }
          
          // Store raw response for debugging
          rawReplicateResponse = result;
        }
        
        // If we still don't have images, try a direct approach with regex
        if (images.length === 0) {
          console.log(`[${requestId}] No images found with standard methods, trying regex extraction...`);
          // Try to extract any URL-like string from the result
          let stringifiedResult = '';
          
          if (model.includes('ideogram') && prediction?.id) {
            const latestPrediction = await replicate.predictions.get(prediction.id);
            stringifiedResult = JSON.stringify(latestPrediction);
          } else {
            stringifiedResult = JSON.stringify(result);
          }
          
          const urlMatches = stringifiedResult.match(/(https?:\/\/[^\s"']+\.(?:png|jpg|jpeg|webp))/g);
          if (urlMatches && urlMatches.length > 0) {
            console.log(`[${requestId}] Found URLs by regex extraction:`, urlMatches);
            images = urlMatches;
          }
        }
        
        // Log final results for debugging
        console.log(`[${requestId}] Final processed Replicate result: ${images.length} images found`);
        if (images.length > 0) {
          console.log(`[${requestId}] Extracted image URLs:`, images);
        } else {
          console.log(`[${requestId}] WARNING: No images extracted from Replicate response`);
          console.log(`[${requestId}] Full raw response for debugging:`, JSON.stringify(result, null, 2));
        }
      } catch (error: any) {
        console.error(`[${requestId}] Replicate API error:`, error);
        throw new Error(`Error calling Replicate API: ${error.message}`);
      }
    } 
    else if (provider === "Together AI") {
      // Prepare input for Together AI
      const input: any = {
        model,
        prompt,
        negative_prompt: negativePrompt,
        width,
        height,
        n: samples,
        response_format: 'url'
      };
      
      // Add seed if provided
      if (seed !== undefined) {
        input.seed = seed;
      }
      
      // Call Together AI API
      const response = await together.images.create(input);
      
      // Format the result
      images = response.data.map(item => item.url ?? '');
    }
    else {
      return NextResponse.json(
        { error: `Unsupported provider: ${provider}` },
        { status: 400 }
      );
    }
    
    // 6. Get updated usage information - skip if usage tracking is disabled
    let usage = null;
    if (feature && !skipUsageTracking) {
      try {
        const currentUsage = await getUserDailyUsage(userId, feature as FeatureAccess);
        
        // Get subscription type from cache or fresh validation
        const cacheKey = `subscription:${userId}`;
        const cachedData = validationCache.get(cacheKey);
        const now = Date.now();
        
        let subscriptionType = 'free';
        
        if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
          subscriptionType = cachedData.data.type.toLowerCase();
        } else {
          // Try Stripe validation first
          const subscription = await SubscriptionService.validateSubscriptionFromStripe(userEmail);
          if (subscription) {
            subscriptionType = subscription.type.toLowerCase();
          } else {
            // Fallback to Neo4j
            const neoSubscription = await SubscriptionService.getUserSubscription(userId);
            if (neoSubscription) {
              subscriptionType = neoSubscription.type.toLowerCase();
            }
          }
        }
        
        // Get appropriate limits based on subscription
        const { USAGE_LIMITS } = await import('@/lib/permissions');
        const planLimits = USAGE_LIMITS[subscriptionType as keyof typeof USAGE_LIMITS] || USAGE_LIMITS.free;
        const featureKey = feature.toLowerCase() as keyof typeof planLimits;
        const dailyLimit = planLimits[featureKey]?.daily || 0;
        
        usage = {
          current: currentUsage,
          limit: dailyLimit,
          remaining: Math.max(0, dailyLimit - currentUsage)
        };
      } catch (usageError) {
        console.warn(`[${requestId}] Failed to get usage information:`, usageError);
        // Continue without usage information
      }
    }
    
    // Calculate and log total request time
    const totalTime = Date.now() - requestTime;
    console.log(`[${requestId}] Generated ${images.length} images in ${totalTime}ms for user ${shortUserId}...`);
    
    // Return the result with additional debug info for Replicate provider
    if (provider === "Replicate") {
      // Always include raw response data for Replicate to help with debugging in the UI
      const replicateResponse = {
        images,
        requestId,
        provider,
        usage,
        processingTime: totalTime,
        raw_response: rawReplicateResponse,
        debug_info: {
          model,
          empty_result: images.length === 0,
          timestamp: new Date().toISOString()
        }
      };
      
      console.log(`[${requestId}] Returning Replicate response with ${images.length} images`);
      if (images.length === 0) {
        console.log(`[${requestId}] CRITICAL: Replicate response contains 0 images - this indicates a parsing bug`);
        console.log(`[${requestId}] Raw response structure:`, JSON.stringify(rawReplicateResponse, null, 2).substring(0, 2000));
      }
      return NextResponse.json(replicateResponse);
    } else {
      // Standard response for other providers
      return NextResponse.json({
        images,
        requestId,
        provider,
        usage,
        processingTime: totalTime
      });
    }
    
  } catch (error: any) {
    console.error(`[${requestId}] Error generating image:`, error);
    
    return NextResponse.json(
      { 
        error: error.message || 'Failed to generate image',
        requestId
      },
      { status: 500 }
    );
  }
}