import { NextResponse } from 'next/server';
import { initializeSchema } from '@/lib/neo4j';

export async function GET() {
  try {
    await initializeSchema();
    
    return NextResponse.json({
      success: true,
      message: 'Database schema initialized successfully'
    });
  } catch (error) {
    console.error('Failed to initialize database schema:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to initialize database schema' },
      { status: 500 }
    );
  }
} 