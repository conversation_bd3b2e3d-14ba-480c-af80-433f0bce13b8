import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { exec } from 'child_process';
import util from 'util';
import ffmpegStatic from 'ffmpeg-static';
import { put, list, del } from '@vercel/blob';
// @ts-ignore
import audioConcat from 'audioconcat';

// Convert exec to Promise-based
const execPromise = util.promisify(exec);

// Interface for audio segment
interface AudioSegment {
  audioUrl: string;
  text: string;
  voiceId: string;
}

// Interface for merge request data
interface MergeRequestData {
  segments: AudioSegment[];
  title: string;
  includeMusic: boolean;
  includeSoundEffects: boolean;
  returnBlob?: boolean; // Option to return binary audio directly instead of URL
}

/**
 * Helper function to convert a data URL to a file
 */
async function dataUrlToFile(dataUrl: string, tempDir: string, index: number): Promise<string> {
  // Extract base64 data from data URL
  const matches = dataUrl.match(/^data:(.+);base64,(.+)$/);
  if (!matches || matches.length !== 3) {
    throw new Error('Invalid data URL format');
  }
  
  const buffer = Buffer.from(matches[2], 'base64');
  const filePath = path.join(tempDir, `segment-${index}.mp3`);
  await fs.writeFile(filePath, buffer);
  return filePath;
}

/**
 * Helper function to download audio from URL to a file
 */
async function downloadAudioToFile(url: string, tempDir: string, index: number): Promise<string> {
  if (url.startsWith('data:')) {
    return dataUrlToFile(url, tempDir, index);
  }
  
  try {
    console.log(`Fetching audio from URL: ${url.substring(0, 100)}...`);
    
    // For regular URLs, fetch and save to file
    const response = await fetch(url, {
      headers: {
        // Add headers that might be needed for certain URLs
        'Accept': 'audio/mpeg,audio/*',
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch audio file: ${response.status} ${response.statusText}`);
    }
    
    // Get content type for debugging
    const contentType = response.headers.get('content-type') || 'unknown';
    console.log(`Received audio with content type: ${contentType}`);
    
    // Get the buffer
    const buffer = await response.arrayBuffer();
    console.log(`Downloaded audio segment ${index}: ${buffer.byteLength} bytes`);
    
    if (buffer.byteLength === 0) {
      throw new Error('Received empty audio buffer');
    }
    
    // Write to file
    const filePath = path.join(tempDir, `segment-${index}.mp3`);
    await fs.writeFile(filePath, Buffer.from(buffer));
    console.log(`Wrote audio segment ${index} to file: ${filePath}`);
    
    return filePath;
  } catch (error) {
    console.error(`Error downloading audio from URL (${url.substring(0, 30)}...):`, error);
    throw error;
  }
}

/**
 * Helper function to merge audio files using audioconcat library
 */
async function mergeAudioFiles(inputPaths: string[], outputPath: string): Promise<void> {
  try {
    // Create a promise wrapper for audioconcat
    return new Promise((resolve, reject) => {
      // Configure audioconcat with the FFmpeg path
      const audioConcatOptions = {
        ffmpeg: ffmpegStatic || 'ffmpeg',
        log: console.log, // Use console.log for logging
        output: outputPath, // Set the output file path
        sampleRate: 44100, // Standard sample rate
        channels: 2, // Stereo output
        bitrate: 192, // Good quality bitrate
      };
      
      // Use audioconcat to merge the audio files
      audioConcat(inputPaths)
        .concat(outputPath)
        .on('start', (command: string) => {
          console.log('Started audioconcat with command:', command);
        })
        .on('error', (error: Error, stdout: string, stderr: string) => {
          console.error('Error in audioconcat:', error);
          console.error('FFmpeg stderr:', stderr);
          reject(new Error(`Failed to merge audio files: ${error.message}`));
        })
        .on('end', () => {
          console.log(`Successfully merged ${inputPaths.length} audio files to ${outputPath}`);
          resolve();
        });
    });
  } catch (error) {
    console.error('Error merging audio files:', error);
    throw new Error('Failed to merge audio files');
  }
}

/**
 * Clean up old podcast files from Blob storage (optional maintenance)
 */
async function cleanupOldBlobFiles() {
  try {
    // List all blobs in the podcast folder
    const { blobs } = await list({ prefix: 'podcasts/' });
    
    // Get current time
    const now = Date.now();
    const ONE_DAY_MS = 24 * 60 * 60 * 1000;
    
    // Delete blobs older than 1 day
    for (const blob of blobs) {
      if (blob.uploadedAt && (now - new Date(blob.uploadedAt).getTime() > ONE_DAY_MS)) {
        await del(blob.url);
        console.log(`Deleted old blob: ${blob.url}`);
      }
    }
  } catch (error) {
    console.error('Error cleaning up old blob files:', error);
    // Don't throw, just log the error
  }
}

export async function POST(req: NextRequest) {
  // Create temporary directory for audio files
  let tempDir = '';
  const segmentFiles: string[] = [];
  const blobUrls: string[] = [];
  
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await req.json() as MergeRequestData;
    
    // Validate segments
    if (!body.segments || body.segments.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No audio segments provided' },
        { status: 400 }
      );
    }
    
    // Create temporary directory for audio files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'podcast-'));
    
    console.log('Starting audio merge process for', body.segments.length, 'segments');
    
    // Clean up old podcast files occasionally
    cleanupOldBlobFiles().catch(console.error);
    
    // Download all audio segments to temporary files
    for (let i = 0; i < body.segments.length; i++) {
      const segment = body.segments[i];
      if (!segment.audioUrl) {
        console.warn(`Segment ${i} has no audio URL, skipping`);
        continue;
      }
      
      try {
        const filePath = await downloadAudioToFile(segment.audioUrl, tempDir, i);
        segmentFiles.push(filePath);
        console.log(`Downloaded segment ${i} to ${filePath}`);
        
        // Only upload to blob storage if we're not returning the blob directly
        if (!body.returnBlob) {
          // Upload each segment to Vercel Blob for temporary storage
          const fileContent = await fs.readFile(filePath);
          const speakerName = segment.text.substring(0, 20).replace(/[^a-zA-Z0-9]/g, '_');
          const blob = await put(`podcasts/segment-${i}-${speakerName}.mp3`, fileContent, {
            access: 'public',
            contentType: 'audio/mpeg',
            addRandomSuffix: true
          });
          
          blobUrls.push(blob.url);
          console.log(`Uploaded segment ${i} to Blob storage: ${blob.url}`);
        }
      } catch (error) {
        console.error(`Error processing segment ${i}:`, error);
        // Continue with other segments
      }
    }
    
    if (segmentFiles.length === 0) {
      throw new Error('Failed to process any audio segments');
    }
    
    // Add background music if requested (simplified implementation for now)
    if (body.includeMusic) {
      console.log('Background music requested, but implementation is pending');
      // In a full implementation, you would add background music tracks here
    }
    
    // Merge all audio files using audioconcat
    const outputPath = path.join(tempDir, 'merged.mp3');
    await mergeAudioFiles(segmentFiles, outputPath);
    
    // Read the merged file
    const mergedBuffer = await fs.readFile(outputPath);
    
    // If client wants the blob directly, return it
    if (body.returnBlob) {
      console.log('Returning merged audio as blob directly');
      
      return new NextResponse(mergedBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'audio/mpeg',
          'Content-Disposition': `attachment; filename="${body.title.replace(/[^a-zA-Z0-9]/g, '_')}_merged.mp3"`,
        },
      });
    }
    
    // Otherwise, upload to blob storage and return the URL (existing behavior)
    const podcastTitle = body.title.substring(0, 30).replace(/[^a-zA-Z0-9]/g, '_') || 'podcast';
    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    const mergedBlob = await put(`podcasts/${podcastTitle}_${timestamp}.mp3`, mergedBuffer, {
      access: 'public',
      contentType: 'audio/mpeg',
      addRandomSuffix: false
    });
    
    console.log(`Uploaded merged podcast to: ${mergedBlob.url}`);
    
    return NextResponse.json({
      success: true,
      audioUrl: mergedBlob.url,
      duration: segmentFiles.length * 30, // Approximate duration in seconds
      segmentUrls: blobUrls, // Include individual segment URLs for reference
      totalLength: mergedBuffer.length // Add the total size of the merged file
    });
    
  } catch (error: any) {
    console.error('Error merging audio:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to merge audio segments',
        details: error.toString()
      },
      { status: 500 }
    );
  } finally {
    // Clean up all temporary files
    if (tempDir) {
      try {
        for (const file of segmentFiles) {
          await fs.unlink(file).catch(() => {});
        }
        
        // Remove the temporary directory
        await fs.rm(tempDir, { recursive: true, force: true }).catch(() => {});
      } catch (cleanupError) {
        console.error('Error cleaning up temporary files:', cleanupError);
      }
    }
  }
} 