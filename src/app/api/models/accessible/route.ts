import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { checkImageGenerationAccess } from '@/lib/neo4j';
import { FeatureAccess, MODEL_ACCESS } from '@/lib/permissions';

/**
 * API endpoint that returns all models a user has access to in a single request
 */
export async function GET(request: NextRequest) {
  const requestStartTime = Date.now();
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid user session' },
        { status: 400 }
      );
    }
    
    // Check subscription access level
    const { subscriptionType, hasFullAccess } = await checkImageGenerationAccess(userId);
    
    const shortUserId = userId.substring(0, 8);
    console.log(`User ${shortUserId}... subscription: ${subscriptionType}, full access: ${hasFullAccess}`);
    
    // Prepare the response object
    const accessibleModels: string[] = [];
    const modelAccess: Record<string, string> = {};
    
    // Free models are accessible to all users
    const freeModels = [
      'black-forest-labs/FLUX.1-schnell-Free',
      'google/upscaler'
    ];
    
    // Add free models
    freeModels.forEach(model => {
      accessibleModels.push(model);
      modelAccess[model] = 'free';
    });
    
    // Standard & Premium subscribers get access to advanced models
    if (hasFullAccess) {
      MODEL_ACCESS.ADVANCED.forEach(model => {
        if (!accessibleModels.includes(model)) {
          accessibleModels.push(model);
          modelAccess[model] = 'standard';
        }
      });
      
      // Only Premium subscribers get access to premium models
      if (subscriptionType.toLowerCase() === 'premium') {
        MODEL_ACCESS.PREMIUM.forEach(model => {
          if (!accessibleModels.includes(model)) {
            accessibleModels.push(model);
            modelAccess[model] = 'premium';
          }
        });
      }
    }
    
    const responseTime = Date.now() - requestStartTime;
    console.log(`User ${shortUserId}... has access to ${accessibleModels.length} models (response time: ${responseTime}ms)`);
    
    return NextResponse.json({
      subscriptionType,
      hasFullAccess,
      accessibleModels,
      modelAccess
    });
    
  } catch (error) {
    console.error('Error getting accessible models:', error);
    return NextResponse.json(
      { error: 'Failed to get accessible models' },
      { status: 500 }
    );
  }
} 