import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { stripe } from '@/lib/stripe/stripe';

// Standard and Premium price IDs from environment variables
const STANDARD_PRICE_ID = process.env.STRIPE_STANDARD_PRICE_ID || '';
const PREMIUM_PRICE_ID = process.env.STRIPE_PREMIUM_PRICE_ID || '';

export async function POST(request: NextRequest) {
  try {
    // Verify that Stripe is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not set');
      return NextResponse.json(
        { error: 'Payment service is not configured' },
        { status: 500 }
      );
    }
    
    // Get session data to check if user is authenticated
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'You must be logged in to create a checkout session' },
        { status: 401 }
      );
    }
    
    // Parse request data
    const data = await request.json();
    const { priceId, planType } = data;
    
    // Validate that price ID is provided
    if (!priceId && !planType) {
      return NextResponse.json(
        { error: 'Price ID or plan type is required' },
        { status: 400 }
      );
    }
    
    // Determine the price ID to use
    let usePriceId = priceId;
    
    // If no price ID provided but plan type is provided, use the plan type to determine price ID
    if (!usePriceId && planType) {
      if (planType === 'Standard') {
        usePriceId = STANDARD_PRICE_ID;
      } else if (planType === 'Premium') {
        usePriceId = PREMIUM_PRICE_ID;
      } else {
        return NextResponse.json(
          { error: 'Invalid plan type' },
          { status: 400 }
        );
      }
    }
    
    if (!usePriceId) {
      return NextResponse.json(
        { error: 'Price ID for the selected plan is not configured' },
        { status: 500 }
      );
    }
    
    // Determine plan type from price ID if not provided
    let usePlanType = planType;
    if (!usePlanType) {
      if (usePriceId === STANDARD_PRICE_ID) {
        usePlanType = 'Standard';
      } else if (usePriceId === PREMIUM_PRICE_ID) {
        usePlanType = 'Premium';
      } else {
        // Default to Standard if unknown price ID
        usePlanType = 'Standard';
      }
    }
    
    console.log(`Creating checkout session with price ID: ${usePriceId} for plan: ${usePlanType}`);
    
    // Create checkout session
    try {
      const checkoutSession = await stripe.checkout.sessions.create({
        line_items: [
          {
            price: usePriceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        allow_promotion_codes: true,
        success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success`,
        cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/subscription?canceled=true`,
        metadata: {
          userId: session.user.id,
          planType: usePlanType,
          isGuestCheckout: 'false'
        },
      });
      
      return NextResponse.json({ url: checkoutSession.url });
    } catch (stripeError: any) {
      console.error('Stripe error:', stripeError);
      return NextResponse.json(
        { error: stripeError.message || 'Error creating checkout session' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in checkout API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 