import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { stripe } from '@/lib/stripe/stripe';

export async function POST(request: NextRequest) {
  try {
    // Get the session to ensure user is authenticated
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized - user email required' },
        { status: 401 }
      );
    }

    const { customerId, returnUrl } = await request.json();
    let stripeCustomerId = customerId;

    // If no customer ID provided, try to find customer by email
    if (!stripeCustomerId) {
      console.log('No customer ID provided, searching by email:', session.user.email);

      try {
        const customers = await stripe.customers.list({
          email: session.user.email,
          limit: 1,
        });

        if (customers.data.length > 0) {
          stripeCustomerId = customers.data[0].id;
          console.log('Found existing customer:', stripeCustomerId);
        } else {
          return NextResponse.json(
            { error: 'No Stripe customer found. Please ensure you have an active subscription.' },
            { status: 404 }
          );
        }
      } catch (searchError) {
        console.error('Error searching for customer:', searchError);
        return NextResponse.json(
          { error: 'Failed to find customer account' },
          { status: 500 }
        );
      }
    }

    console.log('Creating portal session for customer:', stripeCustomerId);

    // Create a portal session
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: stripeCustomerId,
      return_url: returnUrl ?? `${process.env.NEXTAUTH_URL}/dashboard/subscription`,
    });

    console.log('Portal session created successfully:', portalSession.id);
    return NextResponse.json({ url: portalSession.url });
  } catch (error) {
    console.error('Error creating portal session:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('No such customer')) {
        return NextResponse.json(
          { error: 'Customer not found in Stripe. Please contact support.' },
          { status: 404 }
        );
      }
      if (error.message.includes('billing portal')) {
        return NextResponse.json(
          { error: 'Billing portal is not configured. Please contact support.' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to create portal session. Please try again or contact support.' },
      { status: 500 }
    );
  }
}