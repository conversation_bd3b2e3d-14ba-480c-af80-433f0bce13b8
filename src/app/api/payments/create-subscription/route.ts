import { NextResponse } from 'next/server';
import { executeQuery } from '@/lib/neo4j';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getStripe, createSubscription } from '@/lib/stripe';
import { SUBSCRIPTION_TYPES } from '@/lib/stripe-client';

export async function POST(request: Request) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { planType, paymentMethodId } = await request.json();
    
    if (!paymentMethodId) {
      return NextResponse.json({ error: 'Payment method is required' }, { status: 400 });
    }
    
    // Get user from database to ensure they exist
    const userQuery = `
      MATCH (u:User {id: $userId})
      RETURN u
    `;
    const userResult = await executeQuery(userQuery, { userId: session.user.id });
    
    if (userResult.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    const user = userResult[0].toObject();
    
    // Determine price ID based on plan type
    let priceId: string;
    if (planType === SUBSCRIPTION_TYPES.STANDARD) {
      priceId = process.env.STRIPE_STANDARD_PRICE_ID!; // Using non-null assertion operator
    } else if (planType === SUBSCRIPTION_TYPES.PREMIUM) {
      priceId = process.env.STRIPE_PREMIUM_PRICE_ID!; // Using non-null assertion operator
    } else {
      return NextResponse.json({ error: 'Invalid plan type' }, { status: 400 });
    }
    
    // Make sure priceId is defined
    if (!priceId) {
      return NextResponse.json({ error: 'Subscription price ID not configured' }, { status: 500 });
    }
    
    // Check if customer already exists in Stripe
    let customerId = user.stripeCustomerId;
    
    if (!customerId) {
      // Create a new customer in Stripe
      const stripe = await getStripe();
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user.id
        }
      });
      
      customerId = customer.id;
      
      // Store Stripe customer ID in Neo4j
      const updateQuery = `
        MATCH (u:User {id: $userId})
        SET u.stripeCustomerId = $customerId
        RETURN u
      `;
      await executeQuery(updateQuery, { 
        userId: user.id, 
        customerId 
      });
    }
    
    // Create a subscription with the payment method
    const subscription = await createSubscription(
      customerId,
      priceId,
      paymentMethodId,
      {
        userId: user.id,
        planType: planType
      }
    );
    
    // The subscription might need confirmation if using 3D Secure
    const invoice = subscription.latest_invoice as any;
    const paymentIntent = invoice?.payment_intent;
    
    // Store initial subscription info in Neo4j
    // This will be updated to 'active' when webhook confirms payment
    await executeQuery(`
      MATCH (u:User {id: $userId})
      MERGE (u)-[:HAS_SUBSCRIPTION]->(s:Subscription)
      SET s = $subscriptionData
      RETURN s
    `, { 
      userId: user.id, 
      subscriptionData: {
        type: planType,
        status: subscription.status,
        startDate: new Date().toISOString(),
        stripeCustomerId: customerId,
        stripeSubscriptionId: subscription.id,
        updatedAt: new Date().toISOString()
      }
    });
    
    // Return the client secret if additional authentication is required
    if (paymentIntent && paymentIntent.status === 'requires_action') {
      return NextResponse.json({
        subscriptionId: subscription.id,
        clientSecret: paymentIntent.client_secret,
        requiresAction: true
      });
    }
    
    // Subscription created successfully without requiring additional action
    return NextResponse.json({
      subscriptionId: subscription.id,
      status: subscription.status,
      requiresAction: false
    });
    
  } catch (error) {
    console.error('Error creating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    );
  }
} 