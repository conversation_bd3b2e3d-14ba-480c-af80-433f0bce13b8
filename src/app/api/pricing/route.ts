import { NextResponse } from 'next/server';

// Standard and Premium price IDs from environment variables
const STANDARD_PRICE_ID = process.env.STRIPE_STANDARD_PRICE_ID ?? '';
const PREMIUM_PRICE_ID = process.env.STRIPE_PREMIUM_PRICE_ID ?? '';

export async function GET() {
  try {
    // Return the price IDs from server environment variables
    return NextResponse.json({
      standard: STANDARD_PRICE_ID,
      premium: PREMIUM_PRICE_ID,
    });
  } catch (error) {
    console.error('Error fetching price IDs:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve pricing information' },
      { status: 500 }
    );
  }
} 