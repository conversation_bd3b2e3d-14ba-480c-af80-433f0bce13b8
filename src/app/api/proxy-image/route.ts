import { NextRequest, NextResponse } from "next/server";

/**
 * Proxy endpoint to fetch images and avoid CORS issues
 * This allows the client to fetch images from external domains through our server
 */
export async function POST(req: NextRequest) {
  try {
    // Get the image URL from the request
    const { imageUrl } = await req.json();
    
    if (!imageUrl) {
      return NextResponse.json({ error: "Missing image URL" }, { status: 400 });
    }
    
    console.log(`Proxy fetching image from: ${imageUrl}`);
    
    // Fetch the image
    const response = await fetch(imageUrl, {
      headers: {
        // Add headers to improve compatibility
        "User-Agent": "AstroStudio/1.0",
        "Accept": "image/*"
      }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to fetch image: ${response.status}` }, 
        { status: response.status }
      );
    }
    
    // Get the image data as blob
    const imageBlob = await response.blob();
    
    if (!imageBlob || imageBlob.size === 0) {
      return NextResponse.json({ error: "Empty image data" }, { status: 400 });
    }
    
    console.log(`Successfully proxied image: ${imageBlob.type}, ${imageBlob.size} bytes`);
    
    // Return the image with appropriate content type
    return new NextResponse(imageBlob, {
      headers: {
        "Content-Type": imageBlob.type,
        "Cache-Control": "public, max-age=86400" // Cache for 24 hours
      }
    });
  } catch (error) {
    console.error("Error in proxy-image:", error);
    return NextResponse.json(
      { error: "Failed to proxy image" }, 
      { status: 500 }
    );
  }
} 