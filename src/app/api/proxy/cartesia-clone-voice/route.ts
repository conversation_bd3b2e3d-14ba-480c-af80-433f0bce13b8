import { NextResponse } from 'next/server';
import { CartesiaClient } from "@cartesia/cartesia-js";

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const voiceName = formData.get('name') as string;
    const languageInput = formData.get('language') as string || 'en';
    const description = formData.get('description') as string || 'Cloned voice using Cartesia AI';
    
    if (!audioFile || !voiceName) {
      return NextResponse.json(
        { error: 'Audio file and voice name are required' },
        { status: 400 }
      );
    }
    
    // Initialize Cartesia client with API key
    const client = new CartesiaClient({ 
      apiKey: process.env.CARTESIA_API_KEY || 'sk_car_r_mFWwQdKXqz06hgyV5D4'
    });
    
    // Convert File to ArrayBuffer for buffer creation
    const arrayBuffer = await audioFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Create a readable stream from the buffer
    const { Readable } = require('stream');
    const readableStream = new Readable();
    readableStream.push(buffer);
    readableStream.push(null); // Signal the end of the stream
    
    // Map input language to supported language type
    // Using 'en' as the default language if not specified or not supported
    const language = (languageInput === 'en' || languageInput === 'es' || 
                      languageInput === 'fr' || languageInput === 'de' || 
                      languageInput === 'it' || languageInput === 'pt' || 
                      languageInput === 'pl' || languageInput === 'hi' || 
                      languageInput === 'ja' || languageInput === 'ko' || 
                      languageInput === 'zh') ? languageInput : 'en';
    
    // Clone voice using Cartesia API
    const clonedVoice = await client.voices.clone(readableStream, {
      name: voiceName,
      description: description,
      mode: "stability",
      language: language as any, // Using type assertion to bypass TypeScript check
      enhance: true
    });
    
    return NextResponse.json({
      success: true,
      voiceId: clonedVoice.id,
      name: clonedVoice.name
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('Error cloning voice with Cartesia:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to clone voice' },
      { status: 500 }
    );
  }
} 