import { NextResponse } from 'next/server';
import { CartesiaClient } from "@cartesia/cartesia-js";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { voiceId, text } = body;
    
    if (!voiceId || !text) {
      return NextResponse.json(
        { error: 'Voice ID and text are required' },
        { status: 400 }
      );
    }
    
    // Initialize Cartesia client with API key
    const client = new CartesiaClient({ 
      apiKey: process.env.CARTESIA_API_KEY || 'sk_car_r_mFWwQdKXqz06hgyV5D4'
    });
    
    // Make a direct API request to Cartesia TTS API
    const response = await fetch('https://api.cartesia.ai/tts', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.CARTESIA_API_KEY || 'sk_car_r_mFWwQdKXqz06hgyV5D4'}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text,
        voice_id: voiceId,
        output_format: "mp3"
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to generate speech');
    }
    
    // Get the audio data as ArrayBuffer
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Convert to base64 for frontend playback
    const base64Audio = buffer.toString('base64');
    const audioUrl = `data:audio/mpeg;base64,${base64Audio}`;
    
    return NextResponse.json({
      success: true,
      audioUrl
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('Error converting text to speech with Cartesia:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to convert text to speech' },
      { status: 500 }
    );
  }
} 