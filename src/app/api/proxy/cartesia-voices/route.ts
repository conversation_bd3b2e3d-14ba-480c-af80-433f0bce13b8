import { NextResponse } from 'next/server';
import { CartesiaClient } from "@cartesia/cartesia-js";

export async function GET() {
  try {
    // Initialize Cartesia client with API key
    const client = new CartesiaClient({ 
      apiKey: process.env.CARTESIA_API_KEY || 'sk_car_r_mFWwQdKXqz06hgyV5D4'
    });
    
    // Make a direct API request to fetch voices
    const response = await fetch('https://api.cartesia.ai/voices', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.CARTESIA_API_KEY || 'sk_car_r_mFWwQdKXqz06hgyV5D4'}`,
      },
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch voices');
    }
    
    const voices = await response.json();
    
    return NextResponse.json({
      success: true,
      voices: voices
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('Error fetching voices from Cartesia:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch voices' },
      { status: 500 }
    );
  }
} 