import { NextResponse } from 'next/server';
import { ElevenLabsClient } from "elevenlabs";

export async function POST(request: Request) {
  try {
    // Get the request body
    const body = await request.json();
    const { text, voice_description } = body;
    
    if (!text || !voice_description) {
      return NextResponse.json(
        { error: 'Text and voice description are required' },
        { status: 400 }
      );
    }
    
    // Initialize ElevenLabs client with API key
    const client = new ElevenLabsClient({
      apiKey: process.env.ELEVENLABS_API_KEY || '',
    });
    
    // Step 1: Create voice previews first
    const previewResponse = await client.textToVoice.createPreviews({
      text,
      voice_description,
    });
    
    // Extract the generated voice ID from the preview response
    // Convert the response to any type to access properties that might not be in the TypeScript type definitions
    const responseData = previewResponse as any;
    let generatedVoiceId = null;
    
    if (responseData && responseData.generated_voice_ids && responseData.generated_voice_ids.length > 0) {
      generatedVoiceId = responseData.generated_voice_ids[0];
    } else {
      throw new Error('No generated voice ID found in preview response');
    }
    
    // Step 2: Create a permanent voice from the preview using the generatedVoiceId
    const voiceName = voice_description.substring(0, 40); // Limit name length
    
    const finalVoice = await client.textToVoice.createVoiceFromPreview({
      voice_name: voiceName,
      voice_description: voice_description,
      generated_voice_id: generatedVoiceId
    });
    
    // Return the voice data with preview URLs
    return NextResponse.json({
      success: true,
      previewData: {
        voiceId: finalVoice.voice_id,
        name: voiceName,
        description: voice_description,
        generatedVoiceIds: responseData.generated_voice_ids,
        // Include audio URLs from the preview response
        audioUrls: responseData.audios.map((audio: any) => audio.audio_url)
      }
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('Error creating voice with ElevenLabs:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create voice' },
      { status: 500 }
    );
  }
} 