import { NextResponse } from 'next/server';
import { ElevenLabsClient } from "elevenlabs";
import { put } from '@vercel/blob';

// Cache of valid voice IDs to avoid repeated API calls
let validVoiceIdsCache: string[] = [];
let lastCacheTime = 0;

/**
 * Validate if a voice ID exists in ElevenLabs 
 */
async function validateVoiceId(voiceId: string): Promise<boolean> {
  const now = Date.now();
  const CACHE_TTL = 10 * 60 * 1000; // 10 minutes
  
  // Refresh the cache if it's old or empty
  if (now - lastCacheTime > CACHE_TTL || validVoiceIdsCache.length === 0) {
    try {
      const response = await fetch('https://api.elevenlabs.io/v1/voices', {
        headers: {
          'xi-api-key': process.env.ELEVENLABS_API_KEY || '',
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        validVoiceIdsCache = data.voices.map((voice: any) => voice.voice_id);
        lastCacheTime = now;
        console.log(`Cached ${validVoiceIdsCache.length} valid voice IDs from ElevenLabs`);
      } else {
        console.error(`Failed to fetch voices from ElevenLabs: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching voices from ElevenLabs:', error);
    }
  }
  
  return validVoiceIdsCache.includes(voiceId);
}

/**
 * Execute with retry logic
 */
async function executeWithRetry<T>(
  operation: () => Promise<T>, 
  retries = 3, 
  delay = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      console.log(`Attempt ${attempt}/${retries} failed:`, error);
      
      if (attempt < retries) {
        // Calculate exponential backoff with jitter
        const jitter = Math.random() * 0.3 + 0.85; // Random between 0.85 and 1.15
        const sleepTime = Math.min(delay * Math.pow(2, attempt - 1) * jitter, 10000);
        console.log(`Retrying in ${Math.round(sleepTime)}ms...`);
        await new Promise(resolve => setTimeout(resolve, sleepTime));
      }
    }
  }
  
  throw lastError;
}

export async function POST(request: Request) {
  try {
    // Get the request body
    const body = await request.json();
    const { voiceId, text, modelId = "eleven_multilingual_v2" } = body;
    
    if (!voiceId || !text) {
      return NextResponse.json(
        { error: 'Voice ID and text are required' },
        { status: 400 }
      );
    }
    
    console.log(`Processing TTS request - Voice ID: ${voiceId}, Text length: ${text.length}, Model: ${modelId}`);
    
    // Validate the voice ID
    const isValid = await validateVoiceId(voiceId);
    if (!isValid) {
      console.error(`Invalid voice ID: ${voiceId}`);
      return NextResponse.json(
        { 
          error: 'Invalid voice ID', 
          details: `The voice ID '${voiceId}' does not exist in ElevenLabs`,
          validVoiceCount: validVoiceIdsCache.length
        },
        { status: 400 }
      );
    }
    
    try {
      // Use direct API call with fetch and retry logic
      const audioBuffer = await executeWithRetry(async () => {
        const elevenlabsResponse = await fetch(
          `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`,
          {
            method: 'POST',
            headers: {
              'Accept': 'audio/mpeg',
              'Content-Type': 'application/json',
              'xi-api-key': process.env.ELEVENLABS_API_KEY || '',
            },
            body: JSON.stringify({
              text,
              model_id: modelId,
              voice_settings: {
                stability: 0.5,
                similarity_boost: 0.75
              }
            })
          }
        );
        
        if (!elevenlabsResponse.ok) {
          let errorText = '';
          try {
            errorText = await elevenlabsResponse.text();
            // Try to parse as JSON for better error reporting
            const errorJson = JSON.parse(errorText);
            console.error('ElevenLabs API error:', JSON.stringify(errorJson, null, 2));
            throw new Error(`ElevenLabs API error: ${elevenlabsResponse.status} - ${errorJson.detail || errorJson.message || 'Unknown error'}`);
          } catch (parseError) {
            // If not JSON or other parsing error
            console.error('ElevenLabs API error (raw):', errorText);
            throw new Error(`ElevenLabs API error: ${elevenlabsResponse.status} - ${errorText.substring(0, 200) || 'No response body'}`);
          }
        }
        
        const arrayBuffer = await elevenlabsResponse.arrayBuffer();
        return Buffer.from(arrayBuffer);
      });
      
      console.log(`Successfully generated audio from ElevenLabs API: ${audioBuffer.byteLength} bytes`);
      
      // Two approaches - save to Vercel Blob for longer content, use data URL for short content
      let audioUrl;
      
      if (text.length > 200 || audioBuffer.byteLength > 500000) {
        // For longer content, save to Vercel Blob
        // Create a sanitized snippet of the text for the filename
        const textSnippet = text.substring(0, 30).replace(/[^a-zA-Z0-9]/g, '_');
        const timestamp = Date.now();
        
        const blob = await put(`tts/${voiceId}_${textSnippet}_${timestamp}.mp3`, audioBuffer, {
          access: 'public',
          contentType: 'audio/mpeg',
          addRandomSuffix: true
        });
        
        audioUrl = blob.url;
        console.log(`Saved TTS audio to Blob storage: ${audioUrl}`);
      } else {
        // For shorter content, use data URL
        const base64Audio = audioBuffer.toString('base64');
        audioUrl = `data:audio/mpeg;base64,${base64Audio}`;
        console.log(`Created data URL for TTS audio: ${audioUrl.substring(0, 50)}...`);
      }
      
      return NextResponse.json({
        success: true,
        audioUrl
      }, { status: 200 });
      
    } catch (elevenlabsError: any) {
      console.error('ElevenLabs API error:', elevenlabsError);
      return NextResponse.json(
        { 
          error: 'Failed to generate audio from ElevenLabs API', 
          details: elevenlabsError.message || 'Unknown ElevenLabs error',
          voiceId
        },
        { status: 500 }
      );
    }
    
  } catch (error: any) {
    console.error('Error in elevenlabs-text-to-speech route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to convert text to speech' },
      { status: 500 }
    );
  }
} 