import { NextResponse } from 'next/server';
import { ElevenLabsClient } from "elevenlabs";

export async function GET() {
  try {
    // Initialize ElevenLabs client with API key
    const client = new ElevenLabsClient({
      apiKey: process.env.ELEVENLABS_API_KEY || '',
    });

    // Fetch all available voices
    const response = await client.voices.getAll();
    
    return NextResponse.json({ voices: response.voices }, { status: 200 });
  } catch (error: any) {
    console.error('Error fetching ElevenLabs voices:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch voices' },
      { status: 500 }
    );
  }
} 