import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

// Define interfaces for working with Replicate
interface ModelVersion {
  id: string;
  [key: string]: any;
}

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY || '',
});

// Model ID for PlayHT Dialog
const MODEL_OWNER = "playht";
const MODEL_NAME = "play-dialog";

// Define the expected format for text input
// Multi-speaker format: "Speaker 1: Hi there Speaker 2: Hello, how are you?"
// Single-speaker format: Just normal text without any speaker prefixes

export async function POST(req: NextRequest) {
  try {
    const {
      text,
      voice,
      voice_2,
      prompt,
      prompt2,
      language,
      turnPrefix,
      turnPrefix2,
      temperature,
      voice_conditioning_seconds,
      voice_conditioning_seconds_2
    } = await req.json();

    console.log("Received dialog request:", {
      text,
      voice,
      voice_2,
      turnPrefix,
      turnPrefix2
    });

    if (!text || !voice) {
      return NextResponse.json({ error: "Missing required parameters" }, { status: 400 });
    }

    // No subscription checks here - if the user can access the UI, they can use this API

    // Check if we're in multi-speaker mode
    const isMultiSpeaker = voice_2 && voice_2 !== "None";
    console.log(`Using ${isMultiSpeaker ? "multi-speaker" : "single-speaker"} mode`);

    // For most cases, we can use the text as provided.
    // The frontend should already format it correctly.
    let formattedText = text;
    
    // Prepare input for the model exactly as shown in the example
    const input: Record<string, any> = {
      text: formattedText,
      voice: voice,
      prompt: prompt || "",
      prompt2: prompt2 || "",
      voice_2: isMultiSpeaker ? voice_2 : "None",
      language: language || "english",
      turnPrefix: turnPrefix || "Voice 1:",
      temperature: temperature || 1.02,
      turnPrefix2: turnPrefix2 || "Voice 2:",
      voice_conditioning_seconds: voice_conditioning_seconds || 20,
      voice_conditioning_seconds_2: voice_conditioning_seconds_2 || 20
    };
    
    console.log("Sending to Replicate:", input);
    
    // Get the latest version of the model
    let latestVersion: ModelVersion | undefined;
    try {
      // Try to get the model and its latest version
      const model = await replicate.models.get(MODEL_OWNER, MODEL_NAME);
      latestVersion = model.latest_version;
      if (latestVersion) {
        console.log(`Using the latest version of ${MODEL_OWNER}/${MODEL_NAME}: ${latestVersion.id}`);
      } else {
        throw new Error("No latest version found for model");
      }
    } catch (versionError) {
      console.error("Failed to get latest model version:", versionError);
      // Try a few known versions as fallbacks
      const fallbackVersions = [
        "f07566c2b5f6d5c3e0a40fe66045ed8efdd49c57e9e7cfc85521cc7f37ede0a8",
        "42aa995f8a650dd11ee1926f1585f8d07308f731f23ba226dec5e22fe7eb0c59", 
        "f078f147c97f5dfa16e1ba9dce2e60b5e9c23b33c571e5b62f6c6a88e2c89c8e"
      ];
      
      // Try each fallback version
      for (const version of fallbackVersions) {
        try {
          console.log(`Trying fallback version: ${version}`);
          // Test if this version works by creating a minimal prediction
          await replicate.predictions.create({
            version,
            input: { test: true },
            webhook: undefined,
            webhook_events_filter: undefined
          });
          
          // If we get here, the version works
          latestVersion = { id: version };
          console.log(`Using fallback version: ${version}`);
          break;
        } catch (fallbackError) {
          console.log(`Fallback version ${version} failed:`, fallbackError);
          // Continue to the next fallback
        }
      }
      
      // If we couldn't find a working version, return an error
      if (!latestVersion) {
        console.error("No working model version found");
        return NextResponse.json(
          { error: "Failed to find a working model version" }, 
          { status: 500 }
        );
      }
    }
    
    // Call the Replicate API with the PlayHT model using the latest version
    const response = await replicate.predictions.create({
      version: latestVersion!.id,
      input: input,
    });
    
    console.log("Replicate prediction created:", response.id);
    
    // Poll for the result
    let audioUrl: string | null = null;
    let attempts = 0;
    const maxAttempts = 30; // About 5 minutes of polling
    
    while (!audioUrl && attempts < maxAttempts) {
      attempts++;
      console.log(`Polling prediction status (attempt ${attempts})...`);
      
      // Wait between polls
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 seconds
      
      // Get the latest status
      const status = await replicate.predictions.get(response.id);
      console.log("Prediction status:", status.status);
      
      if (status.status === "succeeded") {
        console.log("Prediction output:", status.output);
        
        // Extract the URL directly from the output property
        if (typeof status.output === 'string') {
          audioUrl = status.output;
          console.log("Found direct URL:", audioUrl);
        } else if (status.output && typeof status.output === 'object') {
          // For responses that contain an object with an 'output' property
          audioUrl = (status.output as any).output || status.output;
          console.log("Found URL in object:", audioUrl);
        }
        
        break;
      } else if (status.status === "failed") {
        throw new Error(`Prediction failed: ${status.error || 'Unknown error'}`);
      }
      // Continue polling for processing/starting statuses
    }
    
    // Final check and validation
    if (!audioUrl) {
      console.error("Could not extract audio URL from Replicate response");
      throw new Error("Failed to get audio URL from Replicate");
    }
    
    // Validate the URL is valid
    try {
      new URL(audioUrl);
      console.log("Valid URL confirmed:", audioUrl);
    } catch (urlError: any) {
      console.error(`Invalid URL format: ${audioUrl}`, urlError);
      throw new Error(`Invalid audio URL: ${urlError.message}`);
    }
    
    // Return the audio URL directly from Replicate
    return NextResponse.json({
      audioUrl: audioUrl,
      success: true,
      format: 'mp3',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error in Replicate PlayHT Dialog generation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
} 