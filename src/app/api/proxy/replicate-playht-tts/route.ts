import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(request: NextRequest) {
  try {
    const { text, voice, model_version } = await request.json();
    
    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }
    
    if (!voice) {
      return NextResponse.json(
        { error: 'Voice is required' },
        { status: 400 }
      );
    }
    
    console.log(`Generating audio with PlayHT Dialog model, text length: ${text.length}, voice: ${voice}`);
    
    // Call the Replicate API with the PlayHT model
    const output = await replicate.run("playht/play-dialog", {
      input: {
        text: text,
        voice: voice,
        model_version: model_version || "e24d14c5341a83b73e020a1c4a998ff8ab057e1bff4633a6a3954e275d047f30"
      }
    });
    
    // The output from Replicate for this model is directly the audio URL
    console.log(`Received audio from PlayHT Dialog model: ${typeof output}`);
    
    return NextResponse.json({
      success: true,
      audioUrl: output, // The model returns the audio URL directly
    });
    
  } catch (error: any) {
    console.error('Error in PlayHT TTS via Replicate:', error);
    
    return NextResponse.json(
      { 
        error: error.message || 'Failed to generate audio', 
        details: error.toString()
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
} 