import { NextResponse } from 'next/server';

// This endpoint tests the ElevenLabs API directly to diagnose any issues
export async function GET() {
  try {
    console.log('Testing ElevenLabs API connectivity...');
    
    // First check if we have an API key
    const apiKey = process.env.ELEVENLABS_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'ELEVENLABS_API_KEY is missing in environment variables' },
        { status: 500 }
      );
    }
    
    // Test the API key by getting voices
    console.log('Fetching available voices from ElevenLabs...');
    const voicesResponse = await fetch('https://api.elevenlabs.io/v1/voices', {
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json'
      }
    });
    
    if (!voicesResponse.ok) {
      let errorText = '';
      try {
        errorText = await voicesResponse.text();
      } catch (e) {
        errorText = 'Could not read error response';
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to fetch voices from ElevenLabs', 
          status: voicesResponse.status,
          details: errorText
        },
        { status: 500 }
      );
    }
    
    const voicesData = await voicesResponse.json();
    const voiceCount = voicesData.voices?.length || 0;
    const voiceNames = voicesData.voices?.map((v: any) => v.name) || [];
    
    console.log(`Successfully fetched ${voiceCount} voices from ElevenLabs`);
    
    // Test generating audio with a known voice ID
    // Using a premium voice that should work if the API key is valid
    const testVoiceId = '21m00Tcm4TlvDq8ikWAM'; // Rachel voice, one of ElevenLabs' default voices
    const testText = 'This is a test of the ElevenLabs text to speech API.';
    
    console.log(`Attempting to generate audio with voice ID: ${testVoiceId}`);
    const ttsResponse = await fetch(
      `https://api.elevenlabs.io/v1/text-to-speech/${testVoiceId}`,
      {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': apiKey,
        },
        body: JSON.stringify({
          text: testText,
          model_id: 'eleven_multilingual_v2',
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.75
          }
        })
      }
    );
    
    if (!ttsResponse.ok) {
      let errorDetails = '';
      try {
        const errorText = await ttsResponse.text();
        try {
          // Try to parse as JSON for better error reporting
          const errorJson = JSON.parse(errorText);
          errorDetails = JSON.stringify(errorJson);
        } catch {
          errorDetails = errorText.substring(0, 200);
        }
      } catch (e) {
        errorDetails = 'Could not read error response';
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to generate test audio from ElevenLabs API', 
          status: ttsResponse.status,
          details: errorDetails,
          apiKeyFirstChars: apiKey.substring(0, 4) + '...',
          apiKeyLength: apiKey.length
        },
        { status: 500 }
      );
    }
    
    const audioArrayBuffer = await ttsResponse.arrayBuffer();
    const audioBuffer = Buffer.from(audioArrayBuffer);
    
    return NextResponse.json(
      { 
        success: true, 
        voiceCount,
        voiceNames,
        audioGenerationSuccess: true,
        audioByteLength: audioBuffer.byteLength,
        apiKeyFirstChars: apiKey.substring(0, 4) + '...',
        apiKeyLength: apiKey.length
      },
      { status: 200 }
    );
    
  } catch (error: any) {
    console.error('Error in test-elevenlabs endpoint:', error);
    return NextResponse.json(
      { error: error.message || 'Unknown error during ElevenLabs API test' },
      { status: 500 }
    );
  }
} 