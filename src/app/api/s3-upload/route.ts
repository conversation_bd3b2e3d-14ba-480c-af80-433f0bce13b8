import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const maxDuration = 60; // Allow up to 60 seconds for large uploads

// Supabase storage configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://gntleknxbxbmzwizidgv.supabase.co';
// Use service role key for server-side API routes to bypass RLS
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Storage bucket names
const BUCKET_NAMES = {
  audio: process.env.S3_AUDIO_BUCKET || 'audio',
  image: process.env.S3_IMAGE_BUCKET || 'images',
  video: process.env.S3_VIDEO_BUCKET || 'videos',
  podcast: process.env.S3_PODCAST_BUCKET || 'podcasts',
  voice: process.env.S3_VOICE_BUCKET || 'voice',
};

// Helper function to convert data URL to buffer
function dataURLToBuffer(dataURL: string): { buffer: Buffer, contentType: string } {
  // Extract the base64 data from the URL
  const matches = dataURL.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);
  
  if (!matches || matches.length !== 3) {
    throw new Error('Invalid data URL format');
  }
  
  const contentType = matches[1];
  const data = matches[2];
  return { buffer: Buffer.from(data, 'base64'), contentType };
}

/**
 * Generate a unique file path for storage
 */
function generateFilePath(
  userId: string,
  fileName: string,
  folder?: string
): string {
  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
  const timestamp = Date.now();
  const uniqueId = Math.random().toString(36).substring(2, 8);
  
  const basePath = folder ? `${userId}/${folder}` : userId;
  return `${basePath}/${timestamp}-${uniqueId}-${sanitizedFileName}`;
}

/**
 * Direct upload to Supabase Storage
 */
async function uploadToSupabase(
  fileBuffer: Buffer,
  contentType: string,
  bucketName: string,
  filePath: string
): Promise<string> {
  try {
    console.log(`Uploading to Supabase bucket ${bucketName}, path: ${filePath}`);
    
    // First check if the bucket exists and create it if it doesn't
    try {
      const getBucketResponse = await fetch(
        `${SUPABASE_URL}/storage/v1/bucket/${bucketName}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`
          }
        }
      );
      
      if (!getBucketResponse.ok && getBucketResponse.status === 404) {
        console.log(`Bucket ${bucketName} does not exist, creating...`);
        // Create the bucket
        const createBucketResponse = await fetch(
          `${SUPABASE_URL}/storage/v1/bucket`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`
            },
            body: JSON.stringify({
              id: bucketName,
              name: bucketName,
              public: true, // Make the bucket public
              file_size_limit: 52428800, // 50MB
              allowed_mime_types: ['image/*', 'video/*', 'audio/*']
            })
          }
        );
        
        if (!createBucketResponse.ok) {
          const errorText = await createBucketResponse.text();
          console.error(`Failed to create bucket: ${errorText}`);
        } else {
          console.log(`Successfully created bucket ${bucketName}`);
        }
      }
    } catch (bucketError) {
      console.error('Error checking/creating bucket:', bucketError);
      // Continue anyway, as the bucket might already exist
    }
    
    // Upload the file directly to Supabase Storage
    const uploadResponse = await fetch(
      `${SUPABASE_URL}/storage/v1/object/${bucketName}/${filePath}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': contentType,
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
          'x-upsert': 'true'
        },
        body: fileBuffer
      }
    );
    
    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      // Log the detailed error for debugging
      console.error(`Supabase upload failed with status ${uploadResponse.status}:`, errorText);
      throw new Error(`Supabase upload failed: ${uploadResponse.status} - ${errorText}`);
    }
    
    // Generate a public URL (or signed URL if needed)
    const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/${bucketName}/${filePath}`;
    console.log(`Successfully uploaded to Supabase: ${publicUrl}`);
    
    return publicUrl;
  } catch (error) {
    console.error('Error in direct Supabase upload:', error);
    throw error;
  }
}

export async function POST(req: NextRequest) {
  let parsedFormData: FormData | null = null;
  let fileData = '';
  let fileName = '';
  let fileType = '';
  let userId = '';
  let type = '';
  let folder: string | undefined = undefined;
  let key = '';
  let bucketName = '';
  
  try {
    // Parse the request body
    parsedFormData = await req.formData();
    
    // Get parameters
    fileData = parsedFormData.get('fileData') as string;
    fileName = parsedFormData.get('fileName') as string;
    fileType = parsedFormData.get('fileType') as string;
    userId = parsedFormData.get('userId') as string;
    type = parsedFormData.get('type') as string;
    folder = parsedFormData.get('folder') as string | undefined;
    
    // Validate required parameters
    if (!fileData) {
      return NextResponse.json({ error: 'File data is required' }, { status: 400 });
    }
    
    if (!fileName) {
      return NextResponse.json({ error: 'File name is required' }, { status: 400 });
    }
    
    if (!fileType) {
      return NextResponse.json({ error: 'File type is required' }, { status: 400 });
    }
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    
    if (!type || !['audio', 'image', 'video', 'podcast', 'voice'].includes(type)) {
      return NextResponse.json({ error: 'Invalid file category type' }, { status: 400 });
    }
    
    // Get the bucket name
    bucketName = BUCKET_NAMES[type as keyof typeof BUCKET_NAMES];
    
    // Generate the storage key (file path)
    key = generateFilePath(userId, fileName, folder);
    
    // Process the file data
    let fileBuffer: Buffer;
    let actualContentType: string;
    
    if (fileData.startsWith('data:')) {
      // Handle data URL
      try {
        const result = dataURLToBuffer(fileData);
        fileBuffer = result.buffer;
        actualContentType = result.contentType;
      } catch (error: any) {
        return NextResponse.json({ 
          error: `Invalid data URL: ${error.message}` 
        }, { status: 400 });
      }
    } else {
      // For this endpoint, we only accept data URLs
      return NextResponse.json({ 
        error: 'Only data URLs are supported through this endpoint' 
      }, { status: 400 });
    }
    
    // Handle local storage fallback when Supabase upload fails
    const handleLocalStorageFallback = () => {
      console.log('Supabase upload failed, using local storage fallback');
      // Just return the data URL directly as a fallback
      return NextResponse.json({
        success: true,
        url: fileData,
        key: key,
        storage: 'local'
      });
    };
    
    // Upload to Supabase
    try {
      console.log(`Uploading ${fileName} to Supabase Storage bucket ${bucketName}, key: ${key}`);
      
      // Set timeout for the operation
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);
      
      try {
        // Upload using direct fetch to Supabase Storage API
        const url = await uploadToSupabase(fileBuffer, actualContentType || fileType, bucketName, key);
        
        clearTimeout(timeoutId);
        
        return NextResponse.json({
          success: true,
          url,
          key,
          storage: 'supabase'
        });
      } catch (error: any) {
        clearTimeout(timeoutId);
        
        if (error.name === 'AbortError') {
          console.error('Upload request timed out after 15 seconds');
          return handleLocalStorageFallback();
        }
        
        throw error; // Rethrow for the outer catch
      }
    } catch (uploadError: any) {
      console.error('Error uploading to Supabase:', uploadError);
      
      // Network or connection issues
      if (uploadError.code === 'ENOTFOUND' || 
          uploadError.message?.includes('ENOTFOUND') || 
          uploadError.message?.includes('getaddrinfo') ||
          uploadError.message?.includes('Network') ||
          uploadError.message?.includes('network') ||
          uploadError.message?.includes('timed out')) {
        console.error('Network or connection error:', uploadError.message);
        return handleLocalStorageFallback();
      }
      
      // Authentication issues
      if (uploadError.message?.includes('Authentication') || 
          uploadError.message?.includes('authorization') ||
          uploadError.message?.includes('401') ||
          uploadError.message?.includes('403')) {
        console.error('Authentication error:', uploadError.message);
        return handleLocalStorageFallback();
      }
      
      // Generic Supabase error - use fallback
      console.error('Generic Supabase Storage error, using fallback:', uploadError.message);
      return handleLocalStorageFallback();
    }
  } catch (error: any) {
    console.error('Unhandled error in s3-upload API:', error);
    
    // For any server error, fall back to returning the original data URL
    if (fileData && fileData.startsWith('data:')) {
      return NextResponse.json({
        success: true,
        url: fileData,
        key: key || 'unknown-key',
        storage: 'local',
        note: 'Used fallback due to server error'
      });
    }
    
    return NextResponse.json({ 
      error: `Server error: ${error.message}` 
    }, { status: 500 });
  }
} 