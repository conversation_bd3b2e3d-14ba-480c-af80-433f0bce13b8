import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';

export async function GET(req: NextRequest) {
  try {
    // Get the URL parameters
    const url = new URL(req.url);
    const userId = url.searchParams.get('userId');
    const bucket = url.searchParams.get('bucket') || 'images';
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://gntleknxbxbmzwizidgv.supabase.co';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (!supabaseAnonKey) {
      return NextResponse.json(
        { success: false, error: 'Supabase configuration is missing' },
        { status: 500 }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // The path where user generated content is stored
    const tempUserId = `temp-user-${userId}`;
    const generatedPath = `${tempUserId}/generated`;
    
    // List files from Supabase Storage
    const { data: files, error } = await supabase
      .storage
      .from(bucket)
      .list(generatedPath, {
        limit: 1000,
        offset: 0,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (error) {
      console.error('Error listing files from Supabase Storage:', error);
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      );
    }

    // Filter out directories (folders) and keep only files
    const fileObjects = files.filter(file => !file.id.endsWith('/')).map(file => {
      return {
        id: file.id,
        name: file.name,
        size: file.metadata?.size || 0,
        url: `${supabaseUrl}/storage/v1/object/public/${bucket}/${generatedPath}/${file.name}`,
        createdAt: file.created_at
      };
    });

    return NextResponse.json({
      success: true,
      files: fileObjects
    });
  } catch (error: any) {
    console.error('Error in storage/list API:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
} 