import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getUserSubscription } from '@/lib/neo4j';
import { getSubscription, getStripe } from '@/lib/stripe';

// API route to get subscription status
export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = session.user.id;
    
    // Get subscription from Neo4j
    const subscription = await getUserSubscription(userId);
    
    if (!subscription) {
      return NextResponse.json({ 
        subscription: null,
        isActive: false,
        subscriptionType: 'free'
      });
    }
    
    // If subscription has a Stripe ID, get the latest details from Stripe
    if (subscription.stripeSubscriptionId) {
      try {
        const stripeSubscription = await getSubscription(subscription.stripeSubscriptionId);
        
        // Update with real-time status from Stripe if different
        if (stripeSubscription.status !== subscription.status) {
          const { executeQuery } = await import('@/lib/neo4j');
          
          // Update Neo4j with the latest status
          await executeQuery(`
            MATCH (u:User {id: $userId})-[:HAS_SUBSCRIPTION]->(s:Subscription)
            SET s.status = $status, s.updatedAt = $updatedAt
            RETURN s
          `, {
            userId,
            status: stripeSubscription.status,
            updatedAt: new Date().toISOString()
          });
          
          // Update the response with the latest status
          subscription.status = stripeSubscription.status;
        }
      } catch (stripeError) {
        console.error('Error fetching subscription from Stripe:', stripeError);
        // Continue with the local data if Stripe fetch fails
      }
    }
    
    // Determine if the subscription is active
    const isActive = (
      subscription.status === 'active' || 
      subscription.status === 'trialing'
    ) && new Date(subscription.endDate) > new Date();
    
    return NextResponse.json({ 
      subscription,
      isActive,
      subscriptionType: subscription.type || 'free'
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch subscription',
        isActive: false,
        subscriptionType: 'free'
      },
      { status: 500 }
    );
  }
}

// Update subscription (cancel, change plan, etc)
export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = session.user.id;
    const { action } = await request.json();
    
    // Get current subscription
    const subscription = await getUserSubscription(userId);
    
    if (!subscription) {
      return NextResponse.json(
        { error: 'No active subscription found' },
        { status: 404 }
      );
    }
    
    if (action === 'cancel') {
      if (subscription.stripeSubscriptionId) {
        // Cancel in Stripe
        const stripe = await getStripe();
        await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
          cancel_at_period_end: true
        });
      }
      
      // Mark as cancelling in Neo4j
      const { cancelSubscription } = await import('@/lib/neo4j');
      await cancelSubscription(userId);
      
      return NextResponse.json({ 
        message: 'Subscription will be cancelled at the end of the current billing period' 
      });
    }
    
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error updating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    );
  }
} 