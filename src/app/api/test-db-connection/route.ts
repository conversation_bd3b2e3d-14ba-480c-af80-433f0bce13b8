import { NextResponse } from 'next/server';
import { getSession, initNeo4j } from '@/lib/neo4j';
import neo4j from 'neo4j-driver';

export async function GET() {
  const requestId = Math.random().toString(36).substring(2, 9);
  console.log(`[${requestId}] DB connection test started`);
  
  // Check environment variables (safely, without exposing values)
  const envCheck = {
    NEO4J_URI: !!process.env.NEO4J_URI,
    NEO4J_USERNAME: !!process.env.NEO4J_USERNAME,
    NEO4J_USER: !!process.env.NEO4J_USER,
    NEO4J_PASSWORD: !!process.env.NEO4J_PASSWORD,
  };
  
  console.log(`[${requestId}] Environment variables:`, envCheck);
  
  try {
    // Try to initialize the driver (this will be a no-op if already initialized)
    const driver = await initNeo4j();
    console.log(`[${requestId}] Driver initialized`);
    
    // Test connectivity
    try {
      // Since verifyConnectivity is already a promise, we can just await it
      await driver.verifyConnectivity();
      console.log(`[${requestId}] Neo4j connectivity verified`);
      
      // Try a simple query
      const session = await getSession();
      try {
        const result = await session.run('RETURN 1 AS num');
        console.log(`[${requestId}] Test query executed successfully`);
        
        return NextResponse.json({
          success: true,
          message: 'Database connection successful',
          envCheck,
          testQuery: result.records[0].get('num').toNumber() === 1
        });
      } catch (queryError: any) {
        console.error(`[${requestId}] Test query failed:`, queryError);
        return NextResponse.json({
          success: false,
          message: 'Database connection established but query failed',
          envCheck,
          error: queryError.message || 'Unknown query error'
        }, { status: 500 });
      } finally {
        await session.close();
      }
    } catch (connectError: any) {
      console.error(`[${requestId}] Neo4j connectivity test failed:`, connectError);
      return NextResponse.json({
        success: false,
        message: 'Failed to connect to Neo4j database',
        envCheck,
        error: connectError.message || 'Unknown connection error'
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error(`[${requestId}] Failed to initialize Neo4j driver:`, error);
    return NextResponse.json({
      success: false,
      message: 'Failed to initialize Neo4j driver',
      envCheck,
      error: error.message || 'Unknown initialization error'
    }, { status: 500 });
  }
} 