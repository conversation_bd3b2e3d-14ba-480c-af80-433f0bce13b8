import { NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import { mkdir } from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@deepgram/sdk';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const audio = formData.get('audio') as File;
    const model = formData.get('model') as string;
    const provider = formData.get('provider') as string;
    const language = formData.get('language') as string;
    const filename = formData.get('filename') as string;
    const providedApiKey = formData.get('apiKey') as string;

    if (!audio) {
      return NextResponse.json(
        { error: 'No audio file provided' },
        { status: 400 }
      );
    }

    // Create uploads directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'uploads');
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (err) {
      console.error('Error creating uploads directory:', err);
    }

    // Generate unique filename
    const uniqueId = uuidv4();
    const fileExtension = filename.split('.').pop();
    const uniqueFilename = `${uniqueId}.${fileExtension}`;
    const filePath = join(uploadDir, uniqueFilename);

    // Convert file to buffer and save it
    const buffer = Buffer.from(await audio.arrayBuffer());
    await writeFile(filePath, buffer);

    let transcript = '';

    if (provider.toLowerCase() === 'groq') {
      // Handle Groq transcription
      const apiKey = providedApiKey || '********************************************************';
      const endpoint = 'https://api.groq.com/openai/v1/audio/transcriptions';
      
      const audioFormData = new FormData();
      audioFormData.append('file', new Blob([buffer]), uniqueFilename);
      audioFormData.append('model', 'whisper-large-v3');
      
      if (language && language !== 'auto') {
        audioFormData.append('language', language);
      }

      const headers = {
        'Authorization': `Bearer ${apiKey}`
      };

      const transcriptionResponse = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: audioFormData
      });

      if (!transcriptionResponse.ok) {
        const errorData = await transcriptionResponse.json();
        throw new Error(`Groq API error: ${JSON.stringify(errorData)}`);
      }

      const transcriptionData = await transcriptionResponse.json();
      transcript = transcriptionData.text || '';
      
    } else if (provider.toLowerCase() === 'deepgram') {
      // Handle Deepgram transcription using SDK
      const apiKey = providedApiKey || 'ab53c6ea404e52560bba8416cc834fda48b7ceab';
      
      // Extract model name from the full model ID (e.g., "deepgram/nova-2" -> "nova-2")
      const modelName = model.split('/')[1] || 'nova-2';
      
      // Create Deepgram client
      const deepgram = createClient(apiKey);
      
      // Set transcription options
      const options: {
        model: string;
        smart_format: boolean;
        language?: string;
      } = {
        model: modelName,
        smart_format: true
      };
      
      // Add language if specified
      if (language && language !== 'auto') {
        options.language = language;
      }
      
      try {
        // Use the SDK to transcribe the file
        const { result, error } = await deepgram.listen.prerecorded.transcribeFile(
          buffer,
          options
        );
        
        if (error) {
          throw new Error(`Deepgram SDK error: ${error.message || JSON.stringify(error)}`);
        }
        
        // Extract transcript from the result
        transcript = result?.results?.channels[0]?.alternatives[0]?.transcript || '';
      } catch (error: any) {
        console.error('Deepgram transcription error:', error);
        throw new Error(`Failed to transcribe with Deepgram: ${error.message || JSON.stringify(error)}`);
      }
    } else if (provider.toLowerCase() === 'elevenlabs') {
      // Handle ElevenLabs transcription
      const apiKey = providedApiKey || '***************************************************';
      const endpoint = 'https://api.elevenlabs.io/v1/speech-to-text';
      
      // Extract model name from the full model ID (e.g., "elevenlabs/scribe_v1" -> "scribe_v1")
      const modelName = model.split('/')[1] || 'scribe_v1';
      
      // Determine if using specialized model
      const isSpecialized = modelName.includes('specialized');
      
      // Create form data for ElevenLabs API
      const audioFormData = new FormData();
      audioFormData.append('file', new Blob([buffer]), uniqueFilename);
      audioFormData.append('model_id', isSpecialized ? 'scribe_v1' : 'scribe_v1');
      
      // Add additional parameters for specialized model
      if (isSpecialized) {
        audioFormData.append('transcription_type', 'detailed');
        audioFormData.append('detect_speakers', 'true');
        audioFormData.append('remove_silence', 'true');
      }
      
      if (language && language !== 'auto') {
        audioFormData.append('language', language);
      }
      
      const headers = {
        'xi-api-key': apiKey
      };
      
      try {
        const transcriptionResponse = await fetch(endpoint, {
          method: 'POST',
          headers,
          body: audioFormData
        });
        
        if (!transcriptionResponse.ok) {
          const errorData = await transcriptionResponse.json();
          throw new Error(`ElevenLabs API error: ${JSON.stringify(errorData)}`);
        }
        
        const transcriptionData = await transcriptionResponse.json();
        
        // Extract the transcript from the response
        // Note: This is based on assumed ElevenLabs API response structure
        // You may need to adjust this based on actual response format
        transcript = transcriptionData.text || transcriptionData.transcript || '';
        
      } catch (error: any) {
        console.error('ElevenLabs transcription error:', error);
        throw new Error(`Failed to transcribe with ElevenLabs: ${error.message || JSON.stringify(error)}`);
      }
    } else {
      return NextResponse.json(
        { error: 'Unsupported provider' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      transcript,
      filename: uniqueFilename,
      duration: 0 // You may need to extract this from the response or calculate it
    });
  } catch (error: unknown) {
    console.error('Error processing audio:', error);
    let errorMessage = 'Failed to process audio';
    
    if (error instanceof Error) {
      errorMessage = `${errorMessage}: ${error.message}`;
    } else if (typeof error === 'string') {
      errorMessage = `${errorMessage}: ${error}`;
    } else {
      errorMessage = `${errorMessage}: ${JSON.stringify(error)}`;
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
} 