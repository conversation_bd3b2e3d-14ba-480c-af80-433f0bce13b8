import { NextRequest, NextResponse } from 'next/server';
import { put } from '@vercel/blob';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import fs from 'fs';
import os from 'os';
import path from 'path';
import { writeFile } from 'fs/promises';

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id as string;
    const { audioUrl, title } = await req.json();

    if (!audioUrl) {
      return NextResponse.json(
        { error: 'Missing audioUrl parameter' },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      // Check if it's a valid URL
      new URL(audioUrl);
      
      // Check for abnormal content in the URL string
      if (audioUrl.includes('return new URL') || 
          audioUrl.includes('function') || 
          audioUrl.startsWith('url(')) {
        throw new Error('Invalid URL format');
      }
    } catch (urlError) {
      console.error('Invalid URL received:', audioUrl, urlError);
      return NextResponse.json(
        { error: 'Invalid audio URL format', success: false },
        { status: 400 }
      );
    }

    console.log('Fetching audio from URL:', audioUrl);
    
    // Fetch the audio from the URL with timeout and proper headers
    try {
      // Create controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout
      
      const response = await fetch(audioUrl, {
        headers: {
          'Accept': 'audio/mpeg, audio/mp3, audio/*',
          'User-Agent': 'AstroStudio-Server/1.0',
        },
        redirect: 'follow',
        cache: 'no-store',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        let errorText = '';
        try {
          errorText = await response.text();
        } catch (e) {
          // Ignore text parsing errors
        }
        
        console.error(`Fetch failed with status ${response.status}: ${response.statusText}`, 
                      errorText ? `Error text: ${errorText.substring(0, 100)}...` : '');
                      
        throw new Error(`Failed to fetch audio: ${response.statusText}`);
      }
      
      // Get audio content as ArrayBuffer
      const audioBuffer = await response.arrayBuffer();
      
      // Validate audio content
      if (audioBuffer.byteLength < 100) {
        console.warn(`Warning: Very small audio file (${audioBuffer.byteLength} bytes)`);
      }
      
      // Create a temporary file to process the audio
      const tmpDir = os.tmpdir();
      const tmpFilePath = path.join(tmpDir, `temp_audio_${Date.now()}.mp3`);
      
      try {
        // Write the audio buffer to a temporary file
        await writeFile(tmpFilePath, Buffer.from(audioBuffer));
        console.log(`Temporary audio file created at: ${tmpFilePath}`);
        
        // Read the file back to ensure it's valid
        const fileBuffer = await fs.promises.readFile(tmpFilePath);
        
        // Generate a unique filename for blob storage
        const fileName = title ? 
          `${title.substring(0, 30).replace(/[^a-zA-Z0-9]/g, '_')}` : 
          'podcast';
        const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
        const uniqueFilename = `podcasts/${userId}/${fileName}_${timestamp}.mp3`;

        // Upload to Vercel Blob
        const blob = await put(uniqueFilename, fileBuffer, {
          access: 'public',
          contentType: 'audio/mpeg',
          addRandomSuffix: true
        });

        console.log(`Uploaded processed audio to Blob: ${blob.url}`);
        
        // Clean up temp file
        await fs.promises.unlink(tmpFilePath);

        // Return the new permanent URL
        return NextResponse.json({
          success: true,
          audioUrl: blob.url,
          originalUrl: audioUrl
        });
      } catch (fileError) {
        console.error('Error processing audio file:', fileError);
        throw new Error('Failed to process audio file');
      }
    } catch (fetchError) {
      console.error('Error fetching audio:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch audio', success: false },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error uploading audio to blob:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false 
      },
      { status: 500 }
    );
  }
} 