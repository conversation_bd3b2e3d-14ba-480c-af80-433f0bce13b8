import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

// Initialize Replicate
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN ?? '',
});

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { model, video_path, resolution, model_type } = body;

    console.log(`Starting video upscaling with model: ${model}`);

    // Prepare prediction input
    let input: Record<string, any> = {
      video_path: video_path,
    };

    // Handle model-specific parameters
    if (model === 'lucataco/real-esrgan-video') {
      input.resolution = resolution ?? 'FHD'; // FHD, 2k, 4k
      input.model = model_type ?? 'RealESRGAN_x4plus'; // Default model
    } else if (model === 'tencentarc/animesr') {
      // AnimeSR has simpler parameters - just video is required
      input = { video: video_path };
    }

    // Create prediction
    const prediction = await replicate.predictions.create({
      version: model,
      input: input,
    });

    // Wait for prediction to complete
    let result;
    if (prediction.id) {
      result = await waitForPrediction(prediction.id);
    } else {
      throw new Error('Failed to create upscaling prediction');
    }

    // Return video URL in response
    return NextResponse.json({ 
      videoUrl: result.output, 
      message: 'Video upscaled successfully'
    });

  } catch (error: any) {
    console.error('Error upscaling video:', error);
    return NextResponse.json(
      { error: error.message ?? 'Failed to upscale video' },
      { status: 500 }
    );
  }
}

// Helper function to wait for prediction to complete
async function waitForPrediction(id: string) {
  let prediction = await replicate.predictions.get(id);
  
  // Poll until the prediction is complete
  while (prediction.status !== 'succeeded' && prediction.status !== 'failed') {
    await new Promise(resolve => setTimeout(resolve, 1000));
    prediction = await replicate.predictions.get(id);
  }
  
  if (prediction.status === 'failed') {
    throw new Error(prediction.error ? String(prediction.error) : 'Prediction failed with unknown error');
  }
  
  return prediction;
} 