import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getServerSession } from 'next-auth';
import authOptions from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    const { imageUrl, prompt, model, width, height, isEdited } = body;

    if (!imageUrl) {
      return NextResponse.json(
        { success: false, error: 'Image URL is required' },
        { status: 400 }
      );
    }

    // Get user from session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = (session.user as any).id || session.user.email;
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID not found in session' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { success: false, error: 'Supabase configuration is missing' },
        { status: 500 }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Fetch the image data - handle both remote URLs and data URLs
    let imageData: Blob;
    let fileName: string;
    
    if (imageUrl.startsWith('data:')) {
      // It's already a data URL
      const res = await fetch(imageUrl);
      imageData = await res.blob();
      
      // Generate a unique filename
      const fileExtension = imageData.type.split('/')[1] || 'png';
      fileName = `generated-${Date.now()}.${fileExtension}`;
    } else {
      // It's a remote URL, fetch it first
      const res = await fetch(imageUrl);
      if (!res.ok) {
        throw new Error('Failed to fetch image from provided URL');
      }
      imageData = await res.blob();
      
      // Extract filename from URL or generate a unique one
      const urlParts = new URL(imageUrl).pathname.split('/');
      const originalFileName = urlParts[urlParts.length - 1];
      fileName = originalFileName || `generated-${Date.now()}.png`;
    }
    
    // Prepare storage path
    const bucket = 'images';
    const tempUserId = `temp-user-${userId}`;
    const filePath = `${tempUserId}/generated/${fileName}`;
    
    // Upload to Supabase Storage
    const { error: uploadError } = await supabase
      .storage
      .from(bucket)
      .upload(filePath, imageData, {
        contentType: imageData.type,
        upsert: true
      });

    if (uploadError) {
      console.error('Error uploading image to Supabase:', uploadError);
      return NextResponse.json(
        { success: false, error: uploadError.message },
        { status: 500 }
      );
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase
      .storage
      .from(bucket)
      .getPublicUrl(filePath);

    // Record in database table (optional)
    // Add code here to save metadata to a database table if needed
    
    return NextResponse.json({
      success: true, 
      url: publicUrl,
      message: 'Image saved successfully'
    });
    
  } catch (error: any) {
    console.error('Error saving generated image:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to save image' },
      { status: 500 }
    );
  }
} 