import { NextRequest, NextResponse } from 'next/server';
import { createOrUpdateSubscription } from '@/lib/neo4j';
import { constructEvent, getStripe, getSubscription } from '@/lib/stripe';
import { SUBSCRIPTION_TYPES } from '@/lib/stripe-client';
import { createClient } from '@vercel/kv';

// KV client for temporary data storage
let kvClient: ReturnType<typeof createClient> | null = null;

function getKvClient() {
  if (!kvClient) {
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      console.error('Missing KV store credentials');
      throw new Error('KV storage is not properly configured');
    }
    
    kvClient = createClient({
      url: process.env.KV_REST_API_URL,
      token: process.env.KV_REST_API_TOKEN,
    });
  }
  
  return kvClient;
}

// Find the user ID from metadata
async function findUserIdFromCustomer(customerId: string) {
  // Try to find user ID from Stripe customer
  try {
    const { executeQuery } = await import('@/lib/neo4j');
    const userQuery = `
      MATCH (u:User {stripeCustomerId: $customerId})
      RETURN u.id as userId
    `;
    const result = await executeQuery(userQuery, { customerId });
    if (result.length > 0) {
      return result[0].get('userId');
    }
  } catch (error) {
    console.error('Error finding user from customer ID:', error);
  }
  
  return null;
}

// This endpoint will receive webhook events from Stripe
export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const signature = req.headers.get('stripe-signature') || '';
    
    if (!signature) {
      return new NextResponse('Missing stripe-signature header', { status: 400 });
    }
    
    // Verify webhook signature and construct the event
    let event;
    try {
      event = await constructEvent(body, signature);
    } catch (err) {
      return new NextResponse(`Webhook Error: ${err instanceof Error ? err.message : 'Unknown error'}`, { status: 400 });
    }
    
    // Handle the event
    console.log(`Event type: ${event.type}`);
    
    switch (event.type) {
      // Handle checkout session completion
      case 'checkout.session.completed': {
        const session = event.data.object;
        
        // Get user ID from metadata or client_reference_id
        const userId = session.metadata?.userId || session.client_reference_id;
        const customerId = session.customer as string;
        const subscriptionId = session.subscription as string;
        
        if (!userId) {
          console.error('No user ID in session');
          return new NextResponse('No user ID', { status: 400 });
        }
        
        // Determine the subscription type from metadata
        const subscriptionType = session.metadata?.planType || SUBSCRIPTION_TYPES.STANDARD;
        
        // Create or update the subscription in Neo4j
        await createOrUpdateSubscription(userId, {
          type: subscriptionType,
          status: 'active',
          startDate: new Date().toISOString(),
          stripeCustomerId: customerId || undefined,
          stripeSubscriptionId: subscriptionId || undefined
        });
        
        // Store a temporary key for redirection after payment
        try {
          const kv = getKvClient();
          const redirectKey = `payment_success:${userId}`;
          await kv.set(redirectKey, {
            timestamp: Date.now(),
            subscriptionType,
            redirectUrl: '/dashboard'
          });
          await kv.expire(redirectKey, 60 * 30); // Expire in 30 minutes
        } catch (kvError) {
          console.error('Error storing redirect info:', kvError);
        }
        
        if (session.customer_email) {
          // Handle payment before registration case
          // This could be a payment from a non-registered user or a Google user
          console.log(`Processing checkout for email: ${session.customer_email}`);
          
          try {
            // Import the stripe sync service
            const stripeSyncService = (await import('@/lib/services/stripe-sync-service')).StripeSyncService;
            
            // Sync the user with immediate access
            await stripeSyncService.syncPaidUserWithImmediateAccess(
              session.customer_email,
              session.customer as string,
              session.subscription as string
            );
            
            // Also try to register or validate a Google user with this email
            await stripeSyncService.validateAndRegisterGoogleUser(session.customer_email);
            
            console.log(`Successfully processed checkout and granted access for: ${session.customer_email}`);
          } catch (error) {
            console.error('Error processing checkout for email:', session.customer_email, error);
          }
        }
        
        break;
      }
      
      // Handle payment intent events for subscriptions
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object;
        const customerId = paymentIntent.customer as string;
        const userId = paymentIntent.metadata?.userId;
        
        if (userId) {
          console.log(`Payment succeeded for user ${userId}`);
          // If this is a first payment for a subscription, the subscription.created
          // event will handle updating the subscription status
        } else if (customerId) {
          // Try to find the user from the customer ID
          const userId = await findUserIdFromCustomer(customerId);
          if (userId) {
            console.log(`Found user ${userId} from customer ${customerId}`);
          }
        }
        
        break;
      }
      
      // Handle subscription created
      case 'customer.subscription.created': {
        const subscription = event.data.object;
        const customerId = subscription.customer as string;
        const userId = subscription.metadata?.userId;
        
        if (!userId && customerId) {
          // Try to find user from customer ID
          const foundUserId = await findUserIdFromCustomer(customerId);
          if (foundUserId) {
            // Create or update subscription status
            await createOrUpdateSubscription(foundUserId, {
              type: subscription.metadata?.planType || 'unknown',
              status: subscription.status,
              startDate: new Date(subscription.created * 1000).toISOString(),
              stripeCustomerId: customerId,
              stripeSubscriptionId: subscription.id
            });
          }
        } else if (userId) {
          // Create or update subscription status
          await createOrUpdateSubscription(userId, {
            type: subscription.metadata?.planType || 'unknown',
            status: subscription.status,
            startDate: new Date(subscription.created * 1000).toISOString(),
            stripeCustomerId: customerId,
            stripeSubscriptionId: subscription.id
          });
        }
        
        break;
      }
      
      // Handle subscription updates
      case 'customer.subscription.updated': {
        const subscription = event.data.object;
        const status = subscription.status;
        const customerId = subscription.customer as string;
        const userId = subscription.metadata?.userId;
        
        if (!userId && customerId) {
          // Try to find user from customer ID
          const foundUserId = await findUserIdFromCustomer(customerId);
          if (foundUserId) {
            // Update subscription status
            await createOrUpdateSubscription(foundUserId, {
              type: subscription.metadata?.planType || 'unknown',
              status: status,
              startDate: new Date().toISOString(),
              stripeCustomerId: customerId,
              stripeSubscriptionId: subscription.id
            });
          }
        } else if (userId) {
          // Update subscription status
          await createOrUpdateSubscription(userId, {
            type: subscription.metadata?.planType || 'unknown',
            status: status,
            startDate: new Date().toISOString(),
            stripeCustomerId: customerId,
            stripeSubscriptionId: subscription.id
          });
        }
        
        break;
      }
      
      // Handle subscription cancellation
      case 'customer.subscription.deleted': {
        const subscription = event.data.object;
        const customerId = subscription.customer as string;
        const userId = subscription.metadata?.userId;
        
        if (!userId && customerId) {
          // Try to find user from customer ID
          const foundUserId = await findUserIdFromCustomer(customerId);
          if (foundUserId) {
            // Update subscription status to cancelled
            await createOrUpdateSubscription(foundUserId, {
              type: subscription.metadata?.planType || 'unknown',
              status: 'cancelled',
              startDate: new Date().toISOString(),
              stripeCustomerId: customerId,
              stripeSubscriptionId: subscription.id
            });
          }
        } else if (userId) {
          // Update subscription status to cancelled
          await createOrUpdateSubscription(userId, {
            type: subscription.metadata?.planType || 'unknown',
            status: 'cancelled',
            startDate: new Date().toISOString(),
            stripeCustomerId: customerId,
            stripeSubscriptionId: subscription.id
          });
        }
        
        break;
      }
      
      // Handle invoice payment succeeded
      case 'invoice.payment_succeeded': {
        const invoice = event.data.object;
        const subscriptionId = invoice.subscription as string;
        const customerId = invoice.customer as string;
        
        if (subscriptionId) {
          // This is a subscription invoice, so we can update the subscription status
          const subscription = await getSubscription(subscriptionId);
          
          const userId = subscription.metadata?.userId;
          if (!userId && customerId) {
            // Try to find user from customer ID
            const foundUserId = await findUserIdFromCustomer(customerId);
            if (foundUserId) {
              // Update subscription status to active
              await createOrUpdateSubscription(foundUserId, {
                type: subscription.metadata?.planType || 'unknown',
                status: 'active',
                startDate: new Date().toISOString(),
                stripeCustomerId: customerId,
                stripeSubscriptionId: subscriptionId
              });
            }
          } else if (userId) {
            // Update subscription status to active
            await createOrUpdateSubscription(userId, {
              type: subscription.metadata?.planType || 'unknown',
              status: 'active',
              startDate: new Date().toISOString(),
              stripeCustomerId: customerId,
              stripeSubscriptionId: subscriptionId
            });
          }
        }
        
        break;
      }
      
      // Handle invoice payment failed
      case 'invoice.payment_failed': {
        const invoice = event.data.object;
        const subscriptionId = invoice.subscription as string;
        const customerId = invoice.customer as string;
        
        if (subscriptionId) {
          // This is a subscription invoice, so we need to update the subscription status
          const subscription = await getSubscription(subscriptionId);
          
          const userId = subscription.metadata?.userId;
          if (!userId && customerId) {
            // Try to find user from customer ID
            const foundUserId = await findUserIdFromCustomer(customerId);
            if (foundUserId) {
              // Update subscription status to past_due or unpaid depending on Stripe settings
              await createOrUpdateSubscription(foundUserId, {
                type: subscription.metadata?.planType || 'unknown',
                status: subscription.status,
                startDate: new Date().toISOString(),
                stripeCustomerId: customerId,
                stripeSubscriptionId: subscriptionId
              });
            }
          } else if (userId) {
            // Update subscription status
            await createOrUpdateSubscription(userId, {
              type: subscription.metadata?.planType || 'unknown',
              status: subscription.status,
              startDate: new Date().toISOString(),
              stripeCustomerId: customerId,
              stripeSubscriptionId: subscriptionId
            });
          }
        }
        
        break;
      }
    }
    
    return new NextResponse('Webhook received', { status: 200 });
  } catch (error) {
    console.error('Webhook handler error:', error);
    return new NextResponse('Webhook Error', { status: 400 });
  }
} 