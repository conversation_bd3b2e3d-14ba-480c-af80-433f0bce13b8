import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Log the webhook payload for debugging
    console.log('Received Replicate webhook:', JSON.stringify(body, null, 2));
    
    // Verify the webhook is from Replicate
    const signature = req.headers.get('x-replicate-signature');
    if (!signature) {
      return NextResponse.json(
        { message: 'Missing signature' },
        { status: 401 }
      );
    }
    
    // TODO: Verify the signature using your Replicate webhook secret
    
    // Handle the webhook event
    if (body.status === 'succeeded') {
      // The prediction has completed successfully
      const audioUrl = body.output;
      
      // TODO: Update your database or state management with the completed audio URL
      // You might want to store this in a database or update your application state
      
      return NextResponse.json({ message: 'Webhook processed successfully' });
    } else if (body.status === 'failed') {
      // The prediction failed
      console.error('Prediction failed:', body.error);
      
      // TODO: Handle the failure in your application
      // You might want to notify the user or update your application state
      
      return NextResponse.json(
        { message: 'Prediction failed', error: body.error },
        { status: 400 }
      );
    }
    
    // Handle other statuses if needed
    return NextResponse.json({ message: 'Webhook received' });
    
  } catch (error: any) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { message: 'Webhook error', error: error.message },
      { status: 500 }
    );
  }
} 