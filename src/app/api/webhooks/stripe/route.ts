import { NextRequest, NextResponse } from 'next/server';
import { constructEvent } from '@/lib/stripe';
import { handleStripeWebhook } from '@/lib/actions/subscription-actions';
import Stripe from 'stripe';

// This endpoint handles Stripe webhook events
export async function POST(request: NextRequest) {
  // Get the request body and signature
  const payload = await request.text();
  const signature = request.headers.get('stripe-signature') || '';

  if (!signature) {
    console.error('No Stripe signature in request');
    return NextResponse.json(
      { error: 'No signature' },
      { status: 400 }
    );
  }

  try {
    // Verify the webhook event
    let event: Stripe.Event;
    
    try {
      event = await constructEvent(payload, signature);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Handle specific events
    try {
      // Process the webhook event
      await handleStripeWebhook(event);
    } catch (error) {
      console.error('Error handling webhook:', error);
      return NextResponse.json(
        { error: 'Webhook handler failed' },
        { status: 500 }
      );
    }

    // Return success
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Unexpected error in webhook handler:', error);
    
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
} 