"use client";

import { useState, useRef, useEffect } from 'react';
import { FiSend, FiDownload,FiLoader, FiCopy, FiCheck, FiPlus, FiMessageCircle, FiChevronDown, FiSearch } from 'react-icons/fi';
import {PaperClipIcon, ArrowUpIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import Image from "next/image";
import rehypeSanitize from 'rehype-sanitize';
import { useAiAssistantTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import { AnimatePresence, motion } from 'framer-motion';

// Types for model selector
interface AIModel {
  id: string;
  name: string;
  description: string;
  provider: string;
  contextLength: number;
  capabilities: string[];
}

interface ModelCategories {
  [category: string]: AIModel[];
}

// Custom CSS for modern black UI design
const customStyles = `
  /* Hide scrollbar for clean UI */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Custom scrollbar for model selection */
  .custom-scrollbar::-webkit-scrollbar {
    width: 5px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 10px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 10px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #444;
  }
  
  /* Animation for modal */
  @keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .modal-animation {
    animation: modalFadeIn 0.2s ease-out;
  }

  /* Message bubble animations */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(8px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .message-animation {
    animation: fadeIn 0.2s ease-out;
  }
  
  /* Typing indicator animation */
  @keyframes blink {
    0% { opacity: 0.2; }
    20% { opacity: 1; }
    100% { opacity: 0.2; }
  }
  
  .typing-dot {
    animation: blink 1.4s infinite ease-in-out;
  }
  
  .typing-dot:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  .typing-dot:nth-child(3) {
    animation-delay: 0.4s;
  }
  
  /* Loading pulsing animation */
  @keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
  }
  
  .loading-pulse {
    animation: pulse 1.5s infinite ease-in-out;
  }
  
  /* Message bubble styles */
  .message-bubble-user {
    background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
    border-radius: 18px 18px 0 18px;
    color: white;
    padding: 12px 16px;
    border: 1px solid #333;
  }

  .message-bubble-assistant {
    background: linear-gradient(135deg, #111 0%, #1a1a1a 100%);
    border-radius: 18px 18px 18px 0;
    color: #e5e5e5;
    padding: 12px 16px;
    border: 1px solid #333;
  }
  
  /* Model selector styles */
  .model-selector {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 8px 12px;
    transition: all 0.2s ease;
  }
  
  .model-selector:hover {
    background-color: #222;
    border-color: #444;
  }
  
  .model-dropdown {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }
  
  .model-option {
    padding: 8px 12px;
    transition: all 0.2s ease;
  }
  
  .model-option:hover {
    background-color: #222;
  }
  
  .model-option.selected {
    background-color: #333;
  }
  
  /* Sidebar animations */
  @keyframes slideIn {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  .sidebar-animation {
    animation: slideIn 0.3s ease-out;
  }
  
  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .mobile-sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100%;
      z-index: 50;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    }
    
    .mobile-sidebar-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 40;
    }
  }
`;

// Define chat message interface
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  mock?: boolean;
}

// Define conversation interface
interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  modelId: string;
  useMock?: boolean;
}

// Available models for AI Assistant
const availableModels: AIModel[] = [
  {
    id: "together-ai/deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free",
    name: "DeepSeek R1 Distill Llama 70B",
    description: "High performance large language model with excellent reasoning capabilities.",
    provider: "Together AI",
    contextLength: 32768,
    capabilities: ["General knowledge", "Creative writing", "Code assistance", "Reasoning", "Creative content"]
  },
  {
    id: "replicate/anthropic/claude-3.7-sonnet",
    name: "Claude 3.7 Sonnet",
    description: "Advanced reasoning with exceptional creativity and instruction following.",
    provider: "Replicate",
    contextLength: 200000,
    capabilities: ["Complex reasoning", "Detailed explanations", "Creative content", "Instruction following", "Code assistance" ]
  },
  {
    id: "openai/gpt-4.1-mini",
    name: "GPT 4.1 Mini",
    description: "Advanced reasoning with exceptional creativity and instruction following.",
    provider: "Replicate",
    contextLength: 200000,
    capabilities: ["Complex reasoning", "Detailed explanations", "Creative content", "Instruction following", "Code assistance" ]
  },
  {
    id: "openai/gpt-4o",
    name: "GPT 4o",
    description: "Advanced reasoning with exceptional creativity and instruction following.",
    provider: "Replicate",
    contextLength: 200000,
    capabilities: ["Complex reasoning", "Detailed explanations", "Creative content", "Instruction following", "Code assistance" ]
  },
  {
    id: "openai/gpt-4.1-nano",
    name: "GPT 4.1 Nano",
    description: "Advanced reasoning with exceptional creativity and instruction following.",
    provider: "Replicate",
    contextLength: 200000,
    capabilities: ["Complex reasoning", "Detailed explanations", "Creative content", "Instruction following", "Code assistance" ]
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT 4o Mini",
    description: "Advanced reasoning with exceptional creativity and instruction following.",
    provider: "Replicate",
    contextLength: 200000,
    capabilities: ["Complex reasoning", "Detailed explanations", "Creative content", "Instruction following", "Code assistance" ]
  },
  {
    id: "anthropic/claude-4-sonnet",
    name: "Claude 4 Sonnet",
    description: "Advanced reasoning with exceptional creativity and instruction following.",
    provider: "Replicate",
    contextLength: 200000,
    capabilities: ["Complex reasoning", "Detailed explanations", "Creative content", "Instruction following", "Code assistance" ]
  },
  {
    id: "deepseek-ai/deepseek-r1",
    name: "DeepSeek R1",
    description: "Advanced reasoning with exceptional creativity and instruction following.",
    provider: "Replicate",
    contextLength: 200000,
    capabilities: ["Complex reasoning", "Detailed explanations", "Creative content", "Instruction following", "Code assistance" ]
  }
];

// Helper function to generate unique IDs
const generateUniqueId = () => {
  const randomStr = Math.random().toString(36);
  // Add safety check to ensure randomStr is defined before calling substring
  const randomPart = typeof randomStr === 'string' ? randomStr.substring(2, 9) : '';
  return `${Date.now()}-${randomPart}`;
};

export default function AIAssistant() {
  // Translation hooks
  const t = useAiAssistantTranslations();
  const common = useCommonTranslations();

  // Set DeepSeek as the default model
  const defaultModelId = "together-ai/deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free";
  const [selectedModel, setSelectedModel] = useState(defaultModelId);
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false);
  const [modelSearchQuery, setModelSearchQuery] = useState("");
  const [message, setMessage] = useState("");
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState("");
  const temperature = 0.7;
  const maxTokens = 1000;
  const [copySuccess, setCopySuccess] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false); // Start with sidebar collapsed
  const [isMobile, setIsMobile] = useState(false);
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  
  // Create a new conversation if none exists
  useEffect(() => {
    if (conversations.length === 0) {
      createNewConversation();
    } else if (!activeConversation) {
      setActiveConversation(conversations[0]);
    }
  }, [conversations]);
  
  // Scroll to bottom of messages when they change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [activeConversation?.messages]);
  
  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  // Detect mobile view
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setShowSidebar(false);
      }
    };

    // Initial check
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Get selected model details
  const getSelectedModel = () => {
    return availableModels.find(m => m.id === selectedModel) || null;
  };

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);
    setIsModelSelectorOpen(false);
    setModelSearchQuery(""); // Clear search when selecting
  };

  // Filter models based on search query
  const filteredModels = availableModels.filter(model =>
    model.name.toLowerCase().includes(modelSearchQuery.toLowerCase())
  );



  // Create a new conversation
  const createNewConversation = () => {
    const newConversation: Conversation = {
      id: generateUniqueId(),
      title: "New Conversation",
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      modelId: selectedModel
    };
    
    setConversations(prev => [newConversation, ...prev]);
    setActiveConversation(newConversation);
  };

  // Update conversation title based on first message
  const updateConversationTitle = (conversation: Conversation, message: string) => {
    if (conversation.messages.length === 0) {
      // For a new conversation, use the first few words of the message as the title
      const title = message.split(' ').slice(0, 5).join(' ') + (message.split(' ').length > 5 ? '...' : '');
      
      setConversations(prev => 
        prev.map(conv => 
          conv.id === conversation.id 
            ? { ...conv, title } 
            : conv
        )
      );
    }
  };

  // Send a message
  const sendMessage = async () => {
    if (!message.trim() || !activeConversation) return;
    
    try {
      setError("");
      setIsProcessing(true);
      
      // Update conversation title if this is the first message
      updateConversationTitle(activeConversation, message);
      
      // Create new user message
      const userMessage: ChatMessage = {
        id: generateUniqueId(),
        role: 'user',
        content: message,
        timestamp: new Date()
      };
      
      // Add to conversation
      const updatedConversation = {
        ...activeConversation,
        messages: [...activeConversation.messages, userMessage],
        updatedAt: new Date()
      };
      
      setActiveConversation(updatedConversation);
      setConversations(prev => 
        prev.map(conv => 
          conv.id === activeConversation.id ? updatedConversation : conv
        )
      );
      
      // Clear input
      setMessage("");
      
      // Make API request
      const model = getSelectedModel();
      console.log('Sending request to API with model:', selectedModel);
      
      try {
        const response = await fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: selectedModel,
            provider: model?.provider || '',
            messages: [...updatedConversation.messages],
            temperature,
            max_tokens: maxTokens
          }),
        });
        
        console.log('API Response status:', response.status, response.statusText);
        
        // Check for non-JSON responses first
        const contentType = response.headers.get('content-type');
        console.log('Content-Type:', contentType);
        
        if (!contentType || !contentType.includes('application/json')) {
          const textResponse = await response.text();
          console.error('Non-JSON response:', textResponse.substring(0, 500));
          throw new Error(`The server returned an invalid response. Status: ${response.status}. Please check the server logs for more details.`);
        }
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('API Error:', errorData);
          throw new Error(errorData.error || errorData.message || `Server error: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Parsed Data:', data);
        
        if (!data.success) {
          throw new Error(data.error || 'Unknown error from API');
        }
        
        // Create assistant message
        const assistantMessage: ChatMessage = {
          id: generateUniqueId(),
          role: 'assistant',
          content: data.message,
          timestamp: new Date(),
          mock: data.mock
        };
        
        // Update conversation with assistant response
        const finalConversation = {
          ...updatedConversation,
          messages: [...updatedConversation.messages, assistantMessage],
          updatedAt: new Date(),
          useMock: data.mock
        };
        
        setActiveConversation(finalConversation);
        setConversations(prev => 
          prev.map(conv => 
            conv.id === activeConversation.id ? finalConversation : conv
          )
        );
        
        // If it's using mock and this is the first message, show a helpful message about API keys
        if (data.mock && updatedConversation.messages.length <= 1) {
          setError("⚠️ Using Mock AI mode because API keys aren't configured. Please set up TOGETHER_API_KEY and REPLICATE_API_KEY in your .env file.");
        }
      } catch (apiError: any) {
        console.error('API request error:', apiError);
        
        // If we were able to at least add the user message, we should keep it
        // in the conversation, even if the AI failed to respond
        setError(apiError.message || 'Failed to get a response from the AI. Please try again.');
      }
    } catch (err: any) {
      console.error('Chat error:', err);
      setError(err.message || 'Failed to process your message. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage();
  };

  // Handle text input with Enter key (Shift+Enter for new line)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Delete a conversation
  const deleteConversation = (id: string) => {
    setConversations(prev => prev.filter(conv => conv.id !== id));
    
    // If the deleted conversation was active, set the first remaining one as active
    if (activeConversation?.id === id) {
      const remaining = conversations.filter(conv => conv.id !== id);
      setActiveConversation(remaining.length > 0 ? remaining[0] : null);
      
      // If no conversations remain, create a new one
      if (remaining.length === 0) {
        createNewConversation();
      }
    }
  };

  // Copy message content to clipboard
  const copyMessageContent = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopySuccess(true);
      
      // Reset after 2 seconds
      setTimeout(() => {
        setCopySuccess(false);
      }, 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
      setError("Failed to copy text to clipboard.");
    }
  };

  // Download conversation as text
  const downloadConversation = () => {
    if (!activeConversation) return;
    
    // Format conversation as text
    let content = `# ${activeConversation.title}\n`;
    content += `Date: ${new Date(activeConversation.createdAt).toLocaleString()}\n`;
    content += `Model: ${activeConversation.modelId}\n\n`;
    
    activeConversation.messages.forEach(msg => {
      content += `${msg.role.toUpperCase()}: ${msg.content}\n\n`;
    });
    
    // Create download link
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `conversation-${activeConversation.id}.txt`;
    a.click();
    
    // Clean up
    URL.revokeObjectURL(url);
  };

  return (
    <div className='h-full flex flex-col bg-black text-white'>
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />
      
      <div className="h-full flex flex-col bg-black text-white">
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Mock AI notification banner */}
          {activeConversation?.useMock && (
            <div className="bg-gradient-to-r from-amber-600 to-amber-700 text-white px-4 py-2 text-center text-sm font-medium">
              <span className="inline-flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {t('mock.banner')}
              </span>
            </div>
          )}
          
          {/* Header with options */}
          <div className="border-b border-[#222] bg-black">
            <div className="flex items-center justify-between p-4">
              <h1 className="text-xl font-medium bg-white bg-clip-text text-transparent">{t('title')}</h1>

              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowSidebar(!showSidebar)}
                  className="p-2 rounded-full text-gray-400 hover:text-[#417ef7] hover:bg-[#222] transition-all"
                  title={showSidebar ? t('sidebar.hideSidebar') : t('sidebar.showSidebar')}
                  aria-label={showSidebar ? t('sidebar.hideSidebar') : t('sidebar.showSidebar')}
                >
                  <FiMessageCircle />
                </button>


              </div>
            </div>
          </div>
      
      {/* Main content area with improved layout */}
      <div className="flex flex-1 overflow-hidden relative">
        {/* Mobile overlay for sidebar */}
        {showSidebar && isMobile && (
          <div 
            className="mobile-sidebar-overlay"
            onClick={() => setShowSidebar(false)}
            aria-hidden="true"
          ></div>
        )}
        
        {/* Sidebar with improved styling */}
        {showSidebar && (
          <div className={`w-64 flex-shrink-0 flex flex-col bg-[#111] border-r border-[#222] sidebar-animation z-30 ${isMobile ? 'mobile-sidebar' : ''}`}>
            {/* New conversation button */}
            <button
              onClick={createNewConversation}
              className="mx-3 my-3 py-2.5 px-3 flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-[#141493] to-[#417ef7] hover:opacity-90 text-white transition-all shadow-md hover:shadow-lg hover:shadow-[#417ef7]/25"
            >
              <FiPlus size={15} />
              <span className="text-sm font-medium">{common('create')}</span>
            </button>
            
            {/* Conversations list with no scroll */}
            <div className="flex-1 overflow-hidden p-3">
              <h3 className="text-xs uppercase text-gray-500 font-semibold px-2 mb-2.5">{t('sidebar.conversations')}</h3>

              {conversations.length === 0 ? (
                <div className="text-center py-6 text-gray-500 text-sm">
                  {t('sidebar.noConversations')}
                </div>
              ) : (
                <div className="space-y-1.5">
                  {conversations.map((conversation) => (
                    <div 
                      key={conversation.id}
                      onClick={() => {
                        setActiveConversation(conversation);
                        if (isMobile) setShowSidebar(false);
                      }}
                      className={`p-2.5 rounded-lg cursor-pointer transition-all flex items-center group ${
                        activeConversation?.id === conversation.id
                          ? 'bg-gray-800 shadow-sm'
                          : 'hover:bg-gray-800/50'
                      }`}
                    >
                      <div className={`flex-shrink-0 w-7 h-7 rounded-full flex items-center justify-center mr-2.5 ${
                        activeConversation?.id === conversation.id
                          ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white'
                          : 'bg-gray-800 text-gray-400'
                      }`}>
                        <FiMessageCircle size={12} />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-xs text-white truncate">{conversation.title}</div>
                        <div className="text-xs text-gray-400 truncate">
                          {new Date(conversation.updatedAt).toLocaleDateString()}
                        </div>
                      </div>
                      
                      {/* Delete button with improved hover effect */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteConversation(conversation.id);
                        }}
                        className="opacity-0 group-hover:opacity-100 p-1.5 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors"
                        title={t('conversation.deleteConversation')}
                        aria-label={t('conversation.deleteConversation')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Model selector with improved styling */}
            <div className="p-3 border-t border-gray-800 bg-gray-900/80">
              <div className="text-xs text-gray-400 mb-1.5 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                {t('sidebar.currentModel')}
              </div>
              <div
                className="text-xs text-white truncate font-medium p-2 rounded-md bg-gray-800 hover:bg-gray-700 cursor-pointer transition-colors"
                onClick={() => {
                  if (isMobile) setShowSidebar(false);
                }}
              >
                {getSelectedModel()?.name || t('sidebar.selectAModel')}
              </div>
            </div>
          </div>
        )}
        
        {/* Main chat area with improved styling */}
        <div className="flex-1 flex flex-col bg-black relative min-w-0">
          
          {/* Main chat area */}
          <div className="flex-1 overflow-y-auto custom-scrollbar">
            {activeConversation && activeConversation.messages.length > 0 ? (
              <div className="p-4 md:p-5">
                <div className="max-w-3xl mx-auto w-full">
                  <div className="space-y-6">
                    {activeConversation.messages.map((msg) => (
                      <div 
                        key={msg.id}
                        className={`message-animation flex ${
                          msg.role === 'user' ? 'justify-end' : 'justify-start'
                        }`}
                      >
                        <div 
                          className={`max-w-[90%] md:max-w-[75%] rounded-xl px-4 py-3.5 ${
                            msg.role === 'user' 
                              ? 'message-bubble-user text-white' 
                              : 'message-bubble-assistant text-gray-200'
                          }`}
                        >
                          {msg.mock && (
                            <div className="text-xs text-amber-300 mb-1.5 flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              {t('mock.response')}
                            </div>
                          )}
                          
                          {msg.role === 'user' ? (
                            <p className="whitespace-pre-line text-sm leading-relaxed">{msg.content}</p>
                          ) : (
                            <ReactMarkdown
                              className="prose prose-invert prose-sm max-w-none"
                              remarkPlugins={[remarkGfm]}
                            rehypePlugins={[rehypeRaw, rehypeSanitize]}
                            components={{
                              code({className, children, inline, ...props}: any) {
                                const match = /language-(\w+)/.exec(className || '');
                                return !inline && match ? (
                                  <pre className="bg-gray-800 p-4 rounded-lg overflow-x-auto">
                                    <code className={`text-gray-100 ${className || ''}`} {...props}>
                                      {String(children).replace(/\n$/, '')}
                                    </code>
                                  </pre>
                                ) : (
                                  <code className={`bg-gray-800 px-1.5 py-0.5 rounded ${className || ''}`} {...props}>
                                    {children}
                                  </code>
                                )
                              },
                              pre({children, ...props}: any) {
                                return (
                                  <pre className="rounded-md overflow-hidden my-4" {...props}>
                                    {children}
                                  </pre>
                                )
                              },
                              table({children, ...props}: any) {
                                return (
                                  <div className="overflow-x-auto my-4 rounded-md border border-gray-600">
                                    <table className="min-w-full text-sm" {...props}>
                                      {children}
                                    </table>
                                  </div>
                                )
                              },
                              thead({children, ...props}: any) {
                                return (
                                  <thead className="bg-gray-800" {...props}>
                                    {children}
                                  </thead>
                                )
                              },
                              th({children, ...props}: any) {
                                return (
                                  <th className="px-4 py-3 border-b border-gray-600 font-medium text-left" {...props}>
                                    {children}
                                  </th>
                                )
                              },
                              td({children, ...props}: any) {
                                return (
                                  <td className="px-4 py-3 border-b border-gray-600" {...props}>
                                    {children}
                                  </td>
                                )
                              },
                              a({children, ...props}: any) {
                                return (
                                  <a className="text-[#417ef7] hover:underline hover:text-[#141493] transition-colors" target="_blank" rel="noopener noreferrer" {...props}>
                                    {children}
                                  </a>
                                )
                              },
                              p({children, ...props}: any) {
                                return (
                                  <p className="mb-3 text-sm leading-relaxed" {...props}>
                                    {children}
                                  </p>
                                )
                              },
                              h1({children, ...props}: any) {
                                return (
                                  <h1 className="text-xl font-bold mt-5 mb-3 pb-2 border-b border-gray-700" {...props}>
                                    {children}
                                  </h1>
                                )
                              },
                              h2({children, ...props}: any) {
                                return (
                                  <h2 className="text-lg font-bold mt-4 mb-3" {...props}>
                                    {children}
                                  </h2>
                                )
                              },
                              h3({children, ...props}: any) {
                                return (
                                  <h3 className="text-md font-bold mt-3 mb-2" {...props}>
                                    {children}
                                  </h3>
                                )
                              },
                              ul({children, ...props}: any) {
                                return (
                                  <ul className="list-disc list-inside my-3 pl-2 space-y-1" {...props}>
                                    {children}
                                  </ul>
                                )
                              },
                              ol({children, ...props}: any) {
                                return (
                                  <ol className="list-decimal list-inside my-3 pl-2 space-y-1" {...props}>
                                    {children}
                                  </ol>
                                )
                              },
                              li({children, ...props}: any) {
                                return (
                                  <li className="mb-1" {...props}>
                                    {children}
                                  </li>
                                )
                              },
                              blockquote({children, ...props}: any) {
                                return (
                                  <blockquote className="border-l-4 border-[#417ef7] pl-4 my-3 italic bg-gray-800/30 py-2 pr-2 rounded-r" {...props}>
                                    {children}
                                  </blockquote>
                                )
                              },
                              hr({...props}: any) {
                                return (
                                  <hr className="my-4 border-gray-600" {...props} />
                                )
                              },
                              img({...props}: any) {
                                return (
                                  <img className="max-w-full h-auto rounded-md my-3" {...props} alt={props.alt || "Image"} />
                                )
                              }
                            }}
                          >
                            {msg.content}
                          </ReactMarkdown>
                        )}
                        
                        <div className="flex justify-end items-center mt-2 gap-2">
                          <button
                            onClick={() => copyMessageContent(msg.content)}
                            className="opacity-60 hover:opacity-100 transition-all"
                            title="Copy message"
                            aria-label="Copy message"
                          >
                            {copySuccess ? <FiCheck size={14} /> : <FiCopy size={14} />}
                          </button>
                          
                          <span className="text-xs opacity-50">
                            {new Date(msg.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                    {/* Typing indicator with improved styling */}
                    {isProcessing && (
                      <div className="flex justify-start message-animation">
                        <div className="bg-gradient-to-r from-[#111] to-[#1a1a1a] border border-[#333] rounded-xl p-3 shadow-md">
                          <div className="flex space-x-2">
                            <div className="w-2 h-2 rounded-full bg-[#417ef7] typing-dot"></div>
                            <div className="w-2 h-2 rounded-full bg-[#417ef7] typing-dot"></div>
                            <div className="w-2 h-2 rounded-full bg-[#417ef7] typing-dot"></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <div ref={messagesEndRef} />
                </div>
              </div>
            ) : (
              <div className="flex-1 flex items-center justify-center p-6">
                <div className="text-center max-w-2xl mx-auto">
                  {/* AstroStudio Logo */}
                  <div className="mb-8">
                    <div className="flex flex-col items-center">
                       <Image src="/Logo Web.webp" alt="AstroStudio AI" width={200} height={70} className="mb-4" />
                    </div>
                    <h2 className="text-xl text-white mb-4">AI Assistant</h2>
                    <p className="text-gray-400 text-sm max-w-md mx-auto">
                      {t('welcome.subtitle')}
                    </p>
                  </div>
                  
                  {/* Example Prompts */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                    <button
                      onClick={() => setMessage(t('welcome.examples.professionalWritingPrompt'))}
                      className="p-4 bg-[#111] hover:bg-[#1a1a1a] border border-[#333] hover:border-[#417ef7]/50 rounded-lg text-left transition-all group hover:shadow-lg hover:shadow-[#417ef7]/10"
                    >
                      <div className="text-white font-medium mb-1 group-hover:text-[#417ef7] transition-colors">{t('welcome.examples.professionalWriting')}</div>
                      <div className="text-gray-400 text-sm">{t('welcome.examples.professionalWritingPrompt')}</div>
                    </button>

                    <button
                      onClick={() => setMessage(t('welcome.examples.learningEducationPrompt'))}
                      className="p-4 bg-[#111] hover:bg-[#1a1a1a] border border-[#333] hover:border-[#417ef7]/50 rounded-lg text-left transition-all group hover:shadow-lg hover:shadow-[#417ef7]/10"
                    >
                      <div className="text-white font-medium mb-1 group-hover:text-[#417ef7] transition-colors">{t('welcome.examples.learningEducation')}</div>
                      <div className="text-gray-400 text-sm">{t('welcome.examples.learningEducationPrompt')}</div>
                    </button>

                    <button
                      onClick={() => setMessage(t('welcome.examples.creativeWritingPrompt'))}
                      className="p-4 bg-[#111] hover:bg-[#1a1a1a] border border-[#333] hover:border-[#417ef7]/50 rounded-lg text-left transition-all group hover:shadow-lg hover:shadow-[#417ef7]/10"
                    >
                      <div className="text-white font-medium mb-1 group-hover:text-[#417ef7] transition-colors">{t('welcome.examples.creativeWriting')}</div>
                      <div className="text-gray-400 text-sm">{t('welcome.examples.creativeWritingPrompt')}</div>
                    </button>

                    <button
                      onClick={() => setMessage(t('welcome.examples.codeAssistancePrompt'))}
                      className="p-4 bg-[#111] hover:bg-[#1a1a1a] border border-[#333] hover:border-[#417ef7]/50 rounded-lg text-left transition-all group hover:shadow-lg hover:shadow-[#417ef7]/10"
                    >
                      <div className="text-white font-medium mb-1 group-hover:text-[#417ef7] transition-colors">{t('welcome.examples.codeAssistance')}</div>
                      <div className="text-gray-400 text-sm">{t('welcome.examples.codeAssistancePrompt')}</div>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Error message with improved styling */}
          {error && (
            <div className="px-4 py-2.5 mx-4 mb-3 bg-red-500/20 border border-red-500/50 rounded-lg text-red-200 text-xs flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{error}</span>
            </div>
          )}
          
          {/* Model Selector and Input area */}
          <div className="border-t border-[#222] bg-black">
            <div className="max-w-4xl mx-auto p-4">
              {/* Model Selector above text area */}
              <div className="mb-4">

                {/* Model Selector */}
                <div className="relative">
                  <button
                    onClick={() => setIsModelSelectorOpen(!isModelSelectorOpen)}
                    className="w-full bg-[#111] border border-[#222] rounded-lg p-3 text-left flex items-center justify-between hover:border-[#417ef7]/50 transition-colors"
                  >
                    <div className="text-white font-medium">
                      {getSelectedModel()?.name || 'Select a model'}
                    </div>
                    <FiChevronDown className={`text-gray-400 transition-transform ${isModelSelectorOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* Model Dropdown - Drop UP */}
                  <AnimatePresence>
                    {isModelSelectorOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                      >
                        {/* Search Input */}
                        <div className="p-3 border-b border-[#222]">
                          <div className="relative">
                            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <input
                              type="text"
                              placeholder="Search models..."
                              value={modelSearchQuery}
                              onChange={(e) => setModelSearchQuery(e.target.value)}
                              className="w-full bg-[#222] border border-[#333] rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#417ef7]/50"
                            />
                          </div>
                        </div>

                        {/* Models List */}
                        <div className="max-h-[300px] overflow-y-auto">
                          {filteredModels.length > 0 ? (
                            filteredModels.map((model) => (
                              <button
                                key={model.id}
                                onClick={() => handleModelSelect(model.id)}
                                className={`w-full text-left p-3 transition-all border-b border-[#222] last:border-b-0 ${
                                  selectedModel === model.id
                                    ? 'bg-[#417ef7]/20 text-[#417ef7]'
                                    : 'hover:bg-[#222] text-white'
                                }`}
                              >
                                <div className="flex items-center justify-between">
                                  <span className="font-medium">{model.name}</span>
                                  {selectedModel === model.id && (
                                    <span className="text-xs bg-[#417ef7] text-white px-2 py-0.5 rounded-full">
                                      Selected
                                    </span>
                                  )}
                                </div>
                              </button>
                            ))
                          ) : (
                            <div className="p-3 text-center text-gray-400">
                              No models found
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>



              {/* Text Input Area */}
              <form onSubmit={handleSubmit} className="relative">
                <div className="relative">
                  <textarea
                    ref={textareaRef}
                    value={message}
                    onChange={(e) => {
                      setMessage(e.target.value);
                      // Auto-resize
                      e.target.style.height = 'inherit';
                      e.target.style.height = `${Math.min(e.target.scrollHeight, 150)}px`;
                    }}
                    onKeyDown={handleKeyDown}
                    placeholder={t('placeholder')}
                    className="w-full bg-[#111] border border-[#222] rounded-lg py-3 px-4 pr-12 text-gray-200 focus:outline-none focus:ring-1 focus:ring-[#417ef7] resize-none min-h-[80px] max-h-[150px]"
                    rows={3}
                    disabled={isProcessing}
                    aria-label={t('input.messageInput')}
                  />
                  <div className="absolute right-3 bottom-3 flex items-center gap-2">
                    {/* Send button */}
                    <button
                      type="submit"
                      className={`p-2 sm:p-1.5 rounded-lg flex-shrink-0 min-h-[44px] sm:min-h-auto flex items-center justify-center ${message.trim() && !isProcessing
                          ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white hover:opacity-90'
                          : 'bg-[#222] text-gray-500 cursor-not-allowed'
                        } transition-colors`}
                      disabled={!message.trim() || isProcessing}
                      aria-label={t('input.sendMessage')}
                    >
                      {isProcessing ? <ArrowPathIcon className="animate-spin w-4 h-4" /> : <ArrowUpIcon className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </form>

              <div className="flex justify-between items-center mt-2">
                <div className="text-xs text-gray-500 text-center flex-1 hidden md:block">
                  {t('input.instructions')}
                </div>

                {activeConversation && activeConversation.messages.length > 0 && (
                  <button
                    onClick={downloadConversation}
                    className="text-xs text-gray-500 hover:text-gray-300 flex items-center gap-1.5 transition-colors py-2 sm:py-1 px-3 sm:px-2.5 rounded hover:bg-[#222] min-h-[44px] sm:min-h-auto"
                    title={t('conversation.downloadConversation')}
                    aria-label={t('conversation.downloadConversation')}
                  >
                    <FiDownload size={12} />
                    <span className="hidden sm:inline">{t('conversation.download')}</span>
                    <span className="sm:hidden">Save</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
    </div>
  );
}