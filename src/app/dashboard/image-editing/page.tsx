"use client";

import React, { useState, useRef, useEffect } from 'react';
import {FiLoader, FiDownload, FiCopy, FiX, FiUpload, FiInfo, FiChevronDown, FiSend, FiSearch, FiGrid } from "react-icons/fi";
import {PaperClipIcon, ArrowUpIcon } from '@heroicons/react/24/outline';
import { toast } from "sonner";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { modelsByCategory } from "@/lib/models-data";
import { useImageEditingTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import { GeneratedImage } from "@/lib/image-utils";
import { Message, createMessage } from '@/lib/message-utils';
import Image from "next/image";
import { AnimatePresence, motion } from 'framer-motion';

// Custom CSS for modern UI
const customStyles = `
  /* Hide scrollbar for clean UI */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Custom scrollbar for model selection */
  .custom-scrollbar::-webkit-scrollbar {
    width: 5px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 10px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 10px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #444;
  }

  /* Animation for modal */
  @keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .modal-animation {
    animation: modalFadeIn 0.2s ease-out;
  }

  /* Format button hover effects */
  .format-btn:hover {
    background-color: #222;
  }
  
  .format-btn.selected {
    background: linear-gradient(to right, #141493, #417ef7);
    color: white;
  }
  
  /* Image preview animation */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .image-animation {
    animation: fadeIn 0.5s ease-out;
  }
  
  /* Loading pulsing animation */
  @keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
  }
  
  .loading-pulse {
    animation: pulse 1.5s infinite ease-in-out;
  }

  /* Progress bar animation */
  @keyframes progress {
    0% { width: 0%; }
    100% { width: 100%; }
  }

  .animate-progress {
    animation: progress 2s linear infinite;
  }
  
  /* Checker background for transparent images */
  .bg-checkerboard {
    background-image: linear-gradient(45deg, #111 25%, transparent 25%), 
                      linear-gradient(-45deg, #111 25%, transparent 25%), 
                      linear-gradient(45deg, transparent 75%, #111 75%), 
                      linear-gradient(-45deg, transparent 75%, #111 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: #1a1a1a;
  }
  
  /* Message bubble styles */
  .message-bubble-user {
    background-color: #1e1e1e;
    border-radius: 18px 18px 0 18px;
    color: white;
    padding: 12px 16px;
  }
  
  .message-bubble-ai {
    background: linear-gradient(135deg, #141493, #417ef7);
    border-radius: 18px 18px 18px 0;
    color: #ffffff;
    padding: 12px 16px;
  }
  
  /* Model selector styles */
  .model-selector {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 8px 12px;
    transition: all 0.2s ease;
  }
  
  .model-selector:hover {
    background-color: #222;
    border-color: #444;
  }
  
  .model-dropdown {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }
  
  .model-option {
    padding: 8px 12px;
    transition: all 0.2s ease;
  }
  
  .model-option:hover {
    background-color: #222;
  }
  
  .model-option.selected {
    background: linear-gradient(to right, #141493, #417ef7);
  }
  
  /* File upload drop zone styling */
  .dropzone {
    border: 2px dashed #333;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #111;
  }
  
  .dropzone:hover, .dropzone.active {
    border-color: #417ef7;
    background-color: rgba(65, 126, 247, 0.05);
  }
  
  .dropzone-icon {
    margin-bottom: 15px;
    color: #417ef7;
  }
`;

// Define free tier models
const FREE_TIER_MODELS = ["cjwbw/rembg"]; // Background removal is free
const FREE_TIER_UPSCALE_MODELS = ["nightmareai/real-esrgan"]; // One upscaler is free

// Define generation modes
type GenerationMode = 'image-generation' | 'image-editing' | 'upscale-image';

// Define the response structure
interface EditImageResponse {
  imageUrl?: string;
  images?: string[];
  error?: string;
  prompt?: string;
  negativePrompt?: string;
  model?: string;
  provider?: string;
}

// Helper function to get user-friendly category descriptions
const getCategoryDescription = (category: string): string => {
  switch (category) {
    case "Quick Generation":
      return "Fast image creation";
    case "Photorealistic":
      return "Realistic image quality";
    case "Artistic":
      return "Creative and stylized";
    case "Specialized":
      return "Domain-specific models";
    case "Image Editing":
      return "Edit existing images";
    case "Upscaling Models":
      return "Enhance resolution";
    default:
      return category;
  }
};

export default function ImageEditing() {
  const router = useRouter();

  // Translation hooks
  const t = useImageEditingTranslations();
  const common = useCommonTranslations();

  // Subscription validation
  const { subscription, loading: subscriptionLoading } = useSubscription();

  // Default settings
  const defaultCategory = "Image Editing";
  const defaultModelId = "cjwbw/rembg"; // Default to background removal
  const defaultUpscaleModelId = "nightmareai/real-esrgan"; // Default upscale model

  // Mode selection state
  const [mode, setMode] = useState<GenerationMode>('image-editing');

  // State for the UI
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [editedImages, setEditedImages] = useState<GeneratedImage[]>([]);
  const [selectedImage, setSelectedImage] = useState<GeneratedImage | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState<boolean>(false);
  const [modelSearchQuery, setModelSearchQuery] = useState<string>('');

  // State for editing parameters
  const [selectedModel, setSelectedModel] = useState<string>(defaultModelId);
  const [prompt, setPrompt] = useState<string>("");
  const [negativePrompt, setNegativePrompt] = useState<string>("");
  const [showAdvancedOptions, setShowAdvancedOptions] = useState<boolean>(false);
  const [activeCategory, setActiveCategory] = useState<string>(defaultCategory);

  // Upload related state
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Effect to handle drag and drop overlay
  useEffect(() => {
    const dragOverlay = document.getElementById('drag-overlay');
    if (!dragOverlay) return;
    
    const handleDragEnter = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      dragOverlay.style.opacity = '1';
      dragOverlay.style.pointerEvents = 'auto';
    };
    
    const handleDragLeave = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.relatedTarget === null || !document.getElementById('app-container')?.contains(e.relatedTarget as Node)) {
        dragOverlay.style.opacity = '0';
        dragOverlay.style.pointerEvents = 'none';
      }
    };
    
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };
    
    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      
      dragOverlay.style.opacity = '0';
      dragOverlay.style.pointerEvents = 'none';
      
      const file = e.dataTransfer?.files?.[0];
      if (!file) return;
      
      // Check file type
      if (!file.type.startsWith("image/")) {
        toast.error(t('errors.imageFileOnly'));
        return;
      }
      
      // Check file size
      if (file.size > 10 * 1024 * 1024) {
        toast.error(t('errors.fileSizeLimit'));
        return;
      }
      
      // Process the dropped file
      setUploadStatus("uploading");
      
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          const result = event.target.result as string;
          setUploadedImage(result);
          setUploadStatus("success");
          setError("");
        }
      };
      
      reader.onerror = () => {
        setUploadStatus("error");
        setError(t('errors.failedToReadFile'));
      };
      
      reader.readAsDataURL(file);
    };
    
    // Add event listeners to the document
    document.addEventListener('dragenter', handleDragEnter);
    document.addEventListener('dragleave', handleDragLeave);
    document.addEventListener('dragover', handleDragOver);
    document.addEventListener('drop', handleDrop);
    
    // Cleanup
    return () => {
      document.removeEventListener('dragenter', handleDragEnter);
      document.removeEventListener('dragleave', handleDragLeave);
      document.removeEventListener('dragover', handleDragOver);
      document.removeEventListener('drop', handleDrop);
    };
  }, []);

  // Get editing-specific categories only
  const getEditingCategories = () => {
    return ["Image Editing"];
  };

  // Get upscaling-specific categories only
  const getUpscalingCategories = () => {
    return ["Upscaling Models"];
  };

  // Handle mode change
  const handleModeChange = (newMode: GenerationMode) => {
    if (newMode === mode) return;

    // If switching to image-generation, navigate to the appropriate page
    if (newMode === 'image-generation') {
      router.push('/dashboard/image-generation');
      return;
    }
    
    // If switching to upscale-image
    if (newMode === 'upscale-image') {
      setMode('upscale-image');
      // Switch to upscale model
      const upscalingModels = modelsByCategory["Upscaling Models"];
      if (upscalingModels && upscalingModels.length > 0) {
        setSelectedModel(upscalingModels[0].id);
      }
      return;
    }

    setMode(newMode);

    // Reset errors and uploaded image
    setError("");
    setUploadedImage(null);
    setUploadStatus("idle");

    // Set default category and model for the new mode
    if (newMode === 'image-editing') {
      setActiveCategory("Image Editing");
      setSelectedModel(defaultModelId);
    }
  };

  // Check if a model is available for free tier
  const isModelAvailableForFreeTier = (modelId: string): boolean => {
    if (mode === 'image-editing') {
      return FREE_TIER_MODELS.includes(modelId);
    } else if (mode === 'upscale-image') {
      return FREE_TIER_UPSCALE_MODELS.includes(modelId);
    }
    return false;
  };

  // Get the currently selected model's provider
  const getSelectedModelProvider = (): string => {
    for (const category of Object.keys(modelsByCategory)) {
      const model = modelsByCategory[category].find(
        (m) => m.id === selectedModel,
      );
      if (model) {
        return model.provider;
      }
    }
    return "Replicate"; // Default provider
  };

  // Handle model change with subscription check
  const handleModelChange = (modelId: string) => {
    // Simply set the selected model without additional filtering
    // The API already checks permissions based on the user's subscription
    setSelectedModel(modelId);

    // Show a notification for premium models if user is not subscribed
    if (!subscription && !isModelAvailableForFreeTier(modelId)) {
      toast.info(
        <div className="flex flex-col gap-1">
          <span className="font-semibold">Premium Model Selected</span>
          <span className="text-sm">You'll need a subscription to use this model</span>
          <Link href="/dashboard/subscription" className="text-xs text-purple-300 underline mt-1">
            View Subscription Plans
          </Link>
        </div>
      );
    }
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith("image/")) {
      toast.error(t('errors.imageFileOnly'));
      return;
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error(t('errors.fileSizeLimit'));
      return;
    }

    setUploadStatus("uploading");
    toast.loading(t('loading.uploadingImage'));

    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        const result = event.target.result as string;
        setUploadedImage(result);
        setUploadStatus("success");
        setError("");
        toast.dismiss();
        toast.success(t('success.imageUploaded'));
      }
    };

    reader.onerror = () => {
      setUploadStatus("error");
      setError(t('errors.failedToReadFile'));
      toast.dismiss();
      toast.error(t('errors.failedToReadFile'));
    };

    reader.readAsDataURL(file);
  };

  // Clear uploaded image
  const clearUploadedImage = () => {
    setUploadedImage(null);
    setUploadStatus("idle");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Process image
  const handleProcessImage = async (promptText?: string) => {
    if (!uploadedImage) {
      toast.error(t('errors.uploadImageFirst'));
      return;
    }

    // Use provided prompt or existing prompt state
    const finalPrompt = promptText ?? prompt;

    // Check if user has access to this model
    if (!subscription && !isModelAvailableForFreeTier(selectedModel)) {
      toast.error(
        <div className="flex flex-col gap-1">
          <span className="font-semibold">{t('subscriptionRequired')}</span>
          <span className="text-sm">{t('subscriptionRequiredDescription')}</span>
          <Link href="/dashboard/subscription" className="text-xs text-purple-300 underline mt-1">
            {t('viewSubscriptionPlans')}
          </Link>
        </div>
      );
      return;
    }

    // For models that need prompts (non-background removal)
    // Background removal doesn't need a prompt
    const needsPrompt = selectedModel !== "cjwbw/rembg" && !finalPrompt.trim();

    if (mode === 'image-editing' && needsPrompt) {
      toast.error(t('errors.enterPromptForModel'));
      return;
    }
    
    // If a prompt was provided, add it to the messages
    if (promptText && promptText.trim() !== "") {
      setMessages(prev => [...prev, createMessage(promptText, true)]);
    }

    try {
      setError("");
      setIsProcessing(true);

      // Prepare request data
      const requestData: any = {
        model: selectedModel,
        provider: getSelectedModelProvider(),
        image: uploadedImage,
        feature: mode === 'image-editing' ? "IMAGE_EDITING" : "IMAGE_UPSCALING"
      };

      // Add prompt and negative_prompt for models that need them
      if (selectedModel !== "cjwbw/rembg" && mode === 'image-editing') {
        requestData.prompt = finalPrompt.trim();
        if (negativePrompt.trim()) {
          requestData.negative_prompt = negativePrompt.trim();
        }
      }

      // Choose the appropriate API endpoint based on the mode
      const endpoint = mode === 'image-editing' ? '/api/generate/edit' : '/api/generate/upscale';

      // Process image with the API endpoint
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to process image');
      }

      const data = await response.json() as EditImageResponse;

      // Extract the image URL from the response
      let imageUrl = '';
      if (typeof data.imageUrl === 'string') {
        imageUrl = data.imageUrl;
      } else if (Array.isArray(data.images) && data.images.length > 0) {
        const firstImage = data.images[0];
        if (typeof firstImage === 'string') {
          imageUrl = firstImage;
        }
      }

      if (!imageUrl) {
        throw new Error(t('errors.noImageInResponse'));
      }

      // Create a new processed image object
      const newImage: GeneratedImage = {
        id: Date.now(),
        url: imageUrl,
        prompt: data.prompt || finalPrompt,
        negativePrompt: data.negativePrompt || negativePrompt,
        model: data.model || selectedModel,
        width: 0,
        height: 0,
        timestamp: new Date(),
      };

      // Add to processed images and set selected image
      setEditedImages(prev => [newImage, ...prev]);
      setSelectedImage(newImage);
      
      // Add the processed image to the chat
      const processedContent = (
        <div className="flex flex-col gap-2">
          <div className="relative bg-checkerboard rounded overflow-hidden">
            <img 
              src={imageUrl} 
              alt="Processed image"
              className="max-h-[300px] w-full object-contain"
            />
          </div>
          <div className="flex justify-end mt-1 gap-2">
            {data.prompt && (
              <button
                onClick={() => copyPrompt(data.prompt!)}
                className="p-1 rounded-md bg-gray-700 hover:bg-gray-600 text-xs flex items-center gap-1 text-gray-200"
              >
                <FiCopy size={14} /> Copy prompt
              </button>
            )}
            <button
              onClick={() => downloadImage(imageUrl)}
              className="p-1 rounded-md bg-gray-700 hover:bg-gray-600 text-xs flex items-center gap-1 text-gray-200"
            >
              <FiDownload size={14} /> Download
            </button>
          </div>
        </div>
      );
      
      setMessages(prev => [
        ...prev,
        createMessage(
          processedContent,
          false,
          data.provider || getSelectedModelProvider(),
          data.model || selectedModel
        )
      ]);

      // Save image to database for user history if user is subscribed
      if (subscription) {
        setIsSaving(true);
        await saveImageToSupabase(newImage.url);
        setIsSaving(false);
      }

    } catch (err: any) {
      console.error('Image processing error:', err);
      setError(err.message || t('errors.failedToProcessImage'));
      
      // Add error message to chat
      setMessages(prev => [
        ...prev,
        createMessage(
          <div className="text-red-300">
            Error: {err.message || t('errors.failedToProcessImage')}
          </div>,
          false,
          "System"
        )
      ]);
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle prompt submission for chat interface
  const handlePromptSubmit = async (promptText: string) => {
    if (!uploadedImage) {
      // If no image is uploaded, show a helpful message
      setMessages(prev => [
        ...prev,
        createMessage(promptText, true),
        createMessage(
          <div>
            {t('studio.uploadFirstMessage')}
          </div>,
          false,
          "System"
        )
      ]);
      return;
    }
    
    // Process the image with the provided prompt
    await handleProcessImage(promptText);
  };

  // Save processed image to Supabase
  const saveImageToSupabase = async (imageUrl: string): Promise<string | null> => {
    try {
      const response = await fetch('/api/user/save-generated-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          prompt,
          model: selectedModel,
          isEdited: true,
        }),
      });

      if (!response.ok) {
        throw new Error(t('errors.failedToSaveImage'));
      }

      // Return the image URL on success
      return imageUrl;
    } catch (error) {
      console.error('Error saving image to user history:', error);
      return null;
    }
  };

  // Copy prompt to clipboard
  const copyPrompt = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(t('success.promptCopied'));
    } catch (err) {
      console.error('Failed to copy text:', err);
      setError(t('errors.failedToCopyText'));
    }
  };

  // Download image
  const downloadImage = async (url: string) => {
    try {
      // Use the download-image API to handle CORS issues
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl: url }),
      });

      if (!response.ok) {
        throw new Error(t('errors.failedToFetchImageData'));
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || t('errors.failedToDownloadImage'));
      }

      // Create a temporary link to download the data URL
      const a = document.createElement('a');
      a.href = data.dataUrl;
      a.download = `edited-image-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading image:', err);
      toast.error(t('errors.failedToDownloadImage'));
    }
  };

  // Get preview content for the chat interface
  const previewContent = selectedImage ? (
    <div className="bg-checkerboard rounded-lg overflow-hidden">
      <img
        src={selectedImage.url}
        alt={selectedImage.prompt ?? "Processed image"}
        className="max-h-[300px] mx-auto object-contain"
      />
      <div className="flex justify-end mt-2 gap-2">
        <button
          onClick={() => selectedImage.prompt && copyPrompt(selectedImage.prompt)}
          className="p-1 rounded-md bg-gray-800 hover:bg-gray-700 text-gray-300 transition-colors text-xs flex items-center gap-1"
          title="Copy prompt"
        >
          <FiCopy size={14} /> Copy prompt
        </button>
        <button
          onClick={() => downloadImage(selectedImage.url)}
          className="p-1 rounded-md bg-gray-800 hover:bg-gray-700 text-gray-300 transition-colors text-xs flex items-center gap-1"
          title="Download image"
        >
          <FiDownload size={14} /> Download
        </button>
      </div>
    </div>
  ) : uploadedImage ? (
    <div className="bg-checkerboard rounded-lg overflow-hidden">
      <img
        src={uploadedImage}
        alt="Uploaded image"
        className="max-h-[300px] mx-auto object-contain"
      />
      <div className="flex justify-end mt-2">
        <button
          onClick={clearUploadedImage}
          className="p-1 rounded-md bg-gray-800 hover:bg-gray-700 text-gray-300 transition-colors text-xs flex items-center gap-1"
        >
          <FiX size={14} /> Remove
        </button>
      </div>
    </div>
  ) : null;

  // Enhanced file upload with clipboard paste support
  const handlePaste = (e: React.ClipboardEvent) => {
    const items = e.clipboardData.items;
    for (let i = 0; i < items.length; i++) {
      if (items[i].type.indexOf('image') !== -1) {
        const blob = items[i].getAsFile();
        if (!blob) continue;
        
        // Check file size
        if (blob.size > 10 * 1024 * 1024) {
          toast.error(t('errors.fileSizeLimit'));
          return;
        }
        
        setUploadStatus("uploading");
        toast.loading(t('loading.processingPastedImage'));
        
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target?.result) {
            const result = event.target.result as string;
            setUploadedImage(result);
            setUploadStatus("success");
            setError("");
            toast.dismiss();
            toast.success(t('success.imagePasted'));
            
          }
        };
        
        reader.onerror = () => {
          setUploadStatus("error");
          setError(t('errors.failedToReadFile'));
          toast.dismiss();
          toast.error(t('errors.failedToReadFile'));
        };
        
        reader.readAsDataURL(blob);
        break;
      }
    }
  };

  // If loading, show a loading indicator
  if (subscriptionLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <FiLoader className="mx-auto h-8 w-8 animate-spin text-gray-500" />
          <p className="mt-2 text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <style jsx global>{customStyles}</style>
      <div id="app-container" className="h-full flex flex-col">
        {/* Main content area */}
        <div className="flex-1 min-h-0 overflow-hidden">
          <div className="h-full flex flex-col bg-black rounded-xl">
            <div className="flex items-center justify-between p-4 border-b border-[#222]">
              <h1 className="text-xl font-medium text-white">
                {mode === 'image-editing' ? t('editImage') : t('upscaleImage')}
              </h1>
              
              <div className="flex items-center space-x-2">
                {/* Mode switcher buttons */}
                <div className="bg-[#111] rounded-lg p-1 flex">
            <button
                    className={`px-3 py-1.5 rounded-md text-sm ${
                      mode === 'image-editing'
                        ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white'
                        : 'text-gray-400 hover:text-white'
                }`}
              onClick={() => handleModeChange('image-editing')}
            >
                    Edit
            </button>
            <button
                    className={`px-3 py-1.5 rounded-md text-sm ${
                      mode === 'upscale-image'
                        ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white'
                        : 'text-gray-400 hover:text-white'
                }`}
              onClick={() => handleModeChange('upscale-image')}
            >
                    Upscale
            </button>
        </div>

                {/* Upload button */}
                <label className="bg-[#222] hover:bg-[#333] text-gray-300 px-3 py-1.5 rounded-lg cursor-pointer transition-colors text-sm flex items-center">
                  <FiUpload className="mr-2" />
                  Upload
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleFileUpload}
                          ref={fileInputRef}
                        />
                      </label>
                </div>
              </div>

            {/* Settings panel */}
            <div className="bg-[#111] border-b border-[#222] p-4">
              <div className="flex flex-wrap items-center justify-between gap-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">Mode:</span>
                  <span className="text-sm text-white bg-[#222] px-2 py-1 rounded">
                    {mode === 'image-editing' ? 'Edit' : 'Upscale'}
                  </span>
                </div>

              {mode === 'image-editing' && selectedModel !== "cjwbw/rembg" && (
                  <button
                    className="bg-[#222] hover:bg-[#333] text-gray-300 px-3 py-1.5 rounded-lg text-sm flex items-center"
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  >
                    <FiGrid className="mr-2" />
                    {showAdvancedOptions ? "Hide" : "Show"} Options
                  </button>
                )}
              </div>
            </div>
            
            {/* File upload area - show only when no image is uploaded */}
            {!uploadedImage && (
              <div className="p-4 flex-1 flex items-center justify-center">
                <div className="max-w-2xl mx-auto text-center">
                  <div className="flex flex-col items-center">
                    <Image src="/Logo Web.webp" alt="AstroStudio AI" width={200} height={70} className="mb-4" />
                    <h3 className="text-xl font-medium text-white mb-4">{t('studio.title')}</h3>
                    <p className="text-gray-400 mb-6">
                      {t('studio.uploadInstructions')}
                    </p>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <FiInfo className="text-gray-400" />
                      <span>{t('studio.pasteInstructions')}</span>
                    </div>
                  </div>
                      </div>
                    </div>
                  )}
            
            {/* Chat interface - direct implementation */}
            <div className="flex-1 bg-black overflow-y-auto custom-scrollbar">
              {/* Messages */}
              <div className="space-y-6 px-4 py-4 overflow-y-auto auto-scroll custom-scrollbar">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} max-w-full`}
                  >
                    <div className={`flex ${message.isUser ? 'flex-row-reverse' : 'flex-row'} items-start gap-3 max-w-[90%]`}>
                      {/* Message content */}
                      <div
                        className={`${
                          message.isUser
                            ? 'message-bubble-user'
                            : 'message-bubble-ai'
                        } max-w-full overflow-hidden`}
                      >
                        <div className="break-words">
                          {message.content}
                        </div>
                        {!message.isUser && message.provider && (
                          <div className="mt-2 text-xs text-gray-400">
                            Generated with {message.provider}
                            {message.model && ` • ${message.model}`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Loading indicator */}
                {isProcessing && (
                  <div className="flex justify-start max-w-full">
                    <div className="flex flex-row items-start gap-3 max-w-[90%]">
                      <div className="message-bubble-ai">
                        <div className="flex space-x-2 items-center h-5">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                        </div>
                      </div>
                    </div>
                </div>
              )}
            </div>
          </div>

            {/* Model selector and input area */}
            <div className="border-t border-[#222] p-4">
              {/* Model selector */}
              <div className="relative">
                      <button
                  onClick={() => setIsModelSelectorOpen(!isModelSelectorOpen)}
                  className="flex items-center justify-between w-full px-3 py-2 rounded-lg bg-[#111] hover:bg-[#181818] transition-colors mb-3"
                      >
                  <div className="flex items-center gap-2 ">
                    <div className="w-6 h-6 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                      <span className="text-xs font-semibold text-white">
                        AI
                      </span>
                  </div>
                    <span className="text-sm text-gray-200">
                      {(() => {
                        for (const category of Object.keys(modelsByCategory)) {
                          const model = modelsByCategory[category].find(m => m.id === selectedModel);
                          if (model) return model.name;
                        }
                        return "Select a model";
                      })()}
                    </span>
                </div>
                  <div className="flex items-center gap-2">
                    <FiChevronDown className={`text-gray-400 transition-transform ${isModelSelectorOpen ? 'rotate-180' : ''}`} />
                  </div>
                </button>
              
                {/* Model selector dropdown */}
                <AnimatePresence>
                  {isModelSelectorOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                    >
                      {/* Model search */}
                      <div className="p-2 border-b border-[#222]">
                        <div className="relative">
                          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                          <input
                            type="text"
                            value={modelSearchQuery}
                            onChange={(e) => setModelSearchQuery(e.target.value)}
                            placeholder="Search models..."
                            className="w-full bg-[#181818] text-gray-300 pl-9 pr-3 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                    />
                  </div>
                </div>

                      {/* Model list */}
                      <div className="p-2 max-h-[350px] overflow-y-auto custom-scrollbar">
                        {(mode === 'image-editing' ? 
                          modelsByCategory["Image Editing"] : 
                          modelsByCategory["Upscaling Models"])
                          ?.filter(model => 
                            !modelSearchQuery || 
                            model.name.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                            model.provider.toLowerCase().includes(modelSearchQuery.toLowerCase())
                          )
                          .map((model) => (
                          <button
                            key={model.id}
                            className={`p-3 rounded-lg text-left transition w-full ${
                              selectedModel === model.id
                                ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                : "hover:bg-[#222] text-gray-300"
                            } ${!subscription && !isModelAvailableForFreeTier(model.id) ? 'opacity-50' : ''}`}
                            onClick={() => {
                              if (!subscription && !isModelAvailableForFreeTier(model.id)) {
                                toast.info(
                                  <div className="flex flex-col gap-1">
                                    <span className="font-semibold">Premium Model</span>
                                    <span className="text-sm">This model requires a subscription</span>
                                    <Link href="/dashboard/subscription" className="text-xs text-blue-300 underline mt-1">
                                      View Subscription Plans
                                    </Link>
                    </div>
                                );
                                return;
                              }
                              handleModelChange(model.id);
                              setIsModelSelectorOpen(false);
                            }}
                          >
                            <div className="flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                                  <span className="text-xs font-semibold text-white">
                                    AI
                                  </span>
                  </div>
                                <div>
                                  <div className="text-sm font-medium">{model.name}</div>
                                  <div className="text-xs text-gray-400">{getCategoryDescription(mode === 'image-editing' ? "Image Editing" : "Upscaling Models")}</div>
              </div>
                    </div>
                              <div className="flex items-center gap-2">
                                {!subscription && !isModelAvailableForFreeTier(model.id) && (
                                  <span className="text-xs px-2 py-1 rounded-full bg-[#222] text-gray-400">Premium</span>
                                )}
                                <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                  {selectedModel === model.id && (
                                    <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                  )}
                  </div>
                  </div>
                </div>
                          </button>
                        ))}
              </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              
              {/* Input form with file upload */}
              <form 
                onSubmit={(e) => {
                  e.preventDefault();
                  if (!prompt.trim() || isProcessing || !uploadedImage) return;
                  handlePromptSubmit(prompt);
                  setPrompt('');
                }} 
                className="relative"
              >
                <div className="flex flex-col">
                  {/* Image preview area when image is uploaded */}
                  {uploadedImage && (
                    <div className="mb-3 bg-checkerboard rounded-lg overflow-hidden">
                      <div className="relative">
                      <img
                          src={uploadedImage} 
                          alt="Uploaded image"
                          className="max-h-[200px] mx-auto object-contain"
                      />
                        <button
                          onClick={() => clearUploadedImage()}
                          className="absolute top-2 right-2 p-1 bg-black bg-opacity-70 rounded-full hover:bg-opacity-100 transition-all"
                          type="button"
                        >
                          <FiX size={16} className="text-white" />
                        </button>
                      </div>
                    </div>
                  )}
                  
                  <div className="relative flex">
                    <textarea
                      className="w-full bg-[#111] border border-[#222] rounded-lg py-3 px-4 pr-24 text-gray-200 focus:outline-none focus:ring-1 focus:ring-[#417ef7] resize-none min-h-[80px] max-h-[150px]"
                      placeholder={uploadedImage
                        ? t('studio.placeholderWithImage')
                        : t('studio.placeholderWithoutImage')}
                      value={prompt}
                      onChange={(e) => {
                        setPrompt(e.target.value);
                        // Auto-resize
                        e.target.style.height = 'inherit';
                        e.target.style.height = `${Math.min(e.target.scrollHeight, 150)}px`;
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          if (!prompt.trim() || isProcessing || !uploadedImage) return;
                          handlePromptSubmit(prompt);
                          setPrompt('');
                          e.currentTarget.style.height = 'auto';
                        }
                      }}
                      disabled={isProcessing || !uploadedImage}
                      rows={3}
                      onPaste={handlePaste}
                    />
                    <div className="absolute right-3 bottom-3 flex items-center gap-2">
                      {/* Upload button in the prompt area */}
                      <label className="p-2 sm:p-1.5 rounded-lg cursor-pointer hover:bg-[#222] transition-colors min-h-[44px] sm:min-h-auto flex items-center justify-center">
                        <PaperClipIcon className="w-4 h-4 text-gray-400" />
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleFileUpload}
                          ref={fileInputRef}
                        />
                      </label>

                      {/* Send button */}
                      <button
                        type="submit"
                        className={`p-2 sm:p-1.5 rounded-lg flex-shrink-0 min-h-[44px] sm:min-h-auto flex items-center justify-center ${
                          prompt.trim() && !isProcessing && uploadedImage
                            ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white hover:opacity-90'
                            : 'bg-[#222] text-gray-500 cursor-not-allowed'
                        } transition-colors`}
                        disabled={!prompt.trim() || isProcessing || !uploadedImage}
                      >
                        <ArrowUpIcon className="w-4 h-4" />
                      </button>
                </div>
              </div>
          </div>
                <div className="text-xs text-gray-500 mt-2 text-center">
                  {!uploadedImage
                    ? "Upload an image by pasting, dropping, or clicking the upload icon"
                    : <span className="hidden md:inline">Press Enter to send, Shift+Enter for new line</span>}
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 