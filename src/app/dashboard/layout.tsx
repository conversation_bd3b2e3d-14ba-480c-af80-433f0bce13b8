"use client";

import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useState, useEffect, useMemo, useCallback } from 'react';
import {
    Bars3Icon,
    XMarkIcon,
    ArrowRightOnRectangleIcon,
    UserIcon,
    PhotoIcon,
    MicrophoneIcon,
    VideoCameraIcon,
    MusicalNoteIcon,
    CodeBracketSquareIcon,
    HomeIcon,
    PlayCircleIcon,
    CreditCardIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
    SpeakerWaveIcon
} from '@heroicons/react/24/outline';
import { FiEdit3 } from 'react-icons/fi';
import DynamicTitle from '@/lib/DynamicTitle';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { useSession, signOut } from 'next-auth/react';
import { useNavigationTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import { useLocale, createLocaleUrl as createLocaleUrlHelper } from '@/hooks/useLocale';

interface User {
    id: string;
    name: string;
    email: string;
    role: string;
}

// User Profile Dropdown Component
const UserProfileDropdown = ({ createLocaleUrl, common }: { createLocaleUrl: (path: string) => string; common: any }) => {
    const { data: session } = useSession();

    return (
        <div className="relative">
            <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-[#141493] to-[#417ef7] text-white hover:bg-[#181818] cursor-pointer transition-colors">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#141493] to-[#417ef7] border border-white flex items-center justify-center">
                    <UserIcon className="text-gray-300" width={16} height={16} />
                </div>
                <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-white truncate">
                        {session?.user?.name }
                    </p>
                    <p className="text-xs text-white/70 truncate">
                        {session?.user?.email}
                    </p>
                </div>
            </div>

            <button
                onClick={() => signOut({ callbackUrl: createLocaleUrl('/') })}
                className="mt-2 w-full flex items-center space-x-3 p-3 rounded-lg text-gray-400 hover:bg-[#111] hover:text-white transition-colors"
            >
                <ArrowRightOnRectangleIcon width={16} height={16} />
                <span>{common('logout')}</span>
            </button>
        </div>
    );
};

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
    const pathname = usePathname() || '';
    const [isOpen, setIsOpen] = useState(true);
    const { subscription } = useSubscription();
    const { data: session } = useSession();

    const nav = useNavigationTranslations();
    const common = useCommonTranslations();
    const locale = useLocale();

    // Create locale-aware URLs
    const createLocaleUrl = (path: string) => {
        return createLocaleUrlHelper(path, locale);
    };



    // Use useEffect to handle responsive behavior on initial load
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 768) {
                setIsOpen(false); // Start with closed sidebar on mobile
            } else {
                setIsOpen(true); // Start with open sidebar on desktop
            }
        };

        // Set initial state
        handleResize();

        // Add event listener
        window.addEventListener('resize', handleResize);

        // Cleanup
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    // Memoize the toggle function to prevent unnecessary re-renders
    const toggleSidebar = useCallback(() => {
        setIsOpen(!isOpen);
    }, [isOpen]);

    // Memoize the navigation array to prevent unnecessary re-renders
    const navigation = useMemo(() => [
        { name: nav('dashboard'), href: createLocaleUrl('/dashboard'), icon: HomeIcon },
        { name: nav('imageGeneration'), href: createLocaleUrl('/dashboard/image-generation'), icon: PhotoIcon },
        { name: nav('imageEditing'), href: createLocaleUrl('/dashboard/image-editing'), icon: FiEdit3 },
        { name: nav('videoGeneration'), href: createLocaleUrl('/dashboard/video-generation'), icon: PlayCircleIcon },
        { name: nav('musicGeneration'), href: createLocaleUrl('/dashboard/music-generation'), icon: MusicalNoteIcon },
        { name: nav('podcastGeneration'), href: createLocaleUrl('/dashboard/podcast-generation'), icon: MicrophoneIcon },
        { name: nav('aiAssistant'), href: createLocaleUrl('/dashboard/assistant'), icon: CodeBracketSquareIcon },
        { name: nav('subscription'), href: createLocaleUrl('/dashboard/subscription'), icon: CreditCardIcon },
    ], [nav, createLocaleUrl]);

    return (
        <div className="flex h-screen bg-black">
            {/* Add DynamicTitle component to update title */}
            <DynamicTitle />
            
            {/* Fixed mobile header with menu button */}
            <div className="fixed top-0 left-0 right-0 h-14 bg-black md:hidden z-40 flex items-center justify-between px-4">
                <button
                    className="p-2 rounded-md text-gray-400 hover:text-white"
                    onClick={toggleSidebar}
                >
                    {isOpen ? <XMarkIcon width={24} height={24} /> : <Bars3Icon width={24} height={24} />}
                </button>

                <Link href={createLocaleUrl('/dashboard')} className="flex items-center">
                    <Image src="/Logo Web.webp" alt="AstroStudio AI" width={120} height={40} />
                </Link>
            </div>

            {/* Sidebar - sleek modern design with no scroll */}
            <div
                className={`fixed inset-y-0 left-0 z-30 bg-black transition-all duration-300 transform ${
                    isOpen ? 'w-[260px]' : 'w-0 md:w-[70px]'
                } md:relative md:z-10 shadow-xl no-scrollbar`}
            >
                <div className="flex flex-col h-full no-scrollbar">
                    <div className={`flex items-center h-14 ${isOpen ? 'justify-between px-4' : 'justify-center px-2'} transition-all duration-300`}>
                        <Link href={createLocaleUrl('/dashboard')} className={`flex items-center ${!isOpen ? 'justify-center w-12 h-12' : ''}`}>
                            <div className={`flex items-center overflow-hidden ${isOpen ? 'w-auto opacity-100' : 'w-0 opacity-0'} transition-all duration-300`}>
                                <Image src="/Logo Web.webp" alt="AstroStudio AI" width={150} height={50} className="h-8 w-auto object-contain" />
                            </div>
                            <div className={`${isOpen ? 'hidden' : 'hidden md:flex md:items-center md:justify-center'}`}>
                                <Image src="/Logo Perfil.png" alt="AstroStudio AI" width={28} height={28} className="h-7 w-7 object-contain" />
                            </div>
                        </Link>

                        {/* Desktop sidebar collapse button - only show when open */}
                        {isOpen && (
                            <button
                                className="hidden md:block text-gray-400 hover:text-white transition-colors"
                                onClick={toggleSidebar}
                                title="Collapse sidebar"
                            >
                                <ChevronLeftIcon width={20} height={20} />
                            </button>
                        )}
                </div>

                    {/* Expand button for collapsed sidebar */}
                    {!isOpen && (
                        <div className="hidden md:flex justify-center py-2">
                            <button
                                onClick={toggleSidebar}
                                className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-[#111] transition-colors"
                                title="Expand sidebar"
                            >
                                <ChevronRightIcon width={16} height={16} />
                            </button>
                        </div>
                    )}

                    {/* Navigation - clean, modern style with no scroll */}
                    <div className={`flex-1 no-scrollbar ${isOpen ? 'px-2 py-4' : 'px-1 py-2'} ${isOpen ? 'block' : 'hidden md:block'}`}>
                        <nav className={`space-y-1 no-scrollbar ${!isOpen ? 'flex flex-col items-center' : ''}`}>
                    {navigation.map((item) => {
                                // Check if current path matches the navigation item
                                const isActive = pathname === item.href ||
                                                (item.href !== createLocaleUrl('/dashboard') && pathname.startsWith(item.href));
                        return (
                            <div key={item.name} className="relative group">
                                <Link
                                    href={item.href}
                                            className={`flex items-center rounded-lg transition-all ${
                                                isOpen
                                                    ? 'px-3 py-2.5 text-sm'
                                                    : 'p-3 justify-center w-12 h-12'
                                            } ${
                                                isActive
                                                    ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white font-medium'
                                                    : 'text-gray-400 hover:text-white hover:bg-[#111]'
                                            }`}
                                        >
                                            <item.icon className={`flex-shrink-0 h-5 w-5 ${isOpen ? 'mr-3' : ''}`} />
                                            <span className={`whitespace-nowrap ${isOpen ? 'opacity-100' : 'opacity-0 w-0 hidden'} transition-opacity duration-300`}>
                                    {item.name}
                                            </span>
                                </Link>
                                {/* Tooltip for collapsed sidebar */}
                                {!isOpen && (
                                    <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-3 px-3 py-2 bg-gray-800 border border-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-[60] shadow-lg">
                                        {item.name}
                                        <div className="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent border-r-gray-800"></div>
                                    </div>
                                )}
                            </div>
                        );
                    })}
                        </nav>
                    </div>

                    {/* User and Logout section */}
                    <div className={`${isOpen ? 'p-4' : 'p-2 flex justify-center'} ${isOpen ? 'block' : 'hidden md:block'}`}>
                        {isOpen ? (
                            <UserProfileDropdown createLocaleUrl={createLocaleUrl} common={common} />
                        ) : (
                            <button
                                onClick={() => setIsOpen(true)}
                                className="w-12 h-12 rounded-full border border-white bg-gradient-to-r from-[#141493] to-[#417ef7] text-white flex items-center justify-center hover:opacity-80 transition-opacity"
                                title="Open sidebar"
                            >
                                <UserIcon className="text-white" width={16} height={16} />
                            </button>
                        )}
                    </div>
                </div>
            </div>

            {/* Main content area */}
            <div className="flex-1 flex flex-col h-screen overflow-hidden">
                {/* Content area with proper padding for mobile header */}
                <main className="flex-1 overflow-auto bg-black pt-14 md:pt-0">
                    <div className="h-full max-w-screen-2xl mx-auto p-4 sm:p-6">
                    {children}
                    </div>
                </main>
            </div>
        </div>
    );
}