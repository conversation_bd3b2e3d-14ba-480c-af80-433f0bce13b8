"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { FiDownload, FiTrash2, FiInfo, FiLoader, FiImage, FiX, FiMaximize } from "react-icons/fi";
import { ensureAuthenticated } from "@/lib/auth-helpers";
import DynamicTitle from '@/lib/DynamicTitle';
import Link from 'next/link';

interface ImageItem {
  id: string;
  url: string;
  name: string;
  createdAt?: string;
  prompt?: string;
}

export default function Library() {
  const [user, setUser] = useState<any>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [images, setImages] = useState<ImageItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewImage, setViewImage] = useState<ImageItem | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Check for authenticated user on component mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const result = await ensureAuthenticated();
        if (result) {
          // Fetch user information
          const response = await fetch('/api/auth/me', {
            credentials: 'include'
          });

          if (!response.ok) {
            throw new Error('Failed to fetch user data');
          }

          const data = await response.json();
          setUserId(data.user.id);
          setUser(data.user);
        }
      } catch (error) {
        console.error('Authentication error:', error);
        setError('Authentication failed. Please log in again.');
      } finally {
        // Even if we don't get a user ID, the loading state should end
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Fetch images from Supabase storage when userId is available
  useEffect(() => {
    const fetchImagesFromSupabase = async () => {
      if (!userId) return;
      
      try {
        setLoading(true);
        setError(null);
        
        // Fetch the list of files from Supabase
        const response = await fetch(`/api/storage/list?userId=${userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch images: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.success && data.files) {
          // Transform the file data into our ImageItem format
          const transformedData: ImageItem[] = data.files.map((file: any) => ({
            id: file.id,
            url: file.url,
            name: file.name,
            createdAt: file.createdAt,
            // Extract prompt from filename if possible
            prompt: extractPromptFromFilename(file.name),
          }));
          
          // Sort by creation date (newest first) - safely handle undefined dates
          const sortedImages = [...transformedData].sort((a, b) => {
            // If either date is undefined, move it to the end
            if (!a.createdAt) return 1;
            if (!b.createdAt) return -1;
            
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          });
          
          setImages(sortedImages);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (err) {
        console.error('Error fetching images:', err);
        setError('Failed to load your saved images');
      } finally {
        setLoading(false);
      }
    };

    fetchImagesFromSupabase();
  }, [userId]);

  // Extract prompt from filename if it follows a pattern
  function extractPromptFromFilename(filename: string): string | undefined {
    // This is a simple example - adapt based on your actual filename pattern
    try {
      // Example pattern: 1742754522889-8n6n60-flux.1-_schnell_-free-1742754522849-0.png
      // Extract the part after any timestamps that might contain the prompt
      const parts = filename.split('-');
      if (parts.length >= 3) {
        // Look for parts that might contain prompt text (not timestamps or numbers)
        for (let i = 1; i < parts.length - 1; i++) {
          if (isNaN(Number(parts[i])) && !parts[i].match(/^\d+$/)) {
            return parts[i].replace(/_/g, ' ').trim();
          }
        }
      }
      return undefined;
    } catch (e) {
      return undefined;
    }
  }

  const handleDelete = async (image: ImageItem) => {
    if (!confirm('Are you sure you want to delete this image?')) {
      return;
    }

    try {
      setDeletingId(image.id);
      
      // Extract the file path from the URL
      // Example URL: https://gntleknxbxbmzwizidgv.supabase.co/storage/v1/object/public/images/temp-user-1742747654792/generated/1742754522889-8n6n60-flux.1-_schnell_-free-1742754522849-0.png
      const url = new URL(image.url);
      const pathParts = url.pathname.split('/');
      const bucketIndex = pathParts.findIndex(part => part === 'public') + 1;
      
      if (bucketIndex && bucketIndex < pathParts.length) {
        const bucket = pathParts[bucketIndex];
        // The filePath should be everything after the bucket in the path
        const filePath = pathParts.slice(bucketIndex + 1).join('/');
        
        const response = await fetch('/api/storage/delete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            filePath,
            bucket,
          }),
        });

        const data = await response.json();
        
        if (data.success) {
          // Remove the deleted image from the state
          setImages(images.filter(img => img.id !== image.id));
          // Also close the modal if the deleted image was being viewed
          if (viewImage && viewImage.id === image.id) {
            setViewImage(null);
          }
        } else {
          throw new Error(data.error || 'Failed to delete image');
        }
      } else {
        throw new Error('Invalid image URL format');
      }
    } catch (err: any) {
      console.error('Error deleting image:', err);
      alert(`Error deleting image: ${err.message}`);
    } finally {
      setDeletingId(null);
    }
  };

  const handleDownload = (image: ImageItem) => {
    const link = document.createElement('a');
    link.href = image.url;
    link.download = image.name || 'download.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  function formatDate(dateString?: string): string {
    if (!dateString) return 'Unknown date';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (e) {
      return 'Unknown date';
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <DynamicTitle />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <DynamicTitle />
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <DynamicTitle />
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {images.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <FiImage className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-semibold text-gray-900">No images</h3>
            <p className="mt-1 text-sm text-gray-500">
              You haven't generated any images yet.
            </p>
            <div className="mt-6">
              <Link href="/dashboard/image-generation" className="px-4 py-2 rounded-md bg-blue-600 hover:bg-blue-700 text-white">
                Generate Images
              </Link>
            </div>
          </div>
        ) : (
          images.map((image) => (
            <div key={image.id} className="relative group bg-white rounded-lg shadow-md overflow-hidden">
              <div className="aspect-square relative">
                <Image
                  src={image.url}
                  alt={image.prompt || 'Generated image'}
                  fill
                  className="object-cover cursor-pointer"
                  onClick={() => setViewImage(image)}
                  priority={false}
                />
              </div>
              
              <div className="p-2 bg-gray-50">
                <p className="text-xs text-gray-500">{formatDate(image.createdAt)}</p>
                {image.prompt && (
                  <p className="text-sm font-medium text-gray-700 truncate">{image.prompt}</p>
                )}
              </div>
              
              <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDownload(image);
                  }}
                  className="p-1 bg-white rounded-full shadow text-gray-700 hover:text-blue-600"
                  title="Download image"
                >
                  <FiDownload size={16} />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(image);
                  }}
                  className="p-1 bg-white rounded-full shadow text-gray-700 hover:text-red-600"
                  title="Delete image"
                  disabled={deletingId === image.id}
                >
                  {deletingId === image.id ? (
                    <div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-gray-900 rounded-full"></div>
                  ) : (
                    <FiTrash2 size={16} />
                  )}
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Image Viewer Modal */}
      {viewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative bg-white rounded-lg shadow-xl max-w-4xl max-h-[90vh] w-full overflow-hidden">
            <div className="relative aspect-auto max-h-[70vh] overflow-hidden">
              <Image
                src={viewImage.url}
                alt={viewImage.prompt || 'Generated image'}
                width={800}
                height={800}
                className="object-contain w-full h-full"
              />
            </div>
            
            <div className="p-4 bg-white">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-500">{formatDate(viewImage.createdAt)}</p>
                  {viewImage.prompt && (
                    <p className="text-lg font-medium text-gray-900">{viewImage.prompt}</p>
                  )}
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(viewImage);
                    }}
                    className="p-2 rounded-full text-gray-700 hover:bg-gray-100"
                    title="Download image"
                  >
                    <FiDownload size={20} />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(viewImage);
                    }}
                    className="p-2 rounded-full text-gray-700 hover:bg-gray-100"
                    title="Delete image"
                    disabled={deletingId === viewImage.id}
                  >
                    {deletingId === viewImage.id ? (
                      <div className="animate-spin h-5 w-5 border-t-2 border-b-2 border-gray-900 rounded-full"></div>
                    ) : (
                      <FiTrash2 size={20} />
                    )}
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setViewImage(null);
                    }}
                    className="p-2 rounded-full text-gray-700 hover:bg-gray-100"
                    title="Close"
                  >
                    <FiX size={20} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 