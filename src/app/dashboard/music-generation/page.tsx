"use client";

import { useState, useRef, useEffect } from 'react';
import { FiMusic, FiDownload, FiSliders, FiSearch, FiLoader, FiCopy, FiPlay,FiX, FiUpload, FiSend, FiLock, FiChevronDown, FiGrid} from 'react-icons/fi';
import { useSubscription } from '@/contexts/SubscriptionContext';
import {PaperClipIcon, ArrowUpIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { toast } from 'sonner';
import { useMusicGenerationTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

// Custom CSS for chat interface
const customStyles = `
  @media (min-width: 475px) {
    .xs:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  /* Hide scrollbar for clean UI */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1a1a1a;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Message bubbles */
  .message-bubble-user {
    background: linear-gradient(135deg, #141493 0%, #417ef7 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 80%;
    word-wrap: break-word;
  }
  
  .message-bubble-ai {
    background: #111;
    border: 1px solid #222;
    color: #e5e5e5;
    padding: 12px 16px;
    border-radius: 18px 18px 18px 4px;
    max-width: 80%;
    word-wrap: break-word;
  }

  /* Animation for modal */
  @keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .modal-animation {
    animation: modalFadeIn 0.2s ease-out;
  }

  /* Audio preview animation */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .audio-animation {
    animation: fadeIn 0.5s ease-out;
  }
  
  /* Audio loading pulsing animation */
  @keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
  }
  
  .loading-pulse {
    animation: pulse 1.5s infinite ease-in-out;
  }

  /* Progress bar animation */
  @keyframes progress {
    0% { width: 0%; }
    100% { width: 100%; }
  }

  .animate-progress {
    animation: progress 2s linear infinite;
  }
`;

// Define generated music interface
interface GeneratedMusic {
  id: number;
  url: string;
  prompt: string;
  timestamp: Date;
  duration: number;
  model: string;
  title?: string;
}

// Message interface for chat UI
interface Message {
  id: string;
  content: React.ReactNode;
  isUser: boolean;
  timestamp: Date;
  provider?: string;
  model?: string;
}

// Helper function to create messages
const createMessage = (
  content: React.ReactNode,
  isUser: boolean,
  provider?: string,
  model?: string
): Message => ({
  id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
  content,
  isUser,
  timestamp: new Date(),
  provider,
  model,
});

// Define music generation modes
type MusicMode = 'text-to-music' | 'audio-to-music' | 'extend-music' | 'remix-music';

// This will be moved inside the component to access translations
const getMusicModes = (t: any) => [
  {
    id: 'text-to-music' as MusicMode,
    name: t('modes.textToMusic'),
    description: t('modeDescriptions.textToMusic'),
    icon: FiMusic,
  },
  {
    id: 'audio-to-music' as MusicMode,
    name: t('modes.audioToMusic'),
    description: t('modeDescriptions.audioToMusic'),
    icon: FiUpload,
  },
  {
    id: 'extend-music' as MusicMode,
    name: t('modes.extendMusic'),
    description: t('modeDescriptions.extendMusic'),
    icon: FiPlay,
  },
  {
    id: 'remix-music' as MusicMode,
    name: t('modes.remixMusic'),
    description: t('modeDescriptions.remixMusic'),
    icon: FiSliders,
  },
];

// Define music duration options
interface Duration {
  id: string;
  label: string;
  seconds: number;
}

const durations: Duration[] = [
  { id: "10s", label: "10 seconds", seconds: 10 },
  { id: "20s", label: "20 seconds", seconds: 20 },
  { id: "30s", label: "30 seconds", seconds: 30 },
  { id: "45s", label: "45 seconds", seconds: 45 },
  { id: "60s", label: "1 minute", seconds: 60 },
  { id: "90s", label: "1.5 minutes", seconds: 90 },
  { id: "120s", label: "2 minutes", seconds: 120 },
];

// Define music quality options
interface Quality {
  id: string;
  name: string;
  bitrate: string;
  sampleRate: string;
}

const qualityOptions: Quality[] = [
  { id: "high", name: "High Quality", bitrate: "320 kbps", sampleRate: "44.1 kHz" },
  { id: "medium", name: "Medium Quality", bitrate: "192 kbps", sampleRate: "44.1 kHz" },
  { id: "low", name: "Low Quality", bitrate: "128 kbps", sampleRate: "22 kHz" },
];

// Define AI models for music generation
interface MusicModel {
  id: string;
  name: string;
  description: string;
  provider: string;
  maxDuration: number;
  capabilities: string[];
}

// Categorized models by music mode
const modelsByMode: Record<MusicMode, Record<string, MusicModel[]>> = {
  'text-to-music': {
    "Premium Generation": [
      {
        id: "fal-ai/ace-step",
        name: "ACE-Step",
        description: "Advanced music generation with tags and lyrics support.",
        provider: "fal.ai",
        maxDuration: 200,
        capabilities: ["Text-to-music", "Tags support", "Lyrics support", "High quality"]
      },
      {
        id: "fal-ai/ace-step/prompt-to-audio",
        name: "ACE-Step Prompt",
        description: "Simple prompt-based music generation.",
        provider: "fal.ai",
        maxDuration: 200,
        capabilities: ["Text-to-music", "Prompt-based", "Fast generation"]
      },
    ],
    "Quick Models": [
      {
        id: "fal-ai/lyria2",
        name: "Lyria 2",
        description: "High-quality stable audio generation.",
        provider: "fal.ai",
        maxDuration: 30,
        capabilities: ["Text-to-music", "High quality", "Stable generation"]
      },
      {
        id: "cassetteai/music-generator",
        name: "Music Generator",
        description: "Advanced multimodal audio generation.",
        provider: "fal.ai",
        maxDuration: 60,
        capabilities: ["Text-to-audio", "Multimodal", "Advanced control"]
      },
    ]
  },
  'audio-to-music': {
    "Audio Processing": [
      {
        id: "fal-ai/ace-step/audio-outpaint",
        name: "ACE-Step audio",
        description: "Generate music with audio conditioning and tags.",
        provider: "fal.ai",
        maxDuration: 200,
        capabilities: ["Audio-to-music", "Audio conditioning", "Tags support"]
      },
    ],
  },
  'extend-music': {
    "Continuation": [
      {
        id: "fal-ai/ace-step/audio-inpaint",
        name: "Audio Inpaint",
        description: "Extend existing music with stable audio generation.",
        provider: "fal.ai",
        maxDuration: 120,
        capabilities: ["Music continuation", "Stable generation", "High quality"]
      },
    ],
  },
  'remix-music': {
    "Style Transfer": [
      {
        id: "fal-ai/ace-step/audio-inpaint",
        name: "Audio Inpaint Remix",
        description: "Transform music style with multimodal audio generation.",
        provider: "fal.ai",
        maxDuration: 60,
        capabilities: ["Style transfer", "Multimodal", "Creative control"]
      },
    ],
  },
};

export default function MusicGeneration() {
  const { subscription } = useSubscription();

  // Translation hooks
  const t = useMusicGenerationTranslations();
  const common = useCommonTranslations();

  // Get translated music modes
  const musicModes = getMusicModes(t);


  // Music generation state
  const [mode, setMode] = useState<MusicMode>('text-to-music');
  const [prompt, setPrompt] = useState('');
  const [tags, setTags] = useState('');
  const [selectedModel, setSelectedModel] = useState('fal-ai/ace-step');
  const [selectedDuration, setSelectedDuration] = useState('30s');
  const [selectedQuality, setSelectedQuality] = useState('high');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const [sourceAudio, setSourceAudio] = useState<string | null>(null);

  // UI state
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [isModeDropdownOpen, setIsModeDropdownOpen] = useState(false);
  const [isDurationSelectorOpen, setIsDurationSelectorOpen] = useState(false);
  const [isQualitySelectorOpen, setIsQualitySelectorOpen] = useState(false);
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false);
  const [modelSearchQuery, setModelSearchQuery] = useState('');

  // Chat UI state - Initialize messages from localStorage or empty array
  const [messages, setMessages] = useState<Message[]>(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedMessages = localStorage.getItem('astrostudio-music-generation-messages');
        return savedMessages ? JSON.parse(savedMessages) : [];
      } catch (error) {
        console.error('Error loading messages from localStorage:', error);
        return [];
      }
    }
    return [];
  });

  // Save messages to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('astrostudio-music-generation-messages', JSON.stringify(messages));
      } catch (error) {
        console.error('Error saving messages to localStorage:', error);
      }
    }
  }, [messages]);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handlers
  const handleModeChange = (newMode: MusicMode) => {
    setMode(newMode);
    setError('');
    // Reset model selection when mode changes
    const availableModels = Object.values(modelsByMode[newMode]).flat();
    if (availableModels.length > 0) {
      setSelectedModel(availableModels[0].id);
    }
  };

  const getSelectedModel = () => {
    return Object.values(modelsByMode[mode])
      .flat()
      .find(model => model.id === selectedModel);
  };

  const getCategoryDescription = (category: string) => {
    const descriptions: Record<string, string> = {
      "Quick Generation": "Fast and efficient models",
      "Premium Models": "High-quality advanced models",
      "Experimental": "Cutting-edge experimental models",
      "Audio Processing": "Audio input processing models",
      "Continuation": "Music extension models",
      "Style Transfer": "Style transformation models",
    };
    return descriptions[category] || category;
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (file.type.startsWith('audio/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setSourceAudio(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      toast.error(t('errors.audioFileRequired'));
    }
  };

  const handleGenerate = async () => {
    if (!subscription || subscription.status !== 'active') {
      toast.error(t('errors.subscriptionRequired'));
      return;
    }

    if (!prompt.trim()) {
      toast.error(t('errors.musicDescriptionRequired'));
      return;
    }

    setIsGenerating(true);
    setError('');

    try {
      // Add user message
      const userMessage = createMessage(
        <div className="flex flex-col gap-2">
          <div>{prompt}</div>
          {selectedModel === 'fal-ai/ace-step' && tags && (
            <div className="text-xs text-gray-300">🏷️ Tags: {tags}</div>
          )}
          {sourceAudio && (
            <div className="text-xs text-gray-300">📎 Audio file attached</div>
          )}
        </div>,
        true,
        getSelectedModel()?.provider,
        getSelectedModel()?.name
      );
      setMessages(prev => [...prev, userMessage]);

      // Show loading message
      const loadingMessage = createMessage(
        <div className="flex items-center gap-3 text-sm text-gray-400">
          <FiLoader className="animate-spin" />
          <div>{t('loading.generatingMusic')}</div>
        </div>,
        false
      );
      setMessages(prev => [...prev, loadingMessage]);

      // Make actual API call to generate music
      const response = await fetch('/api/generate-music', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: selectedModel,
          provider: 'fal.ai',
          prompt: prompt,
          tags: selectedModel === 'fal-ai/ace-step' ? tags : undefined,
          lyrics: selectedModel === 'fal-ai/ace-step' && prompt ? prompt : undefined,
          duration: parseInt(selectedDuration.replace('s', '')),
          userId: 'user-123', // Replace with actual user ID
          saveToLibrary: true,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t('errors.generationFailed'));
      }

      const data = await response.json();
      const musicUrl = data.audioUrl;

      // Remove loading message
      setMessages(prev => prev.slice(0, -1));
      const aiMessage = createMessage(
        <div className="flex flex-col gap-3">
          <div className="text-sm text-gray-300">
            Generated music for: "{prompt}"
          </div>
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <FiMusic className="text-blue-400" />
              <div>
                <div className="text-sm font-medium">Generated Music</div>
                <div className="text-xs text-gray-400">
                  {durations.find(d => d.id === selectedDuration)?.label} • {getSelectedModel()?.name}
                </div>
              </div>
            </div>
            <audio controls className="w-full">
              <track kind="captions" src="" label="English captions" />
              <source src={musicUrl} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
            <div className="flex gap-2 mt-3">
              <button
                className="flex items-center gap-2 px-3 py-1.5 text-xs bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                onClick={() => {
                  // Download logic
                  const a = document.createElement('a');
                  a.href = musicUrl;
                  a.download = 'generated-music.mp3';
                  a.click();
                }}
              >
                <FiDownload size={12} />
                Download
              </button>
              <button
                className="flex items-center gap-2 px-3 py-1.5 text-xs bg-gray-600 hover:bg-gray-700 rounded transition-colors"
                onClick={() => {
                  navigator.clipboard.writeText(prompt);
                  toast.success('Prompt copied!');
                }}
              >
                <FiCopy size={12} />
                Copy Prompt
              </button>
            </div>
          </div>
        </div>,
        false,
        getSelectedModel()?.provider,
        getSelectedModel()?.name
      );
      setMessages(prev => [...prev, aiMessage]);

      // Reset form
      setPrompt('');
      setTags('');
      setSourceAudio(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error) {
      console.error('Generation error:', error);
      
      // Remove loading message
      setMessages(prev => prev.slice(0, -1));
      
      const errorMessage = createMessage(
        <div className="flex items-center gap-3 text-sm text-red-400">
          <FiX />
          <div>{t('errors.generationFailed')}</div>
        </div>,
        false
      );
      setMessages(prev => [...prev, errorMessage]);
      
      setError(t('errors.generationFailed'));
    } finally {
      setIsGenerating(false);
    }
  };

  const getPlaceholder = () => {
    const placeholders = {
      'text-to-music': t('placeholders.textToMusic'),
      'audio-to-music': t('placeholders.audioToMusic'),
      'extend-music': t('placeholders.extendMusic'),
      'remix-music': t('placeholders.remixMusic'),
    };
    return placeholders[mode];
  };

  const filteredModels = Object.entries(modelsByMode[mode])
    .map(([category, models]) => ({
      category,
      models: models.filter(
        model => 
          model.name.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
          model.description.toLowerCase().includes(modelSearchQuery.toLowerCase())
      )
    }))
    .filter(({ models }) => models.length > 0);

  if (!subscription?.status || subscription.status !== 'active') {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-6">
          <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full flex items-center justify-center">
            <FiLock className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-3">
            {t('subscriptionRequired')}
          </h3>
          <p className="text-gray-400 mb-6">
            {t('subscriptionRequiredDescription')}
          </p>
          <Link
            href="/dashboard/subscription"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[#141493] to-[#417ef7] text-white rounded-lg font-medium hover:from-[#417ef7] to-[#141493] transition-all"
          >
            <FiLock size={16} />
            Upgrade Plan
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='h-full flex flex-col bg-black text-white'>
      <style>{customStyles}</style>
      <div className="h-full flex flex-col bg-black text-white">
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header with mode tabs and options */}
          <div className="border-b border-[#222] bg-black">
            <div className="flex items-center justify-between p-4">
              {/* Desktop Mode Tabs */}
              <div className="hidden md:flex gap-2">
                {musicModes.map((modeOption) => {
                  const Icon = modeOption.icon;
                  return (
                    <button
                      key={modeOption.id}
                      className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm transition ${
                        mode === modeOption.id
                          ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                          : "hover:bg-[#222] text-gray-300"
                      }`}
                      onClick={() => handleModeChange(modeOption.id)}
                    >
                      <Icon className="text-sm" />
                      <span className="font-medium">{modeOption.name}</span>
                    </button>
                  );
                })}
              </div>
              
              {/* Mobile Mode Dropdown */}
              <div className="md:hidden relative px-3">
                <button
                  onClick={() => setIsModeDropdownOpen(!isModeDropdownOpen)}
                  className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 text-sm"
                >
                  {(() => {
                    return (
                      <>
                        {(() => {
                          const selectedMode = musicModes.find(m => m.id === mode);
                          if (selectedMode?.icon) {
                            const Icon = selectedMode.icon;
                            return <Icon className="text-sm" />;
                          }
                          return null;
                        })()}
                        <span className="font-medium">{musicModes.find(m => m.id === mode)?.name ?? 'Select Mode'}</span>
                        <FiChevronDown className={`transition-transform ${isModeDropdownOpen ? 'rotate-180' : ''}`} />
                      </>
                    );
                  })()} 
                </button>
                
                <AnimatePresence>
                  {isModeDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute right-3 top-full mt-1 z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg min-w-[200px]"
                    >
                      <div className="p-2">
                        {musicModes.map((modeOption) => (
                          <button
                            key={modeOption.id}
                            className={`flex items-center gap-2 w-full px-3 py-2 rounded-lg text-sm transition ${
                              mode === modeOption.id
                                ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                : "hover:bg-[#222] text-gray-300"
                            }`}
                            onClick={() => {
                              handleModeChange(modeOption.id);
                              setIsModeDropdownOpen(false);
                            }}
                          >
                            <modeOption.icon className="text-sm" />
                            <span className="font-medium">{modeOption.name}</span>
                          </button>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              
              <button
                className="flex items-center text-gray-400 hover:text-white text-sm bg-[#222] hover:bg-[#333] px-3 py-1.5 rounded-lg"
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              >
                <FiGrid className="mr-2" />
                {showAdvancedOptions ? "Hide" : "Show"} Options
              </button>
            </div>
          </div>

          {/* Advanced Options Panel */}
          {showAdvancedOptions && (
            <div className="bg-[#111] border-b border-[#222] p-4">
              <div className="flex flex-wrap gap-4 items-center">
                {/* Duration Selector */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">Duration:</span>
                  <div className="relative">
                    <button
                      onClick={() => setIsDurationSelectorOpen(!isDurationSelectorOpen)}
                      className="flex items-center justify-between px-3 py-1.5 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-sm min-w-[120px]"
                    >
                      <span>
                        {durations.find(d => d.id === selectedDuration)?.label ?? selectedDuration}
                      </span>
                      <FiChevronDown className={`ml-2 transition-transform ${isDurationSelectorOpen ? 'rotate-180' : ''}`} />
                    </button>
                    
                    <AnimatePresence>
                      {isDurationSelectorOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 10 }}
                          transition={{ duration: 0.2 }}
                          className="absolute left-0 top-full mt-1 z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                        >
                          <div className="p-2 max-h-[250px] overflow-y-auto custom-scrollbar">
                            {durations.map((duration) => (
                              <button
                                key={duration.id}
                                className={`p-3 rounded-lg text-left transition w-full ${selectedDuration === duration.id
                                    ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                    : "hover:bg-[#222] text-gray-300"
                                  }`}
                                onClick={() => {
                                  setSelectedDuration(duration.id);
                                  setIsDurationSelectorOpen(false);
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div>
                                    <div className="text-sm font-medium">{duration.label}</div>
                                  </div>
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedDuration === duration.id && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </button>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
                
                {/* Quality Selector */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">Quality:</span>
                  <div className="relative">
                    <button
                      onClick={() => setIsQualitySelectorOpen(!isQualitySelectorOpen)}
                      className="flex items-center justify-between px-3 py-1.5 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-sm min-w-[100px]"
                    >
                      <span>
                        {qualityOptions.find(q => q.id === selectedQuality)?.name ?? selectedQuality}
                      </span>
                      <FiChevronDown className={`ml-2 transition-transform ${isQualitySelectorOpen ? 'rotate-180' : ''}`} />
                    </button>
                    
                    <AnimatePresence>
                      {isQualitySelectorOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 10 }}
                          transition={{ duration: 0.2 }}
                          className="absolute left-0 top-full mt-1 z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                        >
                          <div className="p-2 max-h-[250px] overflow-y-auto custom-scrollbar">
                            {qualityOptions.map((quality) => (
                              <button
                                key={quality.id}
                                className={`p-3 rounded-lg text-left transition w-full ${selectedQuality === quality.id
                                    ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                    : "hover:bg-[#222] text-gray-300"
                                  }`}
                                onClick={() => {
                                  setSelectedQuality(quality.id);
                                  setIsQualitySelectorOpen(false);
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div>
                                    <div className="text-sm font-medium">{quality.name}</div>
                                    <div className="text-xs text-gray-400">{quality.bitrate} • {quality.sampleRate}</div>
                                  </div>
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedQuality === quality.id && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </button>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Chat Interface */}
          < div className="flex flex-col h-full overflow-y-auto">
              {/* Message area */}
              <div className="flex-1 p-4 overflow-y-auto auto-scroll custom-scrollbar">
                {messages.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-full text-center text-gray-400 space-y-6 py-10">
                    <div className="flex flex-col items-center">
                    <Image src="/Logo Web.webp" alt="AstroStudio AI" width={200} height={70} className="mb-4" />
                      <h2 className="text-2xl font-medium text-gray-300 mb-4">{t('aiMusicGeneration')}</h2>
                      <p className="max-w-lg mx-auto">
                        {t('description')}
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl">
                      <button
                        className="bg-[#111] hover:bg-[#222] rounded-xl p-4 text-left transition-colors"
                        onClick={() => {
                          setPrompt("Upbeat electronic dance music with heavy bass and energetic synths");
                        }}
                      >
                        <p className="font-medium mb-1">{t('examples.electronicDance.title')}</p>
                        <p className="text-sm text-gray-500">{t('examples.electronicDance.description')}</p>
                      </button>
                      <button
                        className="bg-[#111] hover:bg-[#222] rounded-xl p-4 text-left transition-colors"
                        onClick={() => {
                          setPrompt("Relaxing acoustic guitar melody with soft piano accompaniment");
                        }}
                      >
                        <p className="font-medium mb-1">{t('examples.acousticRelaxation.title')}</p>
                        <p className="text-sm text-gray-500">{t('examples.acousticRelaxation.description')}</p>
                      </button>
                    </div>
                  </div>
                )}

                {/* Messages */}
                <div className="space-y-6">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} max-w-full`}
                    >
                      <div className={`flex ${message.isUser ? 'flex-row-reverse' : 'flex-row'} items-start gap-3 max-w-[90%]`}>
                        {/* Message content */}
                        <div
                          className={`${
                            message.isUser
                              ? 'message-bubble-user'
                              : 'message-bubble-ai'
                          } max-w-full overflow-hidden`}
                        >
                          <div className="break-words">{message.content}</div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Loading indicator */}
                  {isGenerating && (
                    <div className="flex justify-start max-w-full">
                      <div className="flex flex-row items-start gap-3 max-w-[90%]">
                        <div className="message-bubble-ai">
                          <div className="flex space-x-2 items-center h-5">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Input area with model selector */}
              < div className="border-t border-[#222] p-4">
                {/* Model selector */}
                <div className="relative">
                  <button
                    onClick={() => setIsModelSelectorOpen(!isModelSelectorOpen)}
                    className="flex items-center justify-between w-full px-3 py-2 rounded-lg bg-[#111] hover:bg-[#181818] transition-colors mb-3"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                        <span className="text-xs font-semibold text-white">
                          AI
                        </span>
                      </div>
                      <span className="text-sm text-gray-200">
                        {(() => {
                          const model = getSelectedModel();
                          return model ? model.name : "Select a model";
                        })()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FiChevronDown className={`text-gray-400 transition-transform ${isModelSelectorOpen ? 'rotate-180' : ''}`} />
                    </div>
                  </button>

                  {/* Model selector dropdown */}
                  <AnimatePresence>
                    {isModelSelectorOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                      >
                        {/* Model search */}
                        <div className="p-2 border-b border-[#222]">
                          <div className="relative">
                            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                              type="text"
                              value={modelSearchQuery}
                              onChange={(e) => setModelSearchQuery(e.target.value)}
                              placeholder="Search models..."
                              className="w-full bg-[#181818] text-gray-300 pl-9 pr-3 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                            />
                          </div>
                        </div>

                        {/* Model list */}
                        <div className="p-2 max-h-[350px] overflow-y-auto custom-scrollbar bg-black/30 backdrop-blur-sm">
                          {Object.keys(modelsByMode[mode])
                            .flatMap(category =>
                              modelsByMode[mode][category].map(model => ({
                                ...model,
                                category
                              }))
                            )
                            .filter(model =>
                              !modelSearchQuery ||
                              model.name.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                              model.provider.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                              getCategoryDescription(model.category).toLowerCase().includes(modelSearchQuery.toLowerCase())
                            )
                            .map(model => (
                              <button
                                key={model.id}
                                className={`p-3 rounded-lg text-left transition w-full ${
                                  selectedModel === model.id
                                    ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                    : "hover:bg-[#222] text-gray-300"
                                }`}
                                onClick={() => {
                                  setSelectedModel(model.id);
                                  setIsModelSelectorOpen(false);
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-2">
                                    <div className="w-8 h-8 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                                      <span className="text-xs font-semibold text-white">
                                        AI
                                      </span>
                                    </div>
                                    <div>
                                      <div className="text-sm font-medium">{model.name}</div>
                                      <div className="text-xs text-gray-400">{getCategoryDescription(model.category)}</div>
                                    </div>
                                  </div>
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedModel === model.id && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </button>
                            ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Input form */}
                <form onSubmit={(e) => {
                  e.preventDefault();
                  if (!prompt.trim() || isGenerating) return;
                  handleGenerate();
                }} className="relative mt-3">
                  <div className="flex flex-col">
                    {/* File preview area when file is uploaded */}
                    {sourceAudio && (
                      <div className="mb-3 bg-[#111] rounded-lg overflow-hidden inline-block max-w-[200px] relative">
                        <div className="relative">
                          <div className="h-[120px] w-[200px] bg-gray-800 flex items-center justify-center">
                            <FiMusic className="w-8 h-8 text-gray-400" />
                          </div>
                          <button
                            onClick={() => {
                              setSourceAudio(null);
                            }}
                            className="absolute top-1 right-1 p-0.5 bg-black bg-opacity-70 rounded-full hover:bg-opacity-100 transition-all"
                            type="button"
                          >
                            <FiX size={12} className="text-white" />
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Tags input for fal-ai/ace-step model */}
                    {selectedModel === 'fal-ai/ace-step' && (
                      <div className="mb-3">
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Tags
                        </label>
                        <input
                          type="text"
                          className="w-full bg-[#111] border border-[#222] rounded-lg py-3 px-4 text-gray-200 focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                          placeholder={t('placeholders.tags')}
                          value={tags}
                          onChange={(e) => setTags(e.target.value)}
                          disabled={isGenerating}
                        />
                      </div>
                    )}

                    <div className="relative">
                      <textarea
                        className="w-full bg-[#111] border border-[#222] rounded-lg py-3 px-4 pr-12 text-gray-200 focus:outline-none focus:ring-1 focus:ring-[#417ef7] resize-none min-h-[80px] max-h-[150px]"
                        placeholder={selectedModel === 'fal-ai/ace-step' ? t('placeholders.lyrics') :
                                   mode === 'text-to-music' ? t('placeholders.textToMusic') :
                                   mode === 'audio-to-music' ? t('placeholders.audioToMusic') :
                                   mode === 'extend-music' ? t('placeholders.extendMusic') :
                                   t('placeholders.remixMusic')}
                        value={prompt}
                        onChange={(e) => {
                          setPrompt(e.target.value);
                          // Auto-resize
                          e.target.style.height = 'inherit';
                          e.target.style.height = `${Math.min(e.target.scrollHeight, 150)}px`;
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            if (!prompt.trim() || isGenerating) return;
                            handleGenerate();
                            e.currentTarget.style.height = 'auto';
                          }
                        }}
                        disabled={isGenerating}
                        rows={3}
                      />
                      <div className="absolute right-3 bottom-3 flex items-center gap-2">
                        {/* Upload button for audio modes */}
                        {(mode === 'audio-to-music' || mode === 'extend-music' || mode === 'remix-music') && (
                          <label className="p-2 sm:p-1.5 rounded-lg cursor-pointer hover:bg-[#222] transition-colors min-h-[44px] sm:min-h-auto flex items-center justify-center">
                            <PaperClipIcon className="w-4 h-4 text-gray-400" />
                            <input
                              type="file"
                              accept="audio/*"
                              className="hidden"
                              onChange={handleFileUpload}
                              ref={fileInputRef}
                            />
                          </label>
                        )}

                        {/* Send button */}
                        <button
                          type="submit"
                          className={`p-2 sm:p-1.5 rounded-lg flex-shrink-0 min-h-[44px] sm:min-h-auto flex items-center justify-center ${
                            prompt.trim() && !isGenerating
                              ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white hover:opacity-90'
                              : 'bg-[#222] text-gray-500 cursor-not-allowed'
                          } transition-colors`}
                          disabled={!prompt.trim() || isGenerating}
                        >
                          <ArrowUpIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-2 text-center hidden md:block">
                    Press Enter to send, Shift+Enter for new line. {(mode === 'audio-to-music' || mode === 'extend-music' || mode === 'remix-music') && 'Upload an audio file to get started.'}
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        </div>
  );
}