"use client";

import { useState, useCallback, Suspense } from 'react';
import Link from 'next/link';
import {
  FiImage,
  FiEdit3,
  FiVideo,
  FiMic,
  FiMusic,
  FiArrowRight,
  FiCheck,
  FiLoader,
} from 'react-icons/fi';
import {UsersIcon, ShieldCheckIcon, BoltIcon, CodeBracketSquareIcon, IdentificationIcon } from '@heroicons/react/24/outline';
import { useDashboardTranslations, useNavigationTranslations } from '@/hooks/useTranslations';
import type { Subscription } from '@/lib/stripe-client';
import PaymentStatusHandler from '@/components/dashboard/PaymentStatusHandler';

// Tools will be defined inside the component to access translations
const getTools = (nav: any, dashboard: any) => [
  {
    name: nav('imageGeneration'),
    description: dashboard('tools.descriptions.imageGeneration'),
    icon: FiImage,
    color: 'from-[#141493] to-[#417ef7]',
    link: '/dashboard/image-generation',
  },
  {
    name: nav('imageEditing'),
    description: dashboard('tools.descriptions.imageEditing'),
    icon: FiEdit3,
    color: 'from-[#141493] to-[#417ef7]',
    link: '/dashboard/image-editing',
  },
  {
    name: nav('videoGeneration'),
    description: dashboard('tools.descriptions.videoGeneration'),
    icon: FiVideo,
    color: 'from-[#141493] to-[#417ef7]',
    link: '/dashboard/video-generation',
  },
  {
    name: nav('podcastGeneration'),
    description: dashboard('tools.descriptions.podcastGeneration'),
    icon: FiMic,
    color: 'from-[#141493] to-[#417ef7]',
    link: '/dashboard/podcast-generation',
  },
  {
    name: nav('musicGeneration'),
    description: dashboard('tools.descriptions.musicGeneration'),
    icon: FiMusic,
    color: 'from-[#141493] to-[#417ef7]',
    link: '/dashboard/music-generation',
  },
  {
    name: nav('aiAssistant'),
    description: dashboard('tools.descriptions.aiAssistant'),
    icon: CodeBracketSquareIcon,
    color: 'from-[#141493] to-[#417ef7]',
    link: '/dashboard/assistant',
  },
];

// Subscription message component to prevent layout shifts
const SubscriptionMessage = ({ 
  subscription, 
  paymentMessage, 
  isLoading 
}: { 
  subscription: Subscription | null, 
  paymentMessage: string | null, 
  isLoading: boolean 
}) => {
  return (
    <div className="min-h-[72px] mb-6 p-4 rounded-lg border border-[#417ef7] bg-[#141493] text-white/50">
      {isLoading ? (
        <div className="flex items-center">
          <FiLoader className="animate-spin mr-2" />
          {paymentMessage ?? 'Loading subscription details...'}
        </div>
      ) : (
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FiCheck className="mr-2" />
            {paymentMessage ?? (
              <span>
                You are subscribed to the <strong>{subscription?.type}</strong> plan.
                {subscription?.status === 'active' ? (
                  ' Your subscription is active.'
                ) : (
                  ` Your subscription status is ${subscription?.status}.`
                )}
              </span>
            )}
          </div>
          <Link href="/dashboard/subscription" className="text-sm underline">
            Manage subscription
          </Link>
        </div>
      )}
    </div>
  );
};

// Tool card component for better code organization
const ToolCard = ({ tool }: { tool: any }) => (
  <Link 
    key={tool.name} 
    href={tool.link}
    className="card group hover:translate-y-[-5px] transition-all duration-300"
  >
    <div className="p-6">
      <div className="flex items-center mb-4">
        <div className={`flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-r ${tool.color} text-white group-hover:scale-110 transition-all duration-300`}>
          <tool.icon className="h-6 w-6" aria-hidden="true" />
        </div>
        <h3 className="ml-4 text-lg leading-6 font-medium text-white">{tool.name}</h3>
      </div>
      <p className="text-base text-gray-400 mb-4">
        {tool.description}
      </p>
      <div className="flex items-center text-[#417ef7]/80 group-hover:text-[#417ef7]">
        <span className="text-sm font-medium">Get started</span>
        <FiArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
      </div>
    </div>
  </Link>
);

export default function Dashboard() {
  const [paymentMessage, setPaymentMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [subscription, setSubscription] = useState<Subscription | null>(null);

  const t = useDashboardTranslations();
  const nav = useNavigationTranslations();

  // Get tools with translations
  const tools = getTools(nav, t);

  // Callback to handle payment status updates from PaymentStatusHandler
  const handlePaymentStatus = useCallback((
    newPaymentMessage: string | null,
    newSubscription: Subscription | null,
    newIsLoading: boolean
  ) => {
    setPaymentMessage(newPaymentMessage);
    setSubscription(newSubscription);
    setIsLoading(newIsLoading);
  }, []);

  return (
    <div className="min-h-screen bg-black custom-scrollbar">
      {/* Payment Status Handler - wrapped in Suspense */}
      <Suspense fallback={null}>
        <PaymentStatusHandler onPaymentStatus={handlePaymentStatus} />
      </Suspense>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-transparent"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              {t('welcome')}{' '}
              <span className="bg-[#417ef7] bg-clip-text text-transparent">
                {t('welcomeHighlight')}
              </span>
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              {t('subtitle')}
            </p>

            {/* Key Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="bg-[#111] border border-[#222] rounded-xl p-6">
                <BoltIcon className="h-8 w-8 text-[#417ef7] mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('features.multiModel.title')}</h3>
                <p className="text-gray-400 text-sm">{t('features.multiModel.description')}</p>
              </div>
              <div className="bg-[#111] border border-[#222] rounded-xl p-6">
                <ShieldCheckIcon className="h-8 w-8 text-[#417ef7] mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('features.privacyFirst.title')}</h3>
                <p className="text-gray-400 text-sm">{t('features.privacyFirst.description')}</p>
              </div>
              <div className="bg-[#111] border border-[#222] rounded-xl p-6">
                <UsersIcon className="h-8 w-8 text-[#417ef7] mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('features.sessionBased.title')}</h3>
                <p className="text-gray-400 text-sm">{t('features.sessionBased.description')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Status */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="min-h-[72px]">
          {(paymentMessage || subscription) && (
            <SubscriptionMessage
              subscription={subscription}
              paymentMessage={paymentMessage}
              isLoading={isLoading}
            />
          )}
        </div>
      </div>

      {/* How It Works Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">{t('howItWorks.title')}</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            {t('howItWorks.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
          <div className="text-center">
            <div className="bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
              <span className="text-white font-bold">1</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">{t('howItWorks.steps.choose.title')}</h3>
            <p className="text-gray-400 text-sm">{t('howItWorks.steps.choose.description')}</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
              <span className="text-white font-bold">2</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">{t('howItWorks.steps.describe.title')}</h3>
            <p className="text-gray-400 text-sm">{t('howItWorks.steps.describe.description')}</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
              <span className="text-white font-bold">3</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">{t('howItWorks.steps.create.title')}</h3>
            <p className="text-gray-400 text-sm">{t('howItWorks.steps.create.description')}</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
              <span className="text-white font-bold">4</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">{t('howItWorks.steps.download.title')}</h3>
            <p className="text-gray-400 text-sm">{t('howItWorks.steps.download.description')}</p>
          </div>
        </div>
      </div>

      {/* AI Tools Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">{t('tools.title')}</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            {t('tools.description')}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {tools.map((tool) => (
            <ToolCard key={tool.name} tool={tool} />
          ))}
        </div>
      </div>

      {/* Privacy & Trust Section */}
      <div className="bg-[#111] border-t border-[#222] h-full maxw-full">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">{t('privacy.title')}</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              {t('privacy.description')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <ShieldCheckIcon className="h-12 w-12 text-[#417ef7] mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">{t('privacy.features.noDataTraining.title')}</h3>
              <p className="text-gray-400 text-sm">{t('privacy.features.noDataTraining.description')}</p>
            </div>
            <div className="text-center">
              <BoltIcon className="h-12 w-12 text-[#417ef7] mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">{t('privacy.features.sessionStorage.title')}</h3>
              <p className="text-gray-400 text-sm">{t('privacy.features.sessionStorage.description')}</p>
            </div>
            <div className="text-center">
              <IdentificationIcon className="h-12 w-12 text-[#417ef7] mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">{t('privacy.features.fullOwnership.title')}</h3>
              <p className="text-gray-400 text-sm">{t('privacy.features.fullOwnership.description')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}