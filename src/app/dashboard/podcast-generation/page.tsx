"use client";

import { useState, useRef, useEffect } from 'react';
import { FiPlay, FiPause, FiCopy, FiDownload, FiCheck, FiSquare, FiMusic, FiVolume2, FiUser, FiPlus, FiMinus, FiTag, FiSliders, FiRotateCw, FiUsers, FiLoader, FiZap, FiChevronRight, FiX, FiSend, FiLock, FiChevronDown, FiSearch } from 'react-icons/fi';
import { toast } from 'sonner';
import {PaperClipIcon, ArrowUpIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { usePodcastGenerationTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

// Custom CSS for chat interface (copied from music-generation)
const customStyles = `
  @media (min-width: 475px) {
    .xs:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  /* Hide scrollbar for clean UI */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1a1a1a;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Message bubbles */
  .message-bubble-user {
    background: linear-gradient(135deg, #141493 0%, #417ef7 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 80%;
    word-wrap: break-word;
  }
  
  .message-bubble-ai {
    background: #111;
    border: 1px solid #222;
    color: #e5e5e5;
    padding: 12px 16px;
    border-radius: 18px 18px 18px 4px;
    max-width: 80%;
    word-wrap: break-word;
  }

  /* Animation for modal */
  @keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .modal-animation {
    animation: modalFadeIn 0.2s ease-out;
  }

  /* Audio preview animation */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .audio-animation {
    animation: fadeIn 0.5s ease-out;
  }
  
  /* Audio loading pulsing animation */
  @keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
  }
  
  .loading-pulse {
    animation: pulse 1.5s infinite ease-in-out;
  }

  /* Progress bar animation */
  @keyframes progress {
    0% { width: 0%; }
    100% { width: 100%; }
  }

  .animate-progress {
    animation: progress 2s linear infinite;
  }
`;

// Define generated podcast interface (can be simplified for chat display)
interface GeneratedPodcast {
  id: string; // Changed to string for consistency with Message id
  url?: string; // Audio URL
  title?: string;
  description?: string;
  timestamp: Date;
  model?: string;
  // Other relevant fields can be added as needed
}

// Message interface for chat UI (copied from music-generation)
interface Message {
  id: string;
  content: React.ReactNode;
  isUser: boolean;
  timestamp: Date;
  provider?: string;
  model?: string;
  podcast?: GeneratedPodcast; // To embed podcast info in AI messages
}

// Helper function to create messages (copied from music-generation)
const createMessage = (
  content: React.ReactNode,
  isUser: boolean,
  provider?: string,
  model?: string,
  podcast?: GeneratedPodcast
): Message => ({
  id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
  content,
  isUser,
  timestamp: new Date(),
  provider,
  model,
  podcast
});

// Define available voices with descriptions
interface Voice {
  id: string;
  name: string;
  provider: string;
  preview_url?: string;
  description: string;
}

// Function to get ElevenLabs voices with translations
const getElevenLabsVoices = (t: any): Voice[] => [
  {
    id: "Aria",
    name: "Aria",
    provider: "ElevenLabs",
    preview_url: "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3",
    description: t('voiceDescriptions.aria')
  },
  {
    id: "Rachel",
    name: "Rachel",
    provider: "ElevenLabs",
    preview_url: "https://storage.googleapis.com/eleven-public-prod/premade/voices/21m00Tcm4TlvDq8ikWAM/b4928a68-c03b-411f-8533-3d5c299fd451.mp3",
    description: t('voiceDescriptions.rachel')
  },
  {
    id: "Drew",
    name: "Drew",
    provider: "ElevenLabs",
    preview_url: "https://storage.googleapis.com/eleven-public-prod/premade/voices/29vD33N1CtxCmqQRPOHJ/b99fc51d-12d3-4312-b480-a8a45a7d51ef.mp3",
    description: t('voiceDescriptions.drew')
  },
  {
    id: "Clyde",
    name: "Clyde",
    provider: "ElevenLabs",
    preview_url: "https://storage.googleapis.com/eleven-public-prod/premade/voices/2EiwWnXFnvU5JabPnv8n/65d80f52-703f-4cae-a91d-75d4e200ed02.mp3",
    description: t('voiceDescriptions.clyde')
  },
  {
    id: "Paul",
    name: "Paul",
    provider: "ElevenLabs",
    preview_url: "https://storage.googleapis.com/eleven-public-prod/premade/voices/5Q0t7uMcjvnagumLfvZi/a4aaa30e-54c4-44a4-8e46-b9b00505d963.mp3",
    description: t('voiceDescriptions.paul')
  },
  {
    id: "Domi",
    name: "Domi",
    provider: "ElevenLabs",
    preview_url: "https://storage.googleapis.com/eleven-public-prod/premade/voices/AZnzlk1XvdvUeBnXmlld/b3c36b01-f80d-4b16-a698-f83682dee84c.mp3",
    description: t('voiceDescriptions.domi')
  },
  {
    id: "Sarah",
    name: "Sarah",
    provider: "ElevenLabs",
    preview_url: "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/01a3e33c-6e99-4ee7-8543-ff2216a32186.mp3",
    description: t('voiceDescriptions.sarah')
  },
  {
    id: "Dave",
    name: "Dave",
    provider: "ElevenLabs",
    preview_url: "https://storage.googleapis.com/eleven-public-prod/premade/voices/CYw3kZ02Hs0563khs1Fj/872cb056-45d3-419e-b5c6-de2b387a93a0.mp3",
    description: t('voiceDescriptions.dave')
  }
];

// Function to get PlayDialog voices with translations
const getPlayDialogVoices = (t: any): Voice[] => [
  {
    id: "Jennifer (English (US)/American)",
    name: "Jennifer",
    provider: "PlayDialog",
    description: t('voiceDescriptions.jennifer')
  },
  {
    id: "Furio (English (IT)/Italian)",
    name: "Furio",
    provider: "PlayDialog",
    description: t('voiceDescriptions.furio')
  },
  {
    id: "Dexter (English (US)/American)",
    name: "Dexter",
    provider: "PlayDialog",
    description: t('voiceDescriptions.dexter')
  },
  {
    id: "Charlotte (Advertising) (English (CA)/Canadian)",
    name: "Charlotte",
    provider: "PlayDialog",
    description: t('voiceDescriptions.charlotte')
  },
  {
    id: "Ava (English (AU)/Australian)",
    name: "Ava",
    provider: "PlayDialog",
    description: t('voiceDescriptions.ava')
  },
  {
    id: "Cecil (English (GB)/British)",
    name: "Cecil",
    provider: "PlayDialog",
    description: t('voiceDescriptions.cecil')
  },
  {
    id: "Cillian (English (IE)/Irish)",
    name: "Cillian",
    provider: "PlayDialog",
    description: t('voiceDescriptions.cillian')
  },
  {
    id: "Alessandro (English (IT)/Italian)",
    name: "Alessandro",
    provider: "PlayDialog",
    description: t('voiceDescriptions.alessandro')
  },
  {
    id: "Carmen (English (MX)/Mexican)",
    name: "Carmen",
    provider: "PlayDialog",
    description: t('voiceDescriptions.carmenMexican')
  },
  {
    id: "Kiriko Conversational (Japanese/Japanese)",
    name: "Kiriko",
    provider: "PlayDialog",
    description: t('voiceDescriptions.kiriko')
  },
  {
    id: "Sterling (English (GB)/British)",
    name: "Sterling",
    provider: "PlayDialog",
    description: t('voiceDescriptions.sterling')
  },
  {
    id: "Dohee Conversational (Korean/Korean)",
    name: "Dohee",
    provider: "PlayDialog",
    description: t('voiceDescriptions.dohee')
  },
  {
    id: "Carmen Conversational (Spanish/Spanish)",
    name: "Carmen",
    provider: "PlayDialog",
    description: t('voiceDescriptions.carmenSpanish')
  },
  {
    id: "Patricia Conversational (Spanish/Spanish)",
    name: "Patricia",
    provider: "PlayDialog",
    description: t('voiceDescriptions.patricia')
  },
  {
    id: "Caroline Conversational (Portuguese (BR)/Brazilian)",
    name: "Caroline",
    provider: "PlayDialog",
    description: t('voiceDescriptions.caroline')
  },
  {
    id: "Madison (English (IE)/Irish)",
    name: "Madison",
    provider: "PlayDialog",
    description: t('voiceDescriptions.madison')
  },
  {
    id: "Baptiste (English (FR)/French)",
    name: "Baptiste",
    provider: "PlayDialog",
    description: t('voiceDescriptions.baptiste')
  },
  {
    id: "Andrei Conversational (Russian/Russian)",
    name: "Andrei",
    provider: "PlayDialog",
    description: t('voiceDescriptions.andrei')
  },
  {
    id: "Ada (English (ZA)/South african)",
    name: "Ada",
    provider: "PlayDialog",
    description: t('voiceDescriptions.ada')
  },
  {
    id: "Charlotte (Meditation) (English (CA)/Canadian)",
    name: "Charlotte (Meditation)",
    provider: "PlayDialog",
    description: t('voiceDescriptions.charlotteMeditation')
  }
];

// Function to get Chatterbox voices with translations
const getChatterboxVoices = (t: any): Voice[] => [
  {
    id: "Aurora",
    name: "Aurora",
    provider: "Chatterbox HD",
    description: t('voiceDescriptions.aurora')
  },
  {
    id: "Blade",
    name: "Blade",
    provider: "Chatterbox HD",
    description: t('voiceDescriptions.blade')
  },
  {
    id: "Britney",
    name: "Britney",
    provider: "Chatterbox HD",
    description: t('voiceDescriptions.britney')
  },
  {
    id: "Carl",
    name: "Carl",
    provider: "Chatterbox HD",
    description: t('voiceDescriptions.carl')
  },
  {
    id: "Cliff",
    name: "Cliff",
    provider: "Chatterbox HD",
    description: t('voiceDescriptions.cliff')
  },
  {
    id: "Rico",
    name: "Rico",
    provider: "Chatterbox HD",
    description: t('voiceDescriptions.rico')
  },
  {
    id: "Vicky",
    name: "Vicky",
    provider: "Chatterbox HD",
    description: t('voiceDescriptions.vicky')
  },
  {
    id: "Richard",
    name: "Richard",
    provider: "Chatterbox HD",
    description: t('voiceDescriptions.richard')
  }
];


// Define AI models for podcast generation
interface PodcastModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  capabilities: string[];
  supportsMultipleVoices?: boolean;
}

const podcastModelsByCategory: Record<string, PodcastModel[]> = {
  "Premium Generation": [
    {
      id: "elevenlabs-multilingual-v2",
      name: "ElevenLabs Multilingual V2",
      provider: "Fal AI",
      description: "High-quality multilingual voice synthesis with natural intonation.",
      capabilities: ["Multilingual", "High quality", "Natural speech", "Single voice"]
    }
  ],
  "Conversational Models": [
    {
      id: "replicate-play-dialog",
      name: "PlayDialog",
      provider: "Fal AI",
      description: "Advanced conversational podcast generation with multiple speakers.",
      capabilities: ["Multi-speaker", "Conversational", "Dialog format", "Natural flow"],
      supportsMultipleVoices: true
    }
  ],
   "Narrative Models": [
    {
      id: "resemble-ai/chatterboxhd/text-to-speech",
      name: "Chatterbox HD",
      provider: "Fal AI",
      description: "High-quality narrative voice synthesis with multiple voice options for storytelling and content creation.",
      capabilities: ["High quality", "Narrative", "Multiple voice options", "Professional audio"],
      supportsMultipleVoices: false
    }
  ]
};

export default function PodcastGeneration() {
  const { subscription } = useSubscription();

  // Translation hooks
  const t = usePodcastGenerationTranslations();
  const common = useCommonTranslations();

  // Chat UI state
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');

  // Podcast specific state
  const [selectedModel, setSelectedModel] = useState(Object.values(podcastModelsByCategory)[0][0].id);
  const [selectedVoice1, setSelectedVoice1] = useState("Rachel");
  const [selectedVoice2, setSelectedVoice2] = useState("Jennifer (English (US)/American)");
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false);
  const [isVoice1SelectorOpen, setIsVoice1SelectorOpen] = useState(false);
  const [isVoice2SelectorOpen, setIsVoice2SelectorOpen] = useState(false);
  const [modelSearchQuery, setModelSearchQuery] = useState('');
  const [voice1SearchQuery, setVoice1SearchQuery] = useState('');
  const [voice2SearchQuery, setVoice2SearchQuery] = useState('');
  const [playingVoice, setPlayingVoice] = useState<string | null>(null);
  const [isProcessingPrompt, setIsProcessingPrompt] = useState(false);
  
  // Modal state for suggestions
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false);
  const [modalInput, setModalInput] = useState('');
  const [generatedContent, setGeneratedContent] = useState('');

  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  // Update selected voices when model changes
  useEffect(() => {
    const availableVoices = getAvailableVoices();
    if (availableVoices.length > 0) {
      setSelectedVoice1(availableVoices[0].id);
      if (availableVoices.length > 1) {
        setSelectedVoice2(availableVoices[1].id);
      }
    }
  }, [selectedModel]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Helper functions
  const getSelectedModel = () => {
    return Object.values(podcastModelsByCategory)
      .flat()
      .find(model => model.id === selectedModel);
  };

  const getCategoryDescription = (category: string) => {
    const descriptions: Record<string, string> = {
      "Premium Generation": "High-quality single voice models",
      "Conversational Models": "Multi-speaker dialog models",
      "Narrative Models": "Professional narrative voice synthesis",
    };
    return descriptions[category] || category;
  };

  const getAvailableVoices = () => {
    const currentModel = getSelectedModel();
    if (currentModel?.provider === "Fal AI" && currentModel?.id.includes("replicate-play-dialog")) {
      return getPlayDialogVoices(t);
    }
    if (currentModel?.id === "resemble-ai/chatterboxhd/text-to-speech") {
      return getChatterboxVoices(t);
    }
    return getElevenLabsVoices(t);
  };

  const handleVoicePreview = async (voiceId: string) => {
    const voices = getAvailableVoices();
    const voice = voices.find(v => v.id === voiceId);
    if (!voice?.preview_url) return;

    if (playingVoice === voiceId) {
      setPlayingVoice(null);
      return;
    }

    setPlayingVoice(voiceId);
    const audio = new Audio(voice.preview_url);
    audio.onended = () => setPlayingVoice(null);
    audio.onerror = () => {
      setPlayingVoice(null);
      toast.error(t('errors.failedToPlayVoicePreview'));
    };
    
    try {
      await audio.play();
    } catch (error) {
      setPlayingVoice(null);
      toast.error(t('errors.failedToPlayVoicePreview'));
    }
  };

  // Function to get dynamic placeholder text based on selected model
  const getPlaceholderText = () => {
    const currentModel = getSelectedModel();
    if (currentModel?.provider === "Replicate" && currentModel?.id.includes("play-dialog")) {
      return t('placeholders.dialog');
    } else if (currentModel?.provider === "ElevenLabs") {
      return t('placeholders.narration');
    }
    return t('placeholders.default');
  };

  // Function to generate content based on user input in modal
  const generateContentFromModal = async () => {
    if (!modalInput.trim()) {
      toast.error(t('errors.requestRequired'));
      return;
    }

    setIsGeneratingSuggestions(true);
    try {
      const currentModel = getSelectedModel();
      let prompt = "";
      
      if (currentModel?.provider === "Replicate" && currentModel?.id.includes("play-dialog")) {
        prompt = `Convert this request into a conversational dialog format with Voice1 and Voice2 speakers. Make it natural and engaging. Use the format "Voice1: [text]\nVoice2: [text]" and ensure both speakers participate meaningfully. Request: ${modalInput}`;
      } else if (currentModel?.provider === "ElevenLabs") {
        prompt = `Convert this request into a clear, well-structured text for single-speaker narration. Make it engaging and informative. Request: ${modalInput}`;
      } else {
        prompt = `Convert this request into appropriate podcast content: ${modalInput}`;
      }

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: prompt,
          model: 'claude-3-5-sonnet-20241022'
        }),
      });

      if (!response.ok) {
        throw new Error(t('errors.failedToGenerateContent'));
      }

      const data = await response.json();
      const content = data.response ?? data.message ?? '';
      
      setGeneratedContent(content);
      toast.success(t('success.contentGenerated'));
    } catch (error) {
      console.error('Error generating content:', error);
      toast.error(t('errors.failedToGenerateContent'));
    } finally {
      setIsGeneratingSuggestions(false);
    }
  };



  // Update voices when model changes
  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
    const newVoices = getAvailableVoices();
    setSelectedVoice1(newVoices[0]?.id || '');
    setSelectedVoice2(newVoices[1]?.id || '');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage = createMessage(input, true);
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsGenerating(true);
    setError('');

    // Show loading message
    const loadingMessage = createMessage(
      <div className="flex items-center gap-3 text-sm text-gray-400">
        <FiLoader className="animate-spin" />
        <div>Thinking...</div>
      </div>,
      false
    );
    setMessages(prev => [...prev, loadingMessage]);

    try {
      // Podcast generation logic
      const currentModelDetails = getSelectedModel();
      const availableVoices = getAvailableVoices();
      
      // Determine voices based on model
      let voiceDetails;
      let requestBody: any = {
        model: selectedModel,
        text: input
      };
      
      if (currentModelDetails?.supportsMultipleVoices) {
        const voice1Details = availableVoices.find(v => v.id === selectedVoice1);
        const voice2Details = availableVoices.find(v => v.id === selectedVoice2);
        voiceDetails = `${voice1Details?.name} & ${voice2Details?.name}`;
        requestBody.voice1 = voice1Details?.name ?? "Jennifer (English (US)/American)";
        requestBody.voice2 = voice2Details?.name ?? "Furio (English (IT)/Italian)";
      } else {
        const voice1Details = availableVoices.find(v => v.id === selectedVoice1);
        voiceDetails = voice1Details?.name;
        requestBody.voice = voice1Details?.name ?? "Aria";
      }

      // Call the actual API
      const response = await fetch('/api/generate-podcast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error ?? t('errors.generationFailed'));
      }

      const data = await response.json();
      const podcastUrl = data.audioUrl;
      
      const generatedPodcast: GeneratedPodcast = {
        id: Date.now().toString(),
        url: podcastUrl,
        title: `Podcast: ${input.substring(0,30)}${input.length > 30 ? '...' : ''}`,
        timestamp: new Date(),
        model: currentModelDetails?.name,
      };

      // Remove loading message
      setMessages(prev => prev.filter(msg => msg.id !== loadingMessage.id));

      const aiResponseMessage = createMessage(
        <div className="flex flex-col gap-3">
          <div className="text-sm text-gray-300">
            I've generated a podcast for you based on: "{input}"
          </div>
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <FiMusic className="text-blue-400" /> {/* Placeholder icon */}
              <div>
                <div className="text-sm font-medium">{generatedPodcast.title}</div>
                <div className="text-xs text-gray-400">
                  Model: {generatedPodcast.model} • Voice: {voiceDetails}
                </div>
              </div>
            </div>
            {generatedPodcast.url && (
              <audio controls className="w-full">
                <track kind="captions" src="" label="English captions" />
                <source src={generatedPodcast.url} type="audio/mpeg" />
                Your browser does not support the audio element.
              </audio>
            )}
            <div className="flex gap-2 mt-3">
              <button
                className="flex items-center gap-2 px-3 py-1.5 text-xs bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                onClick={() => {
                  if(generatedPodcast.url) {
                    const a = document.createElement('a');
                    a.href = generatedPodcast.url;
                    a.download = `${generatedPodcast.title?.replace(/[^a-z0-9]/gi, '_').toLowerCase() ?? 'generated_podcast'}.mp3`;
                    a.click();
                    toast.success(t('success.downloadStarted'));
                  } else {
                    toast.error(t('errors.noAudioUrlAvailable'));
                  }
                }}
              >
                <FiDownload size={12} />
                Download
              </button>
              <button
                className="flex items-center gap-2 px-3 py-1.5 text-xs bg-gray-600 hover:bg-gray-700 rounded transition-colors"
                onClick={() => {
                  navigator.clipboard.writeText(input);
                  toast.success('Topic copied!');
                }}
              >
                <FiCopy size={12} />
                Copy Topic
              </button>
            </div>
          </div>
        </div>,
        false,
        currentModelDetails?.provider,
        currentModelDetails?.name,
        generatedPodcast
      );
      setMessages(prev => [...prev, aiResponseMessage]);

    } catch (err: any) {
      console.error('Podcast generation error:', err);
      // Remove loading message
      setMessages(prev => prev.filter(msg => msg.id !== loadingMessage.id));
      const errorMessage = createMessage(
        <div className="flex items-center gap-3 text-sm text-red-400">
          <FiX />
          <div>{t('errors.failedToGeneratePodcast')} {err.message ?? 'Please try again.'}</div>
        </div>,
        false
      );
      setMessages(prev => [...prev, errorMessage]);
      setError(err.message ?? t('errors.generationFailed'));
    } finally {
      setIsGenerating(false);
    }
  };

  if (!subscription || subscription.status !== 'active') {
    return (
      <div className="h-full flex items-center justify-center bg-black text-white">
        <div className="text-center max-w-md mx-auto px-6">
          <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full flex items-center justify-center">
            <FiLock className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-3">
            {t('subscriptionRequired')}
          </h3>
          <p className="text-gray-400 mb-6">
            {t('subscriptionRequiredDescription')}
          </p>
          <Link
            href="/dashboard/subscription"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[#141493] to-[#417ef7] text-white rounded-lg font-medium hover:from-[#417ef7] to-[#141493] transition-all"
          >
            <FiLock size={16} />
            Upgrade Plan
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='h-full flex flex-col bg-black text-white'>
      <style>{customStyles}</style>
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="border-b border-[#222] bg-black p-4">
          <h1 className="text-xl font-semibold text-white">{t('aiPodcastGeneration')}</h1>
        </div>

        {/* Chat Interface */}
        <div className="flex flex-col h-full overflow-y-auto">
          {/* Message area */}
          <div className="flex-1 p-4 overflow-y-auto auto-scroll custom-scrollbar">
            {messages.length === 0 && (
              <div className="flex flex-col items-center justify-center h-full text-center text-gray-400 space-y-6 py-10">
                <div className="flex flex-col items-center">
                  <Image src="/Logo Web.webp" alt="AstroStudio AI" width={200} height={70} className="mb-4" />
                  <h2 className="text-2xl font-medium text-gray-300 mb-4">{t('aiPodcastGeneration')}</h2>
                  <p className="max-w-lg mx-auto">
                    {t('description')}
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl">
                  <button
                    className="bg-[#111] hover:bg-[#222] rounded-xl p-4 text-left transition-colors"
                    onClick={() => {
                      setInput(t('examples.educationalContent.prompt'));
                    }}
                  >
                    <p className="font-medium mb-1">{t('examples.educationalContent.title')}</p>
                    <p className="text-sm text-gray-500">{t('examples.educationalContent.description')}</p>
                  </button>
                  <button
                    className="bg-[#111] hover:bg-[#222] rounded-xl p-4 text-left transition-colors"
                    onClick={() => {
                      setInput(t('examples.conversationalDialog.prompt'));
                    }}
                  >
                    <p className="font-medium mb-1">{t('examples.conversationalDialog.title')}</p>
                    <p className="text-sm text-gray-500">{t('examples.conversationalDialog.description')}</p>
                  </button>
                </div>
              </div>
            )}

            {/* Messages */}
            <div className="space-y-6 overflow-y-auto">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} max-w-full`}
                >
                  <div className={`flex ${message.isUser ? 'flex-row-reverse' : 'flex-row'} items-start gap-3 max-w-[90%]`}>
                    {/* Message content */}
                    <div
                      className={`${
                        message.isUser
                          ? 'message-bubble-user'
                          : 'message-bubble-ai'
                      } max-w-full overflow-hidden`}
                    >
                      <div className="break-words">{message.content}</div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Loading indicator */}
              {isGenerating && (
                <div className="flex justify-start max-w-full">
                  <div className="flex flex-row items-start gap-3 max-w-[90%]">
                    <div className="message-bubble-ai">
                      <div className="flex space-x-2 items-center h-5">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div ref={messagesEndRef} />
          </div>

          {/* Input area with model and voice selectors */}
          <div className="border-t border-[#222] p-4">
            {/* Model selector */}
            <div className="relative mb-3">
              <button
                onClick={() => setIsModelSelectorOpen(!isModelSelectorOpen)}
                className="flex items-center justify-between w-full px-3 py-2 rounded-lg bg-[#111] hover:bg-[#181818] transition-colors"
              >
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                    <span className="text-xs font-semibold text-white">
                      AI
                    </span>
                  </div>
                  <span className="text-sm text-gray-200">
                    {getSelectedModel()?.name ?? "Select a model"}
                  </span>
                </div>
                <FiChevronDown className={`text-gray-400 transition-transform ${isModelSelectorOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* Model selector dropdown */}
              <AnimatePresence>
                {isModelSelectorOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.2 }}
                    className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                  >
                    {/* Model search */}
                    <div className="p-2 border-b border-[#222]">
                      <div className="relative">
                        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          value={modelSearchQuery}
                          onChange={(e) => setModelSearchQuery(e.target.value)}
                          placeholder="Search models..."
                          className="w-full bg-[#181818] text-gray-300 pl-9 pr-3 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                        />
                      </div>
                    </div>

                    {/* Model list */}
                    <div className="p-2 max-h-[350px] overflow-y-auto custom-scrollbar bg-black/30 backdrop-blur-sm">
                      {Object.entries(podcastModelsByCategory)
                        .flatMap(([category, models]) =>
                          models.map(model => ({
                            ...model,
                            category
                          }))
                        )
                        .filter(model =>
                          !modelSearchQuery ||
                          model.name.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                          model.provider.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                          getCategoryDescription(model.category).toLowerCase().includes(modelSearchQuery.toLowerCase())
                        )
                        .map(model => (
                          <button
                            key={model.id}
                            className={`p-3 rounded-lg text-left transition w-full ${
                              selectedModel === model.id
                                ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                : "hover:bg-[#222] text-gray-300"
                            }`}
                            onClick={() => {
                              handleModelChange(model.id);
                              setIsModelSelectorOpen(false);
                            }}
                          >
                            <div className="flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                                  <span className="text-xs font-semibold text-white">
                                    AI
                                  </span>
                                </div>
                                <div>
                                  <div className="text-sm font-medium">{model.name}</div>
                                  <div className="text-xs text-gray-400">{getCategoryDescription(model.category)}</div>
                                </div>
                              </div>
                              <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                {selectedModel === model.id && (
                                  <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                )}
                              </div>
                            </div>
                          </button>
                        ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Voice selectors */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
              {/* Voice 1 selector */}
              <div className="relative">
                <button
                  onClick={() => setIsVoice1SelectorOpen(!isVoice1SelectorOpen)}
                  className="flex items-center justify-between w-full px-3 py-2 rounded-lg bg-[#111] hover:bg-[#181818] transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <FiUser className="text-blue-400" />
                    <span className="text-sm text-gray-200">
                      Voice 1: {getAvailableVoices().find(v => v.id === selectedVoice1)?.name ?? "Select voice"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getAvailableVoices().find(v => v.id === selectedVoice1)?.provider === "ElevenLabs" && (
                      <span
                        onClick={(e) => {
                          e.stopPropagation();
                          handleVoicePreview(selectedVoice1);
                        }}
                        className="p-1 rounded hover:bg-[#222] transition-colors"
                      >
                        {playingVoice === selectedVoice1 ? (
                          <FiPause className="text-green-400" size={14} />
                        ) : (
                          <FiPlay className="text-gray-400 hover:text-green-400" size={14} />
                        )}
                      </span>
                    )}
                    <FiChevronDown className={`text-gray-400 transition-transform ${isVoice1SelectorOpen ? 'rotate-180' : ''}`} />
                  </div>
                </button>

                {/* Voice 1 selector dropdown */}
                <AnimatePresence>
                  {isVoice1SelectorOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                    >
                      {/* Voice search */}
                      <div className="p-2 border-b border-[#222]">
                        <div className="relative">
                          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                          <input
                            type="text"
                            value={voice1SearchQuery}
                            onChange={(e) => setVoice1SearchQuery(e.target.value)}
                            placeholder="Search voices..."
                            className="w-full bg-[#181818] text-gray-300 pl-9 pr-3 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                          />
                        </div>
                      </div>

                      {/* Voice list */}
                      <div className="p-2 max-h-[300px] overflow-y-auto custom-scrollbar">
                        {getAvailableVoices()
                          .filter(voice =>
                            !voice1SearchQuery ||
                            voice.name.toLowerCase().includes(voice1SearchQuery.toLowerCase()) ||
                            voice.description.toLowerCase().includes(voice1SearchQuery.toLowerCase())
                          )
                          .map(voice => (
                            <button
                              key={voice.id}
                              className={`p-3 rounded-lg text-left transition w-full ${
                                selectedVoice1 === voice.id
                                  ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                  : "hover:bg-[#222] text-gray-300"
                              }`}
                              onClick={() => {
                                setSelectedVoice1(voice.id);
                                setIsVoice1SelectorOpen(false);
                              }}
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <FiUser className="text-blue-400" />
                                  <div>
                                    <div className="text-sm font-medium">{voice.name}</div>
                                    <div className="text-xs text-gray-400">{voice.description}</div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  {voice.provider === "ElevenLabs" && (
                                    <span
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleVoicePreview(voice.id);
                                      }}
                                      className="p-1 rounded hover:bg-[#333] transition-colors"
                                    >
                                      {playingVoice === voice.id ? (
                                        <FiPause className="text-green-400" size={14} />
                                      ) : (
                                        <FiPlay className="text-gray-400 hover:text-green-400" size={14} />
                                      )}
                                    </span>
                                  )}
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedVoice1 === voice.id && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </button>
                          ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Voice 2 selector - only show for multi-voice models */}
              {getSelectedModel()?.supportsMultipleVoices && (
                <div className="relative">
                  <button
                    onClick={() => setIsVoice2SelectorOpen(!isVoice2SelectorOpen)}
                    className="flex items-center justify-between w-full px-3 py-2 rounded-lg bg-[#111] hover:bg-[#181818] transition-colors"
                  >
                    <div className="flex items-center gap-2">
                        <FiUsers className="text-purple-400" />
                        <span className="text-sm text-gray-200">
                          Voice 2: {getAvailableVoices().find(v => v.id === selectedVoice2)?.name ?? "Select voice"}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                         {getAvailableVoices().find(v => v.id === selectedVoice2)?.provider === "ElevenLabs" && (
                           <button
                             onClick={(e) => {
                               e.stopPropagation();
                               handleVoicePreview(selectedVoice2);
                             }}
                             className="p-1 rounded-full hover:bg-gray-600 transition-colors"
                           >
                             {playingVoice === selectedVoice2 ? (
                               <FiSquare className="w-3 h-3" />
                             ) : (
                               <FiPlay className="w-3 h-3" />
                             )}
                           </button>
                         )}
                         <FiChevronDown className={`text-gray-400 transition-transform ${isVoice2SelectorOpen ? 'rotate-180' : ''}`} />
                       </div>
                  </button>

                  {/* Voice 2 selector dropdown */}
                  <AnimatePresence>
                    {isVoice2SelectorOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                      >
                        {/* Voice search */}
                        <div className="p-2 border-b border-[#222]">
                          <div className="relative">
                            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                              type="text"
                              value={voice2SearchQuery}
                              onChange={(e) => setVoice2SearchQuery(e.target.value)}
                              placeholder="Search voices..."
                              className="w-full bg-[#181818] text-gray-300 pl-9 pr-3 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                            />
                          </div>
                        </div>

                        {/* Voice list */}
                        <div className="p-2 max-h-[300px] overflow-y-auto custom-scrollbar">
                          {getAvailableVoices()
                           .filter(voice =>
                             voice2SearchQuery === '' ||
                             voice.name.toLowerCase().includes(voice2SearchQuery.toLowerCase()) ||
                             voice.description.toLowerCase().includes(voice2SearchQuery.toLowerCase())
                           )
                           .map(voice => (
                             <button
                               key={voice.id}
                               className={`w-full text-left px-3 py-2 text-sm rounded-md transition-colors ${
                                 selectedVoice2 === voice.id
                                   ? 'bg-blue-600 text-white'
                                   : 'text-gray-300 hover:bg-gray-700'
                               }`}
                               onClick={() => {
                                 setSelectedVoice2(voice.id);
                                 setIsVoice2SelectorOpen(false);
                               }}
                             >
                               <div className="flex items-center justify-between">
                                 <div>
                                   <div className="font-medium">{voice.name}</div>
                                   <div className="text-xs opacity-75">{voice.description}</div>
                                 </div>
                                 {voice.preview_url && (
                                   <button
                                     onClick={(e) => {
                                       e.stopPropagation();
                                       handleVoicePreview(voice.id);
                                     }}
                                     className="ml-2 p-1 rounded-full hover:bg-gray-600 transition-colors"
                                   >
                                     {playingVoice === voice.id ? (
                                       <FiSquare className="w-3 h-3" />
                                     ) : (
                                       <FiPlay className="w-3 h-3" />
                                     )}
                                   </button>
                                 )}
                               </div>
                             </button>
                           ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>

            {/* AI Assistant Button */}
            <div className="mb-4">
              <button
                onClick={() => {
                  setIsModalOpen(true);
                  setModalInput('');
                  setGeneratedContent('');
                }}
                className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-[#141493] to-[#417ef7] text-white rounded-lg hover:opacity-90 transition-opacity"
              >
                <FiZap className="w-4 h-4" />
                AI Assistant
              </button>
            </div>

            {/* Input form */}
            {error && <p className="text-red-500 text-xs mb-2">Error: {error}</p>}
            <form onSubmit={(e) => {
              e.preventDefault();
              if (!input.trim() || isGenerating) return;
              handleSend();
            }} className="relative">
              <div className="flex items-center gap-3 bg-[#111] border border-[#222] rounded-xl p-1.5">
                <textarea
                  value={input}
                  onChange={handleInputChange}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                  placeholder={isProcessingPrompt ? "Processing with LLM..." : getPlaceholderText()}
                  className="flex-1 bg-transparent text-white placeholder-gray-500 p-2 resize-none focus:outline-none hide-scrollbar text-sm"
                  rows={3}
                  disabled={isGenerating || isProcessingPrompt}
                />
                <button
                  type="submit"
                  disabled={isGenerating || isProcessingPrompt || !input.trim()}
                  className={`p-2 sm:p-1.5 rounded-lg flex-shrink-0 min-h-[44px] sm:min-h-auto flex items-center justify-center ${isGenerating || isProcessingPrompt || !input.trim() ? 'bg-[#222] text-gray-500 cursor-not-allowed' : 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white hover:opacity-90'}`}
                >
                  {isGenerating || isProcessingPrompt ? <ArrowPathIcon className="animate-spin w-4 h-4" /> : <ArrowUpIcon className="w-4 h-4" />}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* AI Assistant Modal */}
      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setIsModalOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-[#111] border border-[#222] rounded-xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                  <FiZap className="w-5 h-5 text-blue-400" />
                  AI Podcast Assistant
                </h3>
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <FiX className="w-5 h-5" />
                </button>
              </div>
              
              <p className="text-gray-400 text-sm mb-4">
                {getSelectedModel()?.provider === "Replicate" && getSelectedModel()?.id.includes("play-dialog")
                  ? "Describe what you want for a conversational podcast with multiple speakers:"
                  : "Describe what you want for a single-speaker podcast:"}
              </p>

              {/* Input field */}
              <div className="mb-4">
                <textarea
                  value={modalInput}
                  onChange={(e) => setModalInput(e.target.value)}
                  placeholder={getSelectedModel()?.provider === "Replicate" && getSelectedModel()?.id.includes("play-dialog")
                    ? "e.g., Create a debate between two experts about artificial intelligence and its impact on society"
                    : "e.g., Create an educational podcast about space exploration with a calm, informative tone"}
                  className="w-full bg-[#1a1a1a] border border-[#333] text-white placeholder-gray-500 p-3 rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-[#417ef7] focus:border-[#417ef7]"
                  rows={4}
                  disabled={isGeneratingSuggestions}
                />
              </div>

              {/* Generate button */}
              <div className="mb-4">
                <button
                  onClick={generateContentFromModal}
                  disabled={isGeneratingSuggestions || !modalInput.trim()}
                  className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-[#141493] to-[#417ef7] text-white rounded-lg hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isGeneratingSuggestions ? (
                    <ArrowPathIcon className="animate-spin w-4 h-4" />
                  ) : (
                    <ArrowUpIcon className="w-4 h-4" />
                  )}
                  {isGeneratingSuggestions ? 'Generating...' : 'Generate Content'}
                </button>
              </div>

              {/* Generated content */}
              {generatedContent && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Generated Content:
                  </label>
                  <div className="bg-[#1a1a1a] border border-[#333] rounded-lg p-3">
                    <pre className="text-white text-sm whitespace-pre-wrap font-mono">{generatedContent}</pre>
                  </div>
                  <button
                    onClick={() => {
                      setInput(generatedContent);
                      setIsModalOpen(false);
                      toast.success('Content added to main input!');
                    }}
                    className="mt-2 flex items-center gap-2 px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                  >
                    <FiCheck className="w-3 h-3" />
                    Use This Content
                  </button>
                </div>
              )}

              <div className="flex items-center justify-end gap-3 pt-4 border-t border-[#222]">
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}