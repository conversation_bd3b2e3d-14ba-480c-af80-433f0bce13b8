"use client";

import { useState, useRef, useEffect } from 'react';
import { FiMic, FiDownload, FiUpload, FiSliders, FiX, FiPlay, FiPause, FiLoader, FiCopy, FiCheck, <PERSON>Lock } from 'react-icons/fi';
import ModelSelector from '@/components/ModelSelector';
import { AIModel as ModelSelectorAIModel, ModelCategories as ModelSelectorCategories } from '@/components/ModelSelector';
import { useSubscription } from "@/contexts/SubscriptionContext";
import Link from 'next/link';
import { useSpeechToTextTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import { useRouter } from 'next/navigation';

// Custom CSS for extra small screens
const customStyles = `
  @media (min-width: 475px) {
    .xs\\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  /* Hide scrollbar for clean UI */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Animation for modal */
  @keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .modal-animation {
    animation: modalFadeIn 0.2s ease-out;
  }

  /* Format button hover effects */
  .format-btn:hover {
    background-color: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.5);
  }
  
  .format-btn.selected {
    background-color: rgba(139, 92, 246, 0.3);
    border-color: rgb(139, 92, 246);
  }
`;

// Define model interface
interface AIModel extends ModelSelectorAIModel {
  apiKey?: string;
  capabilities?: string[];
}

// Define model categories interface
interface ModelCategories extends ModelSelectorCategories {
  // Any speech-to-text specific additions
}

// Define transcript interface
interface Transcript {
  id: number;
  text: string;
  audioUrl: string;
  filename: string;
  duration: number;
}

// Model categories and models
const modelsByCategory: ModelCategories = {
  "Quick Transcription": [
    { 
      id: "groq/distil-whisper", 
      name: "Distil-Whisper", 
      description: "Lightweight and efficient speech recognition model with fast processing.", 
      provider: "groq",
      capabilities: ["Fast processing", "Low resource usage", "English focused"]
    },
    { 
      id: "deepgram/nova-2", 
      name: "Deepgram Nova-2", 
      description: "Fast and accurate model specialized for clear speech recognition.", 
      provider: "deepgram",
      capabilities: ["Punctuation", "Fast turnaround", "Clear audio optimal"]
    },
    { 
      id: "deepgram/nova-3", 
      name: "Deepgram Nova-3", 
      description: "Latest Deepgram model with improved accuracy and performance.", 
      provider: "deepgram",
      capabilities: ["High accuracy", "Next-gen engine", "Smart formatting"]
    },
    { 
      id: "elevenlabs/scribe_v1", 
      name: "ElevenLabs Scribe V1", 
      description: "Versatile speech recognition model with high accuracy across various audio conditions.", 
      provider: "elevenlabs",
      capabilities: ["Versatile", "Good with background noise", "Multiple speakers"] 
    },
  ],
  "Accurate Transcription": [
    { 
      id: "groq/whisper-large-v3-turbo", 
      name: "Whisper Large v3 Turbo", 
      description: "Faster version of Whisper v3 with excellent balance of speed and accuracy.", 
      provider: "groq",
      capabilities: ["Balanced performance", "Good for longer content", "Advanced formatting"]
    },
    { 
      id: "groq/whisper-v3-large", 
      name: "Whisper V3 Large", 
      description: "High-accuracy speech recognition model with enhanced multilingual support.", 
      provider: "groq",
      capabilities: ["Maximum accuracy", "74+ languages", "Excellent punctuation"]
    },
  ],
  "Specialized Models": [
    { 
      id: "elevenlabs/scribe_v1_specialized", 
      name: "ElevenLabs Scribe V1 (Specialized)", 
      description: "Specialized version of Scribe V1 with enhanced features for professional transcription.", 
      provider: "elevenlabs",
      capabilities: ["Speaker diarization", "Timestamps", "Professional quality"]
    }
  ]
};

// Output formats
interface OutputFormat {
  id: string;
  name: string;
  extension: string;
  mimeType: string;
}

const outputFormats: OutputFormat[] = [
  { id: 'txt', name: 'Plain Text', extension: 'txt', mimeType: 'text/plain' },
  { id: 'srt', name: 'Subtitles (SRT)', extension: 'srt', mimeType: 'application/x-subrip' },
  { id: 'vtt', name: 'Web Video Text (VTT)', extension: 'vtt', mimeType: 'text/vtt' },
  { id: 'json', name: 'JSON', extension: 'json', mimeType: 'application/json' },
];

export default function SpeechToText() {
  const router = useRouter();

  // Translation hooks
  const t = useSpeechToTextTranslations();
  const common = useCommonTranslations();

  // Use context with hasAccess for feature-specific permission
  const {
    stripePriceId,
    subscription,
    hasAccess,
    loading: subscriptionLoading
  } = useSubscription();
  
  // Debug log to see what's happening
  console.log("Subscription data (speech-to-text):", { 
    stripePriceId,
    subscription,
    hasFeatureAccess: hasAccess('SPEECH_TO_TEXT')
  });
  
  // Check if user has a valid subscription using multiple validation methods
  const hasSubscription = Boolean(
    // Method 1: Direct check through the hasAccess function
    hasAccess('SPEECH_TO_TEXT') ||
    // Method 2: Check subscription type from Neo4j data
    (subscription?.type && ['Premium', 'Standard'].includes(subscription.type)) ||
    // Method 3: Check Stripe price ID direct match with env vars
    (stripePriceId && (
      stripePriceId === process.env.NEXT_PUBLIC_STRIPE_STANDARD_PRICE_ID || 
      stripePriceId === process.env.NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID
    ))
  );
  
  console.log("Has subscription result (speech-to-text):", hasSubscription);
  
  // State variables
  const [selectedModel, setSelectedModel] = useState<string>('groq/distil-whisper');
  const [activeCategory, setActiveCategory] = useState<string>('Quick Transcription');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [language, setLanguage] = useState<string>('auto');
  const [selectedFormat, setSelectedFormat] = useState<string>('txt');
  const [isRecording, setIsRecording] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordedAudioURL, setRecordedAudioURL] = useState<string | null>(null);
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioProgress, setAudioProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [transcripts, setTranscripts] = useState<Transcript[]>([]);

  // Refs
  const audioRef = useRef<HTMLAudioElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Helper functions
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setAudioFile(file);
      setError(null);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setRecordedAudioURL(audioUrl);
        setAudioFile(null);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      // Start audio level monitoring
      const audioContext = new AudioContext();
      const source = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      source.connect(analyser);

      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      const updateLevel = () => {
        if (!isRecording) return;
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        setAudioLevel(average / 128);
        requestAnimationFrame(updateLevel);
      };
      updateLevel();
    } catch (err) {
      setError(t('errors.failedToStartRecording'));
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }
  };

  const togglePlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
      setAudioProgress((audioRef.current.currentTime / audioRef.current.duration) * 100);
    }
  };

  const transcribeAudio = async () => {
    if (!audioFile && !recordedAudioURL) {
      setError(t('errors.uploadOrRecordFirst'));
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // TODO: Implement transcription logic
      // This is a placeholder for the actual transcription implementation
      await new Promise(resolve => setTimeout(resolve, 2000));
      setTranscript('This is a placeholder transcription. The actual transcription functionality will be implemented here.');
    } catch (err) {
      setError(t('errors.transcriptionFailed'));
    } finally {
      setIsProcessing(false);
    }
  };

  const copyTranscript = () => {
    if (transcript) {
      navigator.clipboard.writeText(transcript);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  const downloadTranscript = () => {
    if (transcript) {
      const blob = new Blob([transcript], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'transcript.txt';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // If loading, show a loading indicator
  if (subscriptionLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <FiLoader className="mx-auto h-8 w-8 animate-spin text-purple-500" />
          <p className="mt-2 text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }
  
  // If no subscription, show a subscription required message
  if (!hasSubscription) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="max-w-md p-6 bg-gray-900 rounded-xl border border-gray-800 text-center">
          <div className="mx-auto w-16 h-16 bg-purple-900/30 rounded-full flex items-center justify-center mb-4">
            <FiLock className="h-8 w-8 text-purple-400" />
          </div>
          <h2 className="text-xl font-bold text-white mb-2">{t('subscriptionRequired')}</h2>
          <p className="text-gray-400 mb-6">{t('subscriptionRequiredDescription')}</p>
          
          <div className="space-y-3">
            <Link 
              href="/dashboard/subscription"
              className="block w-full py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-md text-white transition-colors"
            >
              {t('viewSubscriptionPlans')}
            </Link>
            
            <button 
              onClick={() => router.back()}
              className="block w-full py-2 px-4 bg-gray-800 hover:bg-gray-700 rounded-md text-white transition-colors"
            >
              {t('goBack')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If subscribed, show the main content
  return (
    <div className="max-w-7xl mx-auto">
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />
      
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white">{t('title')}</h1>
        <p className="text-gray-400 mt-2">
          {t('description')}
        </p>
      </div>
      
      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left column: Model selection and settings */}
        <div className="lg:col-span-1">
          <div className="bg-gray-900 rounded-xl border border-gray-800 p-5 mb-6">
            <h2 className="text-xl font-semibold text-white mb-4">{t('modelSelection')}</h2>
            
            <ModelSelector
              modelsByCategory={modelsByCategory}
              selectedModel={selectedModel}
              onModelChange={setSelectedModel}
              defaultCategory={activeCategory}
              showAdvancedSettings={showAdvancedOptions}
              onToggleAdvancedSettings={() => setShowAdvancedOptions(!showAdvancedOptions)}
              renderCapability={(capability, index) => {
                // Generate tooltip text based on capability
                let tooltipText = "";
                if (capability.toLowerCase().includes("fast")) {
                  tooltipText = "Prioritizes speed over maximum accuracy";
                } else if (capability.toLowerCase().includes("accuracy") || capability.toLowerCase().includes("accurate")) {
                  tooltipText = "Provides the highest possible transcription quality";
                } else if (capability.toLowerCase().includes("multiple speakers") || capability.toLowerCase().includes("diarization")) {
                  tooltipText = "Can distinguish between different speakers";
                } else if (capability.toLowerCase().includes("language")) {
                  tooltipText = "Supports transcription in multiple languages";
                } else if (capability.toLowerCase().includes("timestamp")) {
                  tooltipText = "Provides precise timing for each transcribed segment";
                } else if (capability.toLowerCase().includes("professional")) {
                  tooltipText = "Designed for professional-grade transcription needs";
                } else if (capability.toLowerCase().includes("noise")) {
                  tooltipText = "Better performance with background noise present";
                }
                
                return (
                  <span 
                    key={index} 
                    className="text-xs px-2 py-1 bg-gray-700 rounded-full text-gray-300 cursor-help relative group"
                    title={tooltipText}
                  >
                    {capability}
                    {tooltipText && (
                      <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 w-40 text-center pointer-events-none z-10">
                        {tooltipText}
                      </span>
                    )}
                  </span>
                );
              }}
            />
            
            {/* Advanced Options */}
            {showAdvancedOptions && (
              <div className="mb-6 bg-gray-800 p-4 rounded-lg">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {t('language')}
                  </label>
                  <select
                    value={language}
                    onChange={(e) => setLanguage(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="auto">{t('autoDetectLanguage')}</option>
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="zh">Chinese</option>
                    <option value="ja">Japanese</option>
                    <option value="ko">Korean</option>
                    <option value="ru">Russian</option>
                    <option value="pt">Portuguese</option>
                    <option value="it">Italian</option>
                  </select>
                </div>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {t('outputFormat')}
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    {outputFormats.map((format) => (
                      <button
                        key={format.id}
                        onClick={() => setSelectedFormat(format.id)}
                        className={`format-btn px-3 py-2 text-sm rounded-md border ${
                          selectedFormat === format.id
                            ? 'selected border-purple-500 text-white'
                            : 'border-gray-700 text-gray-300 hover:border-gray-600'
                        }`}
                      >
                        {format.name}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Right column: Audio input and transcription */}
        <div className="lg:col-span-2">
          {/* Audio Input Section */}
          <div className="bg-gray-900 rounded-xl border border-gray-800 p-5 mb-6">
            <h2 className="text-xl font-semibold text-white mb-4">Audio Input</h2>
            
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              {/* Upload Button */}
              <div className="flex-1">
                <label className="block w-full cursor-pointer">
                  <div className="flex items-center justify-center px-4 py-6 border-2 border-dashed border-gray-700 hover:border-purple-500 rounded-lg text-center transition-colors">
                    <div className="space-y-2">
                      <div className="flex justify-center">
                        <FiUpload className="h-10 w-10 text-gray-400" />
                      </div>
                      <div className="text-gray-100 font-medium">{t('uploadAudioFile')}</div>
                      <p className="text-xs text-gray-400">
                        {t('supportedFormats')}
                      </p>
                    </div>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept="audio/*"
                    onChange={handleFileUpload}
                  />
                </label>
              </div>
              
              {/* Record Button */}
              <div className="flex-1">
                <div className="h-full flex items-center justify-center px-4 py-6 border-2 border-dashed border-gray-700 hover:border-purple-500 rounded-lg text-center transition-colors">
                  {isRecording ? (
                    <div className="space-y-4 w-full">
                      <div className="flex flex-col items-center">
                        {/* Microphone Level Visualization */}
                        <div className="w-full max-w-xs mb-4">
                          <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-100"
                              style={{ 
                                width: `${Math.min(100, audioLevel * 100)}%`,
                                opacity: audioLevel > 0 ? 1 : 0.5
                              }}
                            />
                          </div>
                          <div className="flex justify-between text-xs text-gray-400 mt-1">
                            <span>Quiet</span>
                            <span>Loud</span>
                          </div>
                        </div>
                        
                        {/* Timer Display */}
                        <div className="relative">
                          <div className="absolute inset-0 bg-purple-500/20 rounded-full blur-xl animate-pulse" />
                          <div className="relative bg-gray-900 rounded-full p-4 border-2 border-purple-500">
                            <span className="text-2xl font-mono font-bold text-white">
                              {formatTime(recordingTime)}
                            </span>
                          </div>
                        </div>
                        
                        <button
                          onClick={stopRecording}
                          className="mt-4 px-6 py-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-all duration-200 flex items-center shadow-lg hover:shadow-red-500/20"
                        >
                          <FiX className="mr-2" /> Stop Recording
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex justify-center">
                        <FiMic className="h-10 w-10 text-gray-400" />
                      </div>
                      <div className="text-gray-100 font-medium">Record Audio</div>
                      <button
                        onClick={startRecording}
                        className="px-6 py-2 bg-purple-700 text-white rounded-full hover:bg-purple-600 transition-all duration-200 shadow-lg hover:shadow-purple-500/20"
                      >
                        Start Recording
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* Audio Player */}
            {(recordedAudioURL || (audioFile && URL.createObjectURL(audioFile))) && (
              <div className="mb-6">
                <h3 className="text-md font-medium text-white mb-2">
                  {audioFile ? audioFile.name : "Recorded Audio"}
                </h3>
                
                <div className="bg-gray-800 rounded-lg p-4">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={togglePlayback}
                      className="h-12 w-12 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 flex items-center justify-center shadow-lg hover:shadow-purple-500/20 transition-all duration-200"
                    >
                      {isPlaying ? <FiPause className="text-white text-xl" /> : <FiPlay className="text-white text-xl" />}
                    </button>
                    
                    <audio
                      ref={audioRef}
                      src={recordedAudioURL || (audioFile && URL.createObjectURL(audioFile)) || ''}
                      onEnded={handleAudioEnded}
                      onTimeUpdate={handleTimeUpdate}
                      className="hidden"
                    />
                    
                    <div className="flex-1">
                      <div className="bg-gray-700 h-2 rounded-full overflow-hidden">
                        <div 
                          className="bg-gradient-to-r from-purple-500 to-pink-500 h-full transition-all duration-100"
                          style={{ width: `${audioProgress}%` }}
                        />
                      </div>
                      <div className="flex justify-between text-xs text-gray-400 mt-1">
                        <span>{formatTime(Math.floor(currentTime))}</span>
                        <span>{formatTime(recordingTime)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {error && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200 text-sm">
                {error}
              </div>
            )}
            
            {/* Transcribe Button */}
            <button
              onClick={transcribeAudio}
              disabled={isProcessing || (!audioFile && !recordedAudioURL)}
              className={`w-full py-3 rounded-md font-medium flex items-center justify-center ${
                isProcessing || (!audioFile && !recordedAudioURL)
                  ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  : 'bg-purple-600 hover:bg-purple-500 text-white'
              }`}
            >
              {isProcessing ? (
                <>
                  <FiLoader className="animate-spin mr-2" /> {t('processing')}
                </>
              ) : (
                t('transcribeAudio')
              )}
            </button>
          </div>
          
          {/* Transcription Results */}
          {transcript && (
            <div className="bg-gray-900 rounded-xl border border-gray-800 p-5">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-white">Transcription Result</h2>
                <div className="flex space-x-2">
                  <button
                    onClick={copyTranscript}
                    className="p-2 rounded-md bg-gray-800 hover:bg-gray-700 text-gray-300"
                    title="Copy to clipboard"
                  >
                    {copySuccess ? <FiCheck className="text-green-400" /> : <FiCopy />}
                  </button>
                  <button
                    onClick={downloadTranscript}
                    className="p-2 rounded-md bg-gray-800 hover:bg-gray-700 text-gray-300"
                    title="Download transcript"
                  >
                    <FiDownload />
                  </button>
                </div>
              </div>
              
              <div className="bg-gray-800 rounded-lg p-4 max-h-96 overflow-y-auto hide-scrollbar">
                <p className="text-gray-200 whitespace-pre-line">{transcript}</p>
              </div>
            </div>
          )}
          
          {/* Past Transcriptions */}
          {transcripts.length > 0 && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-white mb-4">Recent Transcriptions</h2>
              <div className="space-y-4">
                {transcripts.slice(0, 5).map((item) => (
                  <div key={item.id} className="bg-gray-900 rounded-xl border border-gray-800 p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-medium text-white">{item.filename}</h3>
                        <p className="text-sm text-gray-400">Duration: {formatTime(item.duration)}</p>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            navigator.clipboard.writeText(item.text);
                            setCopySuccess(true);
                            setTimeout(() => setCopySuccess(false), 2000);
                          }}
                          className="p-2 rounded-md bg-gray-800 hover:bg-gray-700 text-gray-300"
                          title="Copy to clipboard"
                        >
                          <FiCopy />
                        </button>
                      </div>
                    </div>
                    <div className="bg-gray-800 rounded-lg p-3 max-h-24 overflow-y-auto hide-scrollbar">
                      <p className="text-gray-300 text-sm">{item.text.substring(0, 150)}...</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Model selection help */}
      <div className="mt-8 mb-6 bg-gray-900/60 rounded-lg p-4 border border-gray-800">
        <div className="flex items-center text-purple-400 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <h3 className="font-medium">How to choose the right model</h3>
        </div>
        <div className="text-sm text-gray-300 space-y-2">
          <p><span className="text-purple-400 font-medium">Quick Transcription:</span> Best for fast results on clear audio in English.</p>
          <p><span className="text-purple-400 font-medium">Accurate Transcription:</span> Better for multilingual content and when accuracy is critical.</p>
          <p><span className="text-purple-400 font-medium">Specialized Models:</span> Ideal for specific scenarios like meetings or phone calls.</p>
        </div>
        
        <div className="mt-4 text-xs text-gray-400">
          <p>Need help? Hover over model capabilities to learn more about each model's strengths.</p>
        </div>
      </div>
      
      {/* Custom Styles for Scrollbar */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 5px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #1f2937;
          border-radius: 10px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #4B5563;
          border-radius: 10px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #6366F1;
        }
      `}</style>
    </div>
  );
} 