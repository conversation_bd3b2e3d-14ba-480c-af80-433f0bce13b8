"use client";

import { useState, useEffect } from 'react';
import SubscriptionManager from '@/components/SubscriptionManager';
import { FiCreditCard, FiLoader, FiSettings, FiExternalLink } from 'react-icons/fi';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useSubscription } from '@/hooks/useSubscription';
import { toast } from 'sonner';
import { useSubscriptionTranslations, useCommonTranslations } from '@/hooks/useTranslations';

export default function SubscriptionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { subscription, loading: subscriptionLoading, validateSubscription } = useSubscription();
  const [userId, setUserId] = useState<string>('');
  const [isManagingSubscription, setIsManagingSubscription] = useState(false);

  // Translation hooks
  const t = useSubscriptionTranslations();
  const common = useCommonTranslations();
  
  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);
  
  // Set user ID when session is available
  useEffect(() => {
    if (session?.user?.id) {
      setUserId(session.user.id);
    }
  }, [session]);

  // Handle Stripe customer portal for subscription management
  const handleManageSubscription = async () => {
    if (!session?.user?.email) {
      toast.error(t('errors.unableToAccess'));
      return;
    }

    try {
      setIsManagingSubscription(true);

      const response = await fetch('/api/payments/create-portal-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId: subscription?.stripeCustomerId || null, // Allow null, API will search by email
          returnUrl: window.location.href
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || t('errors.failedToCreate'));
      }

      const { url } = await response.json();

      if (url) {
        // Add a small delay to show the loading state
        setTimeout(() => {
          window.location.href = url;
        }, 500);
      } else {
        throw new Error(t('errors.noPortalUrl'));
      }
    } catch (error) {
      console.error('Error accessing subscription management:', error);
      const errorMessage = error instanceof Error ? error.message : t('errors.failedToAccess');
      toast.error(errorMessage);
    } finally {
      setIsManagingSubscription(false);
    }
  };
  
  if (status === 'loading' || subscriptionLoading) {
    return (
      <div className="h-full flex items-center justify-center p-6">
        <div className="text-center">
          <FiLoader className="animate-spin h-10 w-10 mx-auto text-[#417ef7] mb-4" />
          <p className="text-gray-400">{t('loadingSubscription')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto overflow-auto h-full">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white">{t('title')}</h1>
        <p className="text-gray-400 mt-2">
          {t('subtitle')}
        </p>
      </div>
      
      <div className="bg-[#111] rounded-xl border border-gray-800 p-6 mb-8 overflow-auto">
        <div className="flex items-center mb-6">
          <div className="h-12 w-12 rounded-full bg-gradient-to-r from-[#141493] to-[#417ef7] text-white flex items-center justify-center mr-4">
            <FiCreditCard className="h-6 w-6 text-white-50" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-white">{t('subscriptionPlans')}</h2>
            <p className="text-gray-400">{t('selectPlan')}</p>
          </div>
        </div>
        
        <SubscriptionManager userId={userId} currentPlan={subscription?.type} />
      </div>
      
      {/* Current Subscription Status */}
      {subscription && (subscription.status === 'active' || subscription.status === 'trialing') && (
        <div className="bg-[#111] rounded-xl border border-gray-800 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className="h-12 w-12 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white flex items-center justify-center mr-4">
                <FiSettings className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-white">{t('currentSubscription')}</h2>
                <p className="text-gray-400">{t('manageCurrentPlan')}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-400">{t('status')}</div>
              <div className="text-lg font-semibold text-green-400 capitalize">{subscription.status}</div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-[#1a1a1a] p-4 rounded-lg">
              <div className="text-sm text-gray-400 mb-1">{t('planType')}</div>
              <div className="text-lg font-semibold text-white">{subscription.type}</div>
            </div>
            <div className="bg-[#1a1a1a] p-4 rounded-lg">
              <div className="text-sm text-gray-400 mb-1">{t('nextBilling')}</div>
              <div className="text-lg font-semibold text-white">
                {subscription.currentPeriodEnd
                  ? new Date(subscription.currentPeriodEnd).toLocaleDateString()
                  : 'N/A'
                }
              </div>
            </div>
            <div className="bg-[#1a1a1a] p-4 rounded-lg">
              <div className="text-sm text-gray-400 mb-1">{t('subscriptionId')}</div>
              <div className="text-sm font-mono text-gray-300">
                {subscription.stripeSubscriptionId?.slice(-8) ?? 'N/A'}
              </div>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={handleManageSubscription}
              disabled={isManagingSubscription}
              className="flex items-center justify-center px-6 py-3 bg-[#417ef7] text-white rounded-lg hover:bg-[#3a6fd9] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isManagingSubscription ? (
                <>
                  <FiLoader className="animate-spin mr-2" />
                  {t('opening')}
                </>
              ) : (
                <>
                  <FiExternalLink className="mr-2" />
                  {t('manageSubscription')}
                </>
              )}
            </button>
            <button
              onClick={() => validateSubscription()}
              className="flex items-center justify-center px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                        >
              <FiLoader className="mr-2" />
              {t('refreshStatus')}
            </button>
          </div>
          
          <div className="mt-4 p-4 bg-blue-900/20 border border-blue-800 rounded-lg">
            <p className="text-blue-300 text-sm">
              <strong>{t('manageSubscription')}:</strong> {t('manageSubscriptionInfo')}
            </p>
          </div>
        </div>
      )}

      {/* Subscription Management for users without active subscription */}
      {session?.user?.email && (!subscription || (subscription.status !== 'active' && subscription.status !== 'trialing')) && (
        <div className="bg-[#111] rounded-xl border border-gray-800 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className="h-12 w-12 rounded-full bg-gradient-to-r from-gray-500 to-gray-600 text-white flex items-center justify-center mr-4">
                <FiSettings className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-white">{t('subscriptionManagement')}</h2>
                <p className="text-gray-400">{t('accessBillingHistory')}</p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={handleManageSubscription}
              disabled={isManagingSubscription}
              className="flex items-center justify-center px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isManagingSubscription ? (
                <>
                  <FiLoader className="animate-spin mr-2" />
                  {t('opening')}
                </>
              ) : (
                <>
                  <FiExternalLink className="mr-2" />
                  {t('accessBillingPortal')}
                </>
              )}
            </button>
          </div>

          <div className="mt-4 p-4 bg-gray-900/20 border border-gray-700 rounded-lg">
            <p className="text-gray-300 text-sm">
              <strong>{t('accessBillingPortal')}:</strong> {t('billingPortalInfo')}
            </p>
          </div>
        </div>
      )}

      <div className="bg-[#111] rounded-xl border border-gray-800 p-6">
        <h2 className="text-xl font-semibold text-white mb-4">{t('planFeatures')}</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-[#111] p-4 rounded-lg">
            <h3 className="text-lg font-medium text-white mb-2">{t('standardPlan')}</h3>
            <p className="text-gray-400 mb-3">{t('standardDescription')}</p>
            <ul className="space-y-2">
              <li className="text-gray-300">• {t('features.generateStunningImages')}</li>
              <li className="text-gray-300">• {t('features.createOriginalMusic')}</li>
              <li className="text-gray-300">• {t('features.transcribeAudio')}</li>
              <li className="text-gray-300">• {t('features.aiWritingAssistance')}</li>
            </ul>
          </div>

          <div className="bg-[#111] p-4 rounded-lg">
            <h3 className="text-lg font-medium text-white mb-2">{t('premiumPlan')}</h3>
            <p className="text-gray-400 mb-3">{t('premiumDescription')}</p>
            <ul className="space-y-2">
              <li className="text-[#417ef7] font-medium">• {t('features.everythingStandard')}</li>
              <li className="text-[#417ef7] font-medium">• {t('features.aiVideoGeneration')}</li>
              <li className="text-[#417ef7] font-medium">• {t('features.professionalPodcasts')}</li>
              <li className="text-[#417ef7] font-medium">• {t('features.higherQuality')}</li>
              <li className="text-[#417ef7] font-medium">• {t('features.priorityProcessing')}</li>
              <li className="text-[#417ef7] font-medium">• {t('features.longerContent')}</li>
              <li className="text-[#417ef7] font-medium">• {t('features.advancedCustomization')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}