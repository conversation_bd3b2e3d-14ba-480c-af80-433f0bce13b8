'use client';

import { useState, useRef, useEffect } from 'react';
import { FiVideo, FiX, FiImage, FiUpload, FiSend, FiLock, FiChevronDown, FiSearch, FiGrid } from 'react-icons/fi';
import { useSubscription } from '@/contexts/SubscriptionContext';
import {PaperClipIcon, ArrowUpIcon } from '@heroicons/react/24/outline';
import { toast } from 'sonner';
import { useVideoGenerationTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

// Custom CSS for extra small screens
const customStyles = `
  @media (min-width: 475px) {
    .xs\\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  /* Hide scrollbar for clean UI */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1a1a1a;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Message bubbles */
  .message-bubble-user {
    background: linear-gradient(135deg, #141493 0%, #417ef7 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 80%;
    word-wrap: break-word;
  }
  
  .message-bubble-ai {
    background: #111;
    border: 1px solid #222;
    color: #e5e5e5;
    padding: 12px 16px;
    border-radius: 18px 18px 18px 4px;
    max-width: 80%;
    word-wrap: break-word;
  }

  /* Animation for modal */
  @keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .modal-animation {
    animation: modalFadeIn 0.2s ease-out;
  }

  /* Video preview animation */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .video-animation {
    animation: fadeIn 0.5s ease-out;
  }
  
  /* Video loading pulsing animation */
  @keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
  }
  
  .loading-pulse {
    animation: pulse 1.5s infinite ease-in-out;
  }

  /* Progress bar animation */
  @keyframes progress {
    0% { width: 0%; }
    100% { width: 100%; }
  }

  .animate-progress {
    animation: progress 2s linear infinite;
  }
`;

// Define generated video interface
interface GeneratedVideo {
  id: number;
  url: string;
  prompt: string;
  timestamp: Date;
  duration: number;
  model: string;
}

// Message interface for chat UI
interface Message {
  id: string;
  content: React.ReactNode;
  isUser: boolean;
  timestamp: Date;
  provider?: string;
  model?: string;
}

// Helper function to create messages
const createMessage = (
  content: React.ReactNode,
  isUser: boolean,
  provider?: string,
  model?: string
): Message => ({
  id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
  content,
  isUser,
  timestamp: new Date(),
  provider,
  model,
});

// Add video format options for downloading
interface VideoFormat {
  id: string;
  label: string;
  extension: string;
  mimeType: string;
}

const videoFormats: VideoFormat[] = [
  { id: 'mp4', label: 'MP4 (Recommended)', extension: 'mp4', mimeType: 'video/mp4' },
  { id: 'webm', label: 'WebM', extension: 'webm', mimeType: 'video/webm' },
  { id: 'mov', label: 'MOV', extension: 'mov', mimeType: 'video/quicktime' },
];

// Define generation modes
type GenerationMode = 'text-to-video' | 'image-to-video' | 'upscale-video' | 'audio-video';

// This will be moved inside the component to access translations
const getGenerationModes = (t: any) => [
  {
    id: 'text-to-video' as GenerationMode,
    name: t('modes.textToVideo'),
    description: t('modeDescriptions.textToVideo'),
    icon: FiVideo,
  },
  {
    id: 'image-to-video' as GenerationMode,
    name: t('modes.imageToVideo'),
    description: t('modeDescriptions.imageToVideo'),
    icon: FiImage,
  },
  {
    id: 'upscale-video' as GenerationMode,
    name: t('modes.upscaleVideo'),
    description: t('modeDescriptions.upscaleVideo'),
    icon: FiUpload,
  },
  {
    id: 'audio-video' as GenerationMode,
    name: t('modes.audioVideo'),
    description: t('modeDescriptions.audioVideo'),
    icon: FiSend,
  },
];

// Define resolution options
interface Resolution {
  id: string;
  name: string;
  width: number;
  height: number;
  aspectRatio: string;
}

const resolutions: Resolution[] = [
  { id: "hd", name: "HD", width: 1280, height: 720, aspectRatio: "16:9" },
  { id: "fullhd", name: "Full HD", width: 1920, height: 1080, aspectRatio: "16:9" },
  { id: "square", name: "Square", width: 1024, height: 1024, aspectRatio: "1:1" },
  { id: "portrait", name: "Portrait", width: 720, height: 1280, aspectRatio: "9:16" },
  { id: "4k", name: "4K", width: 3840, height: 2160, aspectRatio: "16:9" },
  { id: "cinematic", name: "Cinematic", width: 2560, height: 1080, aspectRatio: "21:9" },
];

// Define duration options
interface Duration {
  id: string;
  label: string;
  seconds: number;
}

const durations: Duration[] = [
  { id: "2s", label: "2 seconds", seconds: 2 },
  { id: "3s", label: "3 seconds", seconds: 3 },
  { id: "4s", label: "4 seconds", seconds: 4 },
  { id: "5s", label: "5 seconds", seconds: 5 },
  { id: "6s", label: "6 seconds", seconds: 6 },
  { id: "8s", label: "8 seconds", seconds: 8 },
  { id: "10s", label: "10 seconds", seconds: 10 },
];

// Define AI models for video generation
interface VideoModel {
  id: string;
  name: string;
  description: string;
  provider: string;
  maxDuration: number;
  capabilities: string[];
}

// Categorized models by generation mode
const modelsByMode: Record<GenerationMode, Record<string, VideoModel[]>> = {
  'text-to-video': {
    "Quick Generation": [
      {
        id: "luma/ray-flash-2-720p",
        name: "Luma Ray Flash 2 (720p)",
        description: "Fast high-resolution generation with good quality.",
        provider: "Replicate",
        maxDuration: 5,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "luma/ray-flash-2-540p",
        name: "Luma Ray Flash 2 (540p)",
        description: "Optimized mid-resolution generation with fast performance.",
        provider: "Replicate",
        maxDuration: 7,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "luma/ray-2-720p",
        name: "Luma Ray 2 (720p)",
        description: "High-resolution video generation with cinematic quality.",
        provider: "Replicate",
        maxDuration: 6,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "luma/ray-2-540p",
        name: "Luma Ray 2 (540p)",
        description: "Mid-resolution video generation with good balance of quality and performance.",
        provider: "Replicate",
        maxDuration: 8,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "pixverse/pixverse-v4.5",
        name: "Pixverse V4.5",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 8,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "leonardoai/motion-2.0",
        name: "Leonardo Motion 2.0",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 5,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "wavespeedai/wan-2.1-t2v-720p",
        name: "Wan T2V (720p)",
        description: "High-definition text-to-video generation with realistic motion.",
        provider: "Replicate",
        maxDuration: 6,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "wavespeedai/wan-2.1-t2v-480p",
        name: "Wan T2V (480p)",
        description: "Standard definition text-to-video with faster generation times.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      }
    ],
    "Premium Models": [
      {
        id: "bytedance/seedance-1-lite",
        name: "Seedance 1 Lite",
        description: "Fast high-resolution generation with good quality.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "bytedance/seedance-1-pro",
        name: "Seedance 1 Pro",
        description: "Fast high-resolution generation with good quality.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "kwaivgi/kling-v2.1-master",
        name: "Kling V2.1 Master",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "google/veo-3",
        name: "Google Veo 3",
        description: "State-of-the-art video generation from text with realistic physics and motion.",
        provider: "Replicate",
        maxDuration: 8,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "google/veo-2",
        name: "Google Veo 2",
        description: "State-of-the-art video generation from text with realistic physics and motion.",
        provider: "Replicate",
        maxDuration: 8,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "haiper-ai/haiper-video-2",
        name: "Haiper Video 2",
        description: "Fast, high-quality video generation with detailed motion.",
        provider: "Replicate",
        maxDuration: 6,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "kwaivgi/kling-v2.0",
        name: "Kling V2.0",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
    ],
    "Fast Generation": [
      {
        id: "wavespeedai/hunyuan-video-fast",
        name: "Hunyuan Fast",
        description: "Ultra-fast video generation for quick iterations.",
        provider: "Replicate",
        maxDuration: 4,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      }
    ],
    "Additional Models": [
      {
        id: "luma/ray",
        name: "Luma Ray",
        description: "Original Luma Ray model for text-to-video generation.",
        provider: "Replicate",
        maxDuration: 5,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "minimax/video-01",
        name: "Minimax Video",
        description: "Text-to-video generation with stylistic flair.",
        provider: "Replicate",
        maxDuration: 8,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "kwaivgi/kling-v1.6-pro",
        name: "Kling Pro",
        description: "Professional quality video generation with enhanced motion and lighting.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "kwaivgi/kling-v1.6-standard",
        name: "Kling Standard",
        description: "Balanced quality and speed for text-to-video generation.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      }
    ]
  },
  'image-to-video': {
    "High Resolution": [
      {
        id: "bytedance/seedance-1-lite",
        name: "Seedance 1 Lite",
        description: "Fast high-resolution generation with good quality.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "bytedance/seedance-1-pro",
        name: "Seedance 1 Pro",
        description: "Fast high-resolution generation with good quality.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "kwaivgi/kling-v2.1-master",
        name: "Kling V2.1 Master",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "kwaivgi/kling-v2.1",
        name: "Kling V2.1",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "luma/ray-2-720p",
        name: "Luma Ray 2 (720p)",
        description: "High-resolution image animation with cinema quality.",
        provider: "Replicate",
        maxDuration: 6,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "luma/ray-2-540p",
        name: "Luma Ray 2 (540p)",
        description: "Mid-resolution image animation with balanced performance.",
        provider: "Replicate",
        maxDuration: 8,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "luma/ray-flash-2-720p",
        name: "Luma Ray Flash 2 (720p)",
        description: "Fast high-resolution image animation.",
        provider: "Replicate",
        maxDuration: 5,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "luma/ray-flash-2-540p",
        name: "Luma Ray Flash 2 (540p)",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 7,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "pixverse/pixverse-v4.5",
        name: "Pixverse V4.5",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 8,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "leonardoai/motion-2.0",
        name: "Leonardo Motion 2.0",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 5,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "kwaivgi/kling-v2.0",
        name: "Kling V2.0",
        description: "Optimized mid-resolution image animation with fast performance.",
        provider: "Replicate",
        maxDuration: 10,
        capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "wavespeedai/wan-2.1-i2v-720p",
        name: "Wan I2V (720p)",
        description: "High-definition image animation with realistic motion in 720p.",
        provider: "Replicate",
        maxDuration: 6,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "wavespeedai/wan-2.1-i2v-480p",
        name: "Wan I2V (480p)",
        description: "Standard definition image-to-video conversion with balanced performance.",
        provider: "Replicate",
        maxDuration: 8,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      }
    ],
    "Premium Models": [
      {
        id: "minimax/video-01-director",
        name: "Minimax Director",
        description: "Advanced control over motion and direction from a source image.",
        provider: "Replicate",
        maxDuration: 8,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "minimax/video-01-live",
        name: "Minimax Live",
        description: "Real-time animation of images with fluid motion.",
        provider: "Replicate",
        maxDuration: 10,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      }
    ],
    "Standard Models": [
      {
        id: "kwaivgi/kling-v1.6-pro",
        name: "Kling Pro",
        description: "Professional quality image animation with smooth motion.",
        provider: "Replicate",
        maxDuration: 10,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "kwaivgi/kling-v1.6-standard",
        name: "Kling Standard",
        description: "Balanced quality and speed for image animation.",
        provider: "Replicate",
        maxDuration: 10,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      }
    ]
  },
  'upscale-video': {
    "Enhancement Models": [
      {
        id: "lucataco/real-esrgan-video",
        name: "Real-ESRGAN Video",
        description: "High-quality video upscaling with multiple resolution options.",
        provider: "Replicate",
        maxDuration: 60,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      },
      {
        id: "tencentarc/animesr",
        name: "AnimeSR",
        description: "Specialized upscaling for animation and anime content.",
        provider: "Replicate",
        maxDuration: 60,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      }
    ]
  },
  'audio-video': {
    "Audio Generation": [
      {
        id: "zsxkib/mmaudio:62871fb59889b2d7c13777f08deb3b36bdff88f7e1d53a50ad7694548a41b484",
        name: "MM Audio",
        description: "Add AI-generated audio to existing videos based on text prompts.",
        provider: "Replicate",
        maxDuration: 8,
                capabilities: ["Text-to-video", "High resolution", "Fast generation"]
      }
    ]
  }
};

export default function VideoGeneration() {
  const router = useRouter();
  const { subscription, hasAccess } = useSubscription();

  // Translation hooks
  const t = useVideoGenerationTranslations();
  const common = useCommonTranslations();

  // Get translated generation modes
  const generationModes = getGenerationModes(t);

  // Mode selection state
  const [mode, setMode] = useState<GenerationMode>('text-to-video');

  // State for generation parameters
  const [prompt, setPrompt] = useState<string>("");
  const [negativePrompt, setNegativePrompt] = useState<string>("");
  const [duration, setDuration] = useState<number>(4);
  const [fps, setFps] = useState<number>(24);
  const [selectedResolution, setSelectedResolution] = useState<string>('fullhd');
  const [selectedDuration, setSelectedDuration] = useState<string>('5s');
  const [activeCategory, setActiveCategory] = useState<string>(Object.keys(modelsByMode[mode])[0]);
  const [selectedModel, setSelectedModel] = useState<string>(modelsByMode[mode][Object.keys(modelsByMode[mode])[0]][0].id);

  // State for the UI
  const [error, setError] = useState("");
  const [copySuccess, setCopySuccess] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [videoToDownload, setVideoToDownload] = useState<string>('');
  const [selectedFormat, setSelectedFormat] = useState<VideoFormat>(videoFormats[0]);
  const [generatedVideos, setGeneratedVideos] = useState<GeneratedVideo[]>([]);
  const [videoPreview, setVideoPreview] = useState<GeneratedVideo | null>(null);
  
  // State for image-to-video mode
  const [sourceImage, setSourceImage] = useState<string | null>(null);
  const [imageUploadError, setImageUploadError] = useState('');
  
  // State for upscale-video mode
  const [sourceVideo, setSourceVideo] = useState<string | null>(null);
  const [videoUploadError, setVideoUploadError] = useState('');
  
  // State for audio-video mode
  const [audioPrompt, setAudioPrompt] = useState('');
  const [audioNegativePrompt, setAudioNegativePrompt] = useState('music');
  
  // Chat UI state - Initialize messages from localStorage or empty array
  const [messages, setMessages] = useState<Message[]>(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedMessages = localStorage.getItem('astrostudio-video-generation-messages');
        return savedMessages ? JSON.parse(savedMessages) : [];
      } catch (error) {
        console.error('Error loading messages from localStorage:', error);
        return [];
      }
    }
    return [];
  });
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState<boolean>(false);
  const [modelSearchQuery, setModelSearchQuery] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [isResolutionSelectorOpen, setIsResolutionSelectorOpen] = useState(false);
  const [isDurationSelectorOpen, setIsDurationSelectorOpen] = useState(false);
  const [isModeDropdownOpen, setIsModeDropdownOpen] = useState(false);
  
  // Save messages to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('astrostudio-video-generation-messages', JSON.stringify(messages));
      } catch (error) {
        console.error('Error saving messages to localStorage:', error);
      }
    }
  }, [messages]);

  // Refs
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const sourceVideoRef = useRef<HTMLVideoElement | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle mode change
  const handleModeChange = (newMode: GenerationMode) => {
    setMode(newMode);
    
    // Reset errors
    setError("");
    setImageUploadError("");
    setVideoUploadError("");
    
    // Set default category and model for the new mode
    const defaultCat = Object.keys(modelsByMode[newMode])[0];
    setActiveCategory(defaultCat);
    setSelectedModel(modelsByMode[newMode][defaultCat][0].id);
  };

  // Handle category change
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    
    // Don't auto-select first model when changing category to preserve user selection if possible
    const currentModeModels = modelsByMode[mode];
    if (!currentModeModels[category].some(model => model.id === selectedModel)) {
      setSelectedModel(currentModeModels[category][0].id);
    }
  };

  // Handle resolution change
  const handleResolutionChange = (resolutionId: string) => {
    setSelectedResolution(resolutionId);
  };

  // Get the currently selected model
  const getSelectedModel = (): VideoModel | undefined => {
    for (const category of Object.keys(modelsByMode[mode])) {
      const model = modelsByMode[mode][category].find(m => m.id === selectedModel);
      if (model) {
        return model;
      }
    }
    return undefined;
  };

  // Get max duration for the selected model
  const getMaxDuration = (): number => {
    const model = getSelectedModel();
    return model ? model.maxDuration : 10;
  };

  // Handle model change
  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
  };

  // Handle video generation
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error(t('errors.promptRequired'));
      return;
    }

    try {
      setError("");
      setIsGenerating(true);

      // Add user message
      setMessages(prev => [
        ...prev,
        createMessage(
          <div>
            <span>{prompt}</span>
          </div>,
          true,
          undefined,
          selectedModel
        )
      ]);

      // API call based on mode
      const selectedRes = resolutions.find(r => r.id === selectedResolution);
      const selectedDur = durations.find(d => d.id === selectedDuration);

      let apiEndpoint = '/api/generate-video';
      let payload: any = {};

      if (mode === 'upscale-video') {
        // Upscale video mode
        if (!sourceVideo) {
          toast.error(t('errors.videoRequired'));
          setIsGenerating(false);
          return;
        }
        apiEndpoint = '/api/upscale-video';
        payload = {
          model: selectedModel,
          video_path: sourceVideo,
          resolution: selectedRes?.name ?? 'FHD',
          model_type: 'RealESRGAN_x4plus'
        };
      } else if (mode === 'audio-video') {
        // Add audio to video mode
        if (!sourceVideo) {
          toast.error(t('errors.videoForAudioRequired'));
          setIsGenerating(false);
          return;
        }
        apiEndpoint = '/api/add-audio';
        payload = {
          video: sourceVideo,
          prompt: prompt,
          negative_prompt: negativePrompt || 'music',
          duration: selectedDur?.seconds ?? 8,
          seed: -1,
          num_steps: 25,
          cfg_strength: 4.5
        };
      } else {
        // Text-to-video and image-to-video modes
        payload = {
          model: selectedModel,
          prompt: prompt,
          negative_prompt: negativePrompt,
          width: selectedRes?.width ?? 1920,
          height: selectedRes?.height ?? 720,
          duration: selectedDur?.seconds ?? 5,
          fps: fps,
          mode: mode,
        };

        if (mode === 'image-to-video' && sourceImage) {
          payload={
            model: selectedModel,
            image: sourceImage,
            prompt: prompt,
            negative_prompt: negativePrompt,
          }
        }
      }

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error ?? t('errors.generationFailed'));
      }

      const result = await response.json();

      const newVideo: GeneratedVideo = {
        id: Date.now(),
        url: result.videoUrl, // Use videoUrl from API response
        prompt,
        timestamp: new Date(),
        duration: selectedDur?.seconds ?? 5,
        model: selectedModel,
      };

      setGeneratedVideos(prev => [newVideo, ...prev]);
      setVideoPreview(newVideo);

      // Add AI message with generated video
      setMessages(prev => [
        ...prev,
        createMessage(
          <div className="space-y-3">
            <div className="relative aspect-video bg-gray-800 rounded-lg overflow-hidden">
              <video
                src={newVideo.url}
                controls
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex justify-between items-center text-xs text-gray-400">
              <span>{selectedDur?.seconds ?? 5}s • {selectedRes?.width ?? 1920}x{selectedRes?.height ?? 720}</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
          </div>,
          false,
          getSelectedModel()?.provider,
          selectedModel
        )
      ]);

      // Clear prompt
      setPrompt("");
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message || t('errors.generationFailed'));
        toast.error(err.message || t('errors.generationFailed'));
      } else {
        setError(t('errors.generationFailed'));
        toast.error(t('errors.generationFailed'));
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle file upload for image-to-video
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (mode === 'image-to-video' && !file.type.startsWith('image/')) {
      setImageUploadError(t('errors.invalidImageFile'));
      return;
    }

    if ((mode === 'upscale-video' || mode === 'audio-video') && !file.type.startsWith('video/')) {
      setVideoUploadError(t('errors.invalidVideoFile'));
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      const errorMsg = t('errors.fileSizeTooLarge');
      if (mode === 'image-to-video') {
        setImageUploadError(errorMsg);
      } else {
        setVideoUploadError(errorMsg);
      }
      return;
    }

    // Create file URL
    const fileUrl = URL.createObjectURL(file);
    
    if (mode === 'image-to-video') {
      setSourceImage(fileUrl);
      setImageUploadError('');
    } else {
      setSourceVideo(fileUrl);
      setVideoUploadError('');
    }
  };

  // Copy prompt to clipboard
  const copyPrompt = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      toast.success(t('success.promptCopied'));
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
      setError(t('errors.failedToCopyText'));
    }
  };

  // Download video
  const downloadVideo = (url: string) => {
    setVideoToDownload(url);
    setShowDownloadModal(true);
  };

  // Get category description for model selector
  const getCategoryDescription = (category: string): string => {
    const descriptions: Record<string, string> = {
      "Quick Generation": "Fast and efficient models",
      "Premium Models": "High-quality premium models",
      "Animation Models": "Specialized for image animation",
      "Enhancement Models": "Video quality enhancement",
      "Audio Generation": "Audio and music generation"
    };
    return descriptions[category] || category;
  };

  // Check if user has access to video generation
  if (!subscription) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <FiLock className="mx-auto text-6xl text-gray-500 mb-4" />
          <h1 className="text-2xl font-bold mb-4">Video Generation</h1>
          <p className="text-gray-400 mb-6">
            Video generation is available for subscribers only. Upgrade your plan to access this feature.
          </p>
          <div className="space-y-3">
            <Link
              href="/dashboard/subscription"
              className="block w-full py-2 px-4 bg-gradient-to-r from-[#141493] to-[#417ef7] hover:opacity-90 rounded-md text-white transition-colors"
            >
              View Subscription Plans
            </Link>
            
            <button 
              onClick={() => router.back()}
              className="block w-full py-2 px-4 bg-[#111] hover:bg-[#222] rounded-md text-white transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <style jsx global>{customStyles}</style>
      
      {/* Chat Interface with full layout */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <div className="h-full flex flex-col bg-black rounded-xl">
          <div className="flex items-center justify-between p-4 border-b border-[#222]">
            <h1 className="text-xl font-medium text-white">{t('mainTitle')}</h1>
            <div className="flex items-center gap-2">
              {/* Mode Selection - Desktop: horizontal buttons, Mobile: dropdown */}
              <div className="hidden md:flex items-center gap-1">
                {generationModes.map((modeOption) => (
                  <button
                    key={modeOption.id}
                    className={`flex items-center gap-1 px-3 py-1.5 rounded-lg text-xs transition ${
                      mode === modeOption.id
                        ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                        : "bg-[#222] hover:bg-[#333] text-gray-300"
                    }`}
                    onClick={() => handleModeChange(modeOption.id)}
                  >
                    <modeOption.icon className="text-sm" />
                    <span className="font-medium">{modeOption.name}</span>
                  </button>
                ))}
              </div>
              
              {/* Mobile Mode Dropdown */}
              <div className="md:hidden relative">
                <button
                  onClick={() => setIsModeDropdownOpen(!isModeDropdownOpen)}
                  className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 text-sm"
                >
                  {(() => {
                    return (
                      <>
                        {(() => {
                          const selectedMode = generationModes.find(m => m.id === mode);
                          if (selectedMode?.icon) {
                            const Icon = selectedMode.icon;
                            return <Icon className="text-sm" />;
                          }
                          return null;
                        })()}
                        <span className="font-medium">{generationModes.find(m => m.id === mode)?.name ?? 'Select Mode'}</span>
                        <FiChevronDown className={`transition-transform ${isModeDropdownOpen ? 'rotate-180' : ''}`} />
                      </>
                    );
                  })()} 
                </button>
                
                <AnimatePresence>
                  {isModeDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute right-0 top-full mt-1 z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg min-w-[200px]"
                    >
                      <div className="p-2">
                        {generationModes.map((modeOption) => (
                          <button
                            key={modeOption.id}
                            className={`flex items-center gap-2 w-full px-3 py-2 rounded-lg text-sm transition ${
                              mode === modeOption.id
                                ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                : "hover:bg-[#222] text-gray-300"
                            }`}
                            onClick={() => {
                              handleModeChange(modeOption.id);
                              setIsModeDropdownOpen(false);
                            }}
                          >
                            <modeOption.icon className="text-sm" />
                            <span className="font-medium">{modeOption.name}</span>
                          </button>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              
              <button
                className="flex items-center text-gray-400 hover:text-white text-sm bg-[#222] hover:bg-[#333] px-3 py-1.5 rounded-lg"
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              >
                <FiGrid className="mr-2" />
                {showAdvancedOptions ? "Hide" : "Show"} Options
              </button>
            </div>
          </div>
          
          {/* Advanced Options Panel */}
          {showAdvancedOptions && (
            <div className="bg-[#111] border-b border-[#222] p-4">
              <div className="flex flex-wrap gap-4 items-center">
                {/* Resolution Selector */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">Resolution:</span>
                  <div className="relative">
                    <button
                      onClick={() => setIsResolutionSelectorOpen(!isResolutionSelectorOpen)}
                      className="flex items-center justify-between px-3 py-1.5 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-sm min-w-[120px]"
                    >
                      <span>
                        {resolutions.find(r => r.id === selectedResolution)?.name ?? selectedResolution}
                      </span>
                      <FiChevronDown className={`ml-2 transition-transform ${isResolutionSelectorOpen ? 'rotate-180' : ''}`} />
                    </button>
                    
                    <AnimatePresence>
                      {isResolutionSelectorOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 10 }}
                          transition={{ duration: 0.2 }}
                          className="absolute left-0 top-full mt-1 z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                        >
                          <div className="p-2 max-h-[300px] overflow-y-auto custom-scrollbar">
                            {resolutions.map((resolution) => (
                              <button
                                key={resolution.id}
                                className={`p-3 rounded-lg text-left transition w-full ${selectedResolution === resolution.id
                                    ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                    : "hover:bg-[#222] text-gray-300"
                                  }`}
                                onClick={() => {
                                  setSelectedResolution(resolution.id);
                                  setIsResolutionSelectorOpen(false);
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div>
                                    <div className="text-sm font-medium">{resolution.name}</div>
                                    <div className="text-xs text-gray-400">{resolution.width} x {resolution.height} ({resolution.aspectRatio})</div>
                                  </div>
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedResolution === resolution.id && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </button>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
                
                {/* Duration Selector */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">Duration:</span>
                  <div className="relative">
                    <button
                      onClick={() => setIsDurationSelectorOpen(!isDurationSelectorOpen)}
                      className="flex items-center justify-between px-3 py-1.5 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-sm min-w-[100px]"
                    >
                      <span>
                        {durations.find(d => d.id === selectedDuration)?.label ?? selectedDuration}
                      </span>
                      <FiChevronDown className={`ml-2 transition-transform ${isDurationSelectorOpen ? 'rotate-180' : ''}`} />
                    </button>
                    
                    <AnimatePresence>
                      {isDurationSelectorOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 10 }}
                          transition={{ duration: 0.2 }}
                          className="absolute left-0 top-full mt-1 z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                        >
                          <div className="p-2 max-h-[250px] overflow-y-auto custom-scrollbar">
                            {durations.map((duration) => (
                              <button
                                key={duration.id}
                                className={`p-3 rounded-lg text-left transition w-full ${selectedDuration === duration.id
                                    ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                    : "hover:bg-[#222] text-gray-300"
                                  }`}
                                onClick={() => {
                                  setSelectedDuration(duration.id);
                                  setIsDurationSelectorOpen(false);
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div>
                                    <div className="text-sm font-medium">{duration.label}</div>
                                  </div>
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedDuration === duration.id && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </button>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Chat Interface */}
          <div className="flex flex-col h-full overflow-y-auto custom-scrollbar">
              {/* Message area */}
              <div className="flex-1 p-4 overflow-y-auto auto-scroll custom-scrollbar">
                {messages.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-full text-center text-gray-400 space-y-6 py-10">
                    <div className="flex flex-col items-center">
                      <Image src="/Logo Web.webp" alt="AstroStudio AI" width={200} height={70} className="mb-4" />
                      <p className="text-2xl font-medium text-gray-300 mb-4">{t('aiVideoGeneration')}</p>
                      <p className="max-w-lg mx-auto">
                        {t('description')}
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl">
                      <button
                        className="bg-[#111] hover:bg-[#222] rounded-xl p-4 text-left transition-colors"
                        onClick={() => {
                          setPrompt("A cinematic shot of a person walking through a futuristic city at sunset, with flying cars in the background");
                        }}
                      >
                        <p className="font-medium mb-1">{t('examples.cinematicScene.title')}</p>
                        <p className="text-sm text-gray-500">{t('examples.cinematicScene.description')}</p>
                      </button>
                      <button
                        className="bg-[#111] hover:bg-[#222] rounded-xl p-4 text-left transition-colors"
                        onClick={() => {
                          setPrompt("A time-lapse of flowers blooming in a garden, with butterflies flying around");
                        }}
                      >
                        <p className="font-medium mb-1">{t('examples.natureTimelapse.title')}</p>
                        <p className="text-sm text-gray-500">{t('examples.natureTimelapse.description')}</p>
                      </button>
                    </div>
                  </div>
                )}

                {/* Messages */}
                <div className="space-y-6">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} max-w-full`}
                    >
                      <div className={`flex ${message.isUser ? 'flex-row-reverse' : 'flex-row'} items-start gap-3 max-w-[90%]`}>
                        {/* Message content */}
                        <div
                          className={`${
                            message.isUser
                              ? 'message-bubble-user'
                              : 'message-bubble-ai'
                          } max-w-full overflow-hidden`}
                        >
                          <div className="break-words">{message.content}</div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Loading indicator */}
                  {isGenerating && (
                    <div className="flex justify-start max-w-full">
                      <div className="flex flex-row items-start gap-3 max-w-[90%]">
                        <div className="message-bubble-ai">
                          <div className="flex space-x-2 items-center h-5">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Input area with model selector */}
              <div className="border-t border-[#222] p-4">
                {/* Model selector */}
                <div className="relative">
                  <button
                    onClick={() => setIsModelSelectorOpen(!isModelSelectorOpen)}
                    className="flex items-center justify-between w-full px-3 py-2 rounded-lg bg-[#111] hover:bg-[#181818] transition-colors mb-3"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                        <span className="text-xs font-semibold text-white">
                          AI
                        </span>
                      </div>
                      <span className="text-sm text-gray-200">
                        {(() => {
                          const model = getSelectedModel();
                          return model ? model.name : "Select a model";
                        })()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FiChevronDown className={`text-gray-400 transition-transform ${isModelSelectorOpen ? 'rotate-180' : ''}`} />
                    </div>
                  </button>

                  {/* Model selector dropdown */}
                  <AnimatePresence>
                    {isModelSelectorOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                      >
                        {/* Model search */}
                        <div className="p-2 border-b border-[#222]">
                          <div className="relative">
                            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                              type="text"
                              value={modelSearchQuery}
                              onChange={(e) => setModelSearchQuery(e.target.value)}
                              placeholder="Search models..."
                              className="w-full bg-[#181818] text-gray-300 pl-9 pr-3 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                            />
                          </div>
                        </div>

                        {/* Model list */}
                        <div className="p-2 max-h-[350px] overflow-y-auto custom-scrollbar bg-black/30 backdrop-blur-sm">
                          {Object.keys(modelsByMode[mode])
                            .flatMap(category =>
                              modelsByMode[mode][category].map(model => ({
                                ...model,
                                category
                              }))
                            )
                            .filter(model =>
                              !modelSearchQuery ||
                              model.name.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                              model.provider.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                              getCategoryDescription(model.category).toLowerCase().includes(modelSearchQuery.toLowerCase())
                            )
                            .map(model => (
                              <button
                                key={model.id}
                                className={`p-3 rounded-lg text-left transition w-full ${
                                  selectedModel === model.id
                                    ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                    : "hover:bg-[#222] text-gray-300"
                                }`}
                                onClick={() => {
                                  setSelectedModel(model.id);
                                  setIsModelSelectorOpen(false);
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-2">
                                    <div className="w-8 h-8 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                                      <span className="text-xs font-semibold text-white">
                                        AI
                                      </span>
                                    </div>
                                    <div>
                                      <div className="text-sm font-medium">{model.name}</div>
                                      <div className="text-xs text-gray-400">{getCategoryDescription(model.category)}</div>
                                    </div>
                                  </div>
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedModel === model.id && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </button>
                            ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Input form */}
                <form onSubmit={(e) => {
                  e.preventDefault();
                  if (!prompt.trim() || isGenerating) return;
                  handleGenerate();
                }} className="relative mt-3">
                  <div className="flex flex-col">
                    {/* File preview area when file is uploaded */}
                    {(sourceImage || sourceVideo) && (
                      <div className="mb-3 bg-[#111] rounded-lg overflow-hidden inline-block max-w-[200px] relative">
                        <div className="relative">
                          {sourceImage && (
                            <img
                              src={sourceImage}
                              alt="Uploaded image"
                              className="h-[120px] w-[200px] object-cover"
                            />
                          )}
                          {sourceVideo && (
                            <video
                              src={sourceVideo}
                              className="h-[120px] w-[200px] object-cover"
                              controls={false}
                            />
                          )}
                          <button
                            onClick={() => {
                              setSourceImage(null);
                              setSourceVideo(null);
                            }}
                            className="absolute top-1 right-1 p-0.5 bg-black bg-opacity-70 rounded-full hover:bg-opacity-100 transition-all"
                            type="button"
                          >
                            <FiX size={12} className="text-white" />
                          </button>
                        </div>
                      </div>
                    )}

                    <div className="relative">
                      <textarea
                        className="w-full bg-[#111] border border-[#222] rounded-lg py-3 px-4 pr-12 text-gray-200 focus:outline-none focus:ring-1 focus:ring-[#417ef7] resize-none min-h-[80px] max-h-[150px]"
                        placeholder={mode === 'text-to-video' ? t('placeholders.textToVideo') :
                                   mode === 'image-to-video' ? t('placeholders.imageToVideo') :
                                   mode === 'upscale-video' ? t('placeholders.upscaleVideo') :
                                   t('placeholders.audioVideo')}
                        value={prompt}
                        onChange={(e) => {
                          setPrompt(e.target.value);
                          // Auto-resize
                          e.target.style.height = 'inherit';
                          e.target.style.height = `${Math.min(e.target.scrollHeight, 150)}px`;
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            if (!prompt.trim() || isGenerating) return;
                            handleGenerate();
                            e.currentTarget.style.height = 'auto';
                          }
                        }}
                        disabled={isGenerating}
                        rows={3}
                      />
                      <div className="absolute right-3 bottom-3 flex items-center gap-2">
                        {/* Upload button for image/video modes */}
                        {(mode === 'image-to-video' || mode === 'upscale-video' || mode === 'audio-video') && (
                          <label className="p-2 sm:p-1.5 rounded-lg cursor-pointer hover:bg-[#222] transition-colors min-h-[44px] sm:min-h-auto flex items-center justify-center">
                            <PaperClipIcon className="w-4 h-4 text-gray-400" />
                            <input
                              type="file"
                              accept={mode === 'image-to-video' ? "image/*" : "video/*"}
                              className="hidden"
                              onChange={handleFileUpload}
                              ref={fileInputRef}
                            />
                          </label>
                        )}

                        {/* Send button */}
                        <button
                          type="submit"
                          className={`p-2 sm:p-1.5 rounded-lg flex-shrink-0 min-h-[44px] sm:min-h-auto flex items-center justify-center ${
                            prompt.trim() && !isGenerating
                              ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white hover:opacity-90'
                              : 'bg-[#222] text-gray-500 cursor-not-allowed'
                          } transition-colors`}
                          disabled={!prompt.trim() || isGenerating}
                        >
                          <ArrowUpIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-2 text-center hidden md:block">
                    Press Enter to send, Shift+Enter for new line. {(mode === 'image-to-video' || mode === 'upscale-video' || mode === 'audio-video') && 'Upload a file to get started.'}
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
    </div>
  );
}