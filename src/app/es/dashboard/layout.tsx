import DashboardLayout from '../../dashboard/layout';
import SpanishPageWrapper from '@/components/SpanishPageWrapper';
import { ReactNode } from 'react';

interface SpanishDashboardLayoutProps {
  children: ReactNode;
}

export default function SpanishDashboardLayout({ children }: SpanishDashboardLayoutProps) {
  return (
    <SpanishPageWrapper>
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </SpanishPageWrapper>
  );
}
