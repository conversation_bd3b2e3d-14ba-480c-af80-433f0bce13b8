"use client";

import { useState, Suspense } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiMail, FiArrowLeft, FiAlertCircle, FiCheckCircle } from 'react-icons/fi';
import { useLocale, createLocaleUrl } from '@/hooks/useLocale';
import { useTranslations } from '@/hooks/useTranslations';
import { z } from 'zod';

// Validation schema - we'll validate with translations in the component
const forgotPasswordSchema = z.object({
  email: z.string().email().min(1),
});

function ForgotPasswordForm() {
  const locale = useLocale();
  const t = useTranslations('auth.forgotPassword');

  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form data
    const validationResult = forgotPasswordSchema.safeParse({ email });
    if (!validationResult.success) {
      const emailError = validationResult.error.errors.find(err => err.path[0] === 'email');
      if (emailError) {
        if (emailError.code === 'invalid_string' && emailError.validation === 'email') {
          setError(t('errors.emailInvalid'));
        } else {
          setError(t('errors.emailRequired'));
        }
      }
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          locale,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Use translated error messages
        if (data.error === 'RATE_LIMITED') {
          setError(t('errors.rateLimited'));
        } else if (data.error === 'SEND_FAILED') {
          setError(t('errors.sendFailed'));
        } else {
          setError(t('errors.userNotFound'));
        }
        return;
      }

      setSuccess(true);
    } catch (err) {
      console.error('Forgot password error:', err);
      setError(t('errors.sendFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-black relative overflow-hidden px-4">
      {/* Background effects with blue gradient theme */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#141493]/20 via-black to-[#417ef7]/20"></div>
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-[#141493]/10 to-[#417ef7]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-[#417ef7]/10 to-[#141493]/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-md w-full space-y-8 glass-effect p-10 shadow-2xl">
        <div className="text-center">
          <Link href={createLocaleUrl('/', locale)} className="inline-block mb-6">
            <Image
              src="/Logo Web.webp"
              alt="AstroStudio AI"
              width={200}
              height={70}
              className="mb-4"
            />
          </Link>
          <h1 className="text-3xl font-extrabold text-white">{t('title')}</h1>
          <p className="mt-2 text-sm text-gray-400">
            {t('subtitle')}
          </p>
        </div>
        
        {success ? (
          <div className="mt-6">
            <div className="bg-green-900/20 border border-green-500/30 p-4 rounded-md mb-6">
              <div className="flex items-center">
                <FiCheckCircle className="h-5 w-5 text-green-400" />
                <div className="ml-3">
                  <p className="text-sm text-green-300">
                    {t('success')}
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center">
              <Link href={createLocaleUrl('/login', locale)} className="inline-flex items-center text-sm font-medium text-[#417ef7] hover:text-[#2563eb] transition duration-150">
                <FiArrowLeft className="mr-2 h-4 w-4" />
                {t('backToLogin')}
              </Link>
            </div>
          </div>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-900/20 border border-red-500/30 p-4 rounded-md">
                <div className="flex items-center">
                  <FiAlertCircle className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <p className="text-sm text-red-300">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div>
              <label htmlFor="email" className="sr-only">{t('email')}</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMail className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                  placeholder={t('email')}
                />
              </div>
            </div>
            
            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-[#141493] to-[#417ef7] hover:from-[#0f0f7a] hover:to-[#2563eb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#417ef7] transition duration-150 disabled:opacity-70"
              >
                {isLoading ? t('sending') : t('sendResetLink')}
              </button>
            </div>

            <div className="text-center pt-4">
              <Link href={createLocaleUrl('/login', locale)} className="inline-flex items-center text-sm font-medium text-[#417ef7] hover:text-[#2563eb] transition duration-150">
                <FiArrowLeft className="mr-2 h-4 w-4" />
                {t('backToLogin')}
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

export default function ForgotPasswordPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center bg-black">
      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#417ef7]"></div>
    </div>}>
      <ForgotPasswordForm />
    </Suspense>
  );
}