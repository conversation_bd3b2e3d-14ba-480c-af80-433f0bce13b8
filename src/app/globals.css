@import "tailwindcss";

@theme {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

:root {
  --primary: #417ef7;   
  --secondary: #141493;   /* <PERSON><PERSON>/Teal */
  --accent: 255, 99, 71;      /* Coral */
  --background: 0, 0, 0;      /* Black */
  --foreground: 255, 255, 255; /* White */
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  body {
    @apply bg-black text-white min-h-screen;
    background: radial-gradient(circle at 50% 50%, rgba(var(--primary), 0.15), transparent 70%),
                radial-gradient(circle at 80% 20%, rgba(var(--secondary), 0.1), transparent 50%);
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-[#141493] to-[#417ef7] hover:from-[#417ef7] hover:to-[#141493] text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-[#417ef7]/25;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-[#417ef7]/30 text-[#417ef7] hover:border-[#417ef7] hover:bg-[#417ef7]/10 font-bold py-3 px-6 rounded-xl transition-all duration-300;
  }
  
  .card {
    @apply bg-gray-900 border border-gray-800 rounded-xl p-6 shadow-lg hover:shadow-purple-900/20 transition-all duration-300;
    background: linear-gradient(to bottom right, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.8));
    backdrop-filter: blur(12px);
  }
  
  .glass-effect {
    @apply bg-black/30 backdrop-blur-md border border-white/10 rounded-xl;
  }
  
  /* Hide scrollbar for clean UI */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* No scrollbar for sidebar */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow: hidden;
  }

  /* Custom scrollbar for other areas */
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #374151;
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #417ef7;
  }

  /* Firefox scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #374151 transparent;
  }
  
  /* Checkerboard pattern for transparent images */
  .bg-checkerboard {
    background-color: #1f2937;
    background-image:
      linear-gradient(45deg, #2d3748 25%, transparent 25%),
      linear-gradient(-45deg, #2d3748 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #2d3748 75%),
      linear-gradient(-45deg, transparent 75%, #2d3748 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  }

  /* Modern animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(65, 126, 247, 0.3); }
    50% { box-shadow: 0 0 30px rgba(65, 126, 247, 0.5); }
  }

  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Splash cursor effect */
  .splash-cursor {
    cursor: none;
  }

  .splash-cursor::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(65, 126, 247, 0.8) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transform: translate(-50%, -50%);
    transition: all 0.1s ease;
  }
}
