import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from '@/lib/AuthProvider';
import { SubscriptionProvider } from '@/contexts/SubscriptionContext';
import DynamicTitle from '@/lib/DynamicTitle';
import Script from 'next/script';
import { NextIntlClientProvider } from 'next-intl';
import enMessages from '@/i18n/messages/en.json';
import { LanguageProvider } from '@/contexts/LanguageContext';


const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "AstroStudio AI - AI-Powered Multimedia Generation",
    template: "AstroStudio AI"
  },
  description: "Create stunning images, edit photos, generate videos, produce podcasts, and compose music with our advanced AI tools. Start for free, no credit card required.",
  applicationName: "Astro Studio AI",
  keywords: "AI, image generation, video creation, podcast, music, artificial intelligence, content creation, AI tools, creative AI",
  verification: {
    google: "M9TO2VdwZ58BgJjJ1e9ennk4E-D-W7qaCpmMI2r9uHw",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/ProfileLogo.ico', type: 'image/svg+xml' },
      { url: 'ProfileLogo.ico', type: 'image/ico', sizes: '192x192' },
      { url: 'Logo Perfil.png', type: 'image/png', sizes: '512x512' },
    ],
    shortcut: ['/ProfileLogo.ico'],
    apple: [
      { url: '/icons/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
  },
  openGraph: {
    title: "AstroStudio AI - AI-Powered Multimedia Generation",
    description: "Generate stunning images, videos, music, podcasts, and more with our powerful AI creation platform",
    url: "https://www.astrostudioai.com",
    siteName: "AstroStudio AI",
    images: [
      {
        url: "/ProfileLogo.ico",
        width: 520,
        height: 424,
        alt: "AstroStudio AI Logo",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AstroStudio AI - AI-Powered Multimedia Generation",
    description: "Unlock creativity with state-of-the-art AI tools for generating images, videos, music, and more",
    images: ["/ProfileLogo.ico"],
    site: "@AstroStudioAI",
    creator: "@AstroStudioAI"
  },
  metadataBase: new URL("https://www.astrostudioai.com"),
  alternates: {
    canonical: "/",
    languages: {
      'en': '/',
      'es': '/es'
    }
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Include Stripe JS for enhanced capabilities */}
        <Script src="https://js.stripe.com/v3/" strategy="beforeInteractive" />

        {/* Script to ensure promotion codes are enabled */}
        <Script id="enable-stripe-promotions" strategy="afterInteractive">
          {`
            if (window.Stripe) {
              window.addEventListener('load', function() {
                // Configure Stripe elements to always show promotion code field
                const checkForStripeForm = setInterval(() => {
                  const form = document.querySelector('form.ElementsApp');
                  if (form) {
                    // Add a custom attribute to signal we want promotion codes visible
                    form.setAttribute('data-show-promotion-code', 'true');

                    // Look for the promotion code toggle button and click it if found
                    const promoButton = document.querySelector('.PromotionCode-toggle');
                    if (promoButton) {
                      try {
                        promoButton.click();
                      } catch (e) {
                        console.log('Could not expand promotion code field automatically');
                      }
                      clearInterval(checkForStripeForm);
                    }
                  }
                }, 1000);
              });
            }
          `}
        </Script>
      </head>
      <body className={`${inter.className} bg-black text-white antialiased`}>
        <LanguageProvider>
          <NextIntlClientProvider messages={enMessages} locale="en">
            <AuthProvider>
              <SubscriptionProvider>
                <DynamicTitle />
                {children}
              </SubscriptionProvider>
            </AuthProvider>
          </NextIntlClientProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
