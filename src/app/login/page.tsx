"use client";

import { useState, useEffect, Suspense } from 'react';
import { signIn, getSession } from 'next-auth/react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { FiMail, FiLock, FiAlertCircle, FiArrowRight, FiLogIn } from 'react-icons/fi';
import { FcGoogle } from 'react-icons/fc';
import { useLocale, createLocaleUrl } from '@/hooks/useLocale';
import { useTranslations } from '@/hooks/useTranslations';

function LoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const locale = useLocale();
  const t = useTranslations('auth.login');
  const callbackUrl = searchParams?.get('callbackUrl') || createLocaleUrl('/dashboard', locale);

  const [isLoading, setIsLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Check if user is already logged in
  useEffect(() => {
    async function checkAuth() {
      const session = await getSession();
      if (session) {
        router.push(callbackUrl);
      }
    }
    checkAuth();
  }, [callbackUrl, router]);

  // Handle form submission for email/password login
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      console.log("Attempting NextAuth login with callbackUrl:", callbackUrl);

      // First, try with NextAuth
      const result = await signIn('credentials', {
        redirect: false,
        email,
        password,
        callbackUrl
      });

      console.log("NextAuth login result:", result);

      if (result?.error) {
        console.log("NextAuth login failed, trying custom JWT login");

        // If NextAuth fails, try the custom JWT endpoint
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Login failed');
        }

        // Navigate to dashboard (or the callback URL)
        router.push(callbackUrl);
      } else {
        // NextAuth login successful, navigate to callback URL
        console.log("NextAuth login successful, redirecting to:", callbackUrl);
        router.push(callbackUrl);
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error instanceof Error ? error.message : 'Failed to login');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Google sign in
  const handleGoogleSignIn = async () => {
    setGoogleLoading(true);
    try {
      await signIn('google', { callbackUrl });
    } catch (error) {
      console.error('Google login error:', error);
      setError('Failed to login with Google');
    } finally {
      setGoogleLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-black px-4">
      {/* Background gradient effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#141493]/20 via-black to-[#417ef7]/20"></div>
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#141493]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#417ef7]/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-md w-full space-y-8 glass-effect p-10 shadow-2xl">
        <div className="text-center">
          <Link href={createLocaleUrl('/', locale)} className="inline-block mb-6">
            <div className="flex items-center justify-center">
              <img
                src="/Logo Web.webp"
                alt="AstroStudio AI"
                className="h-12 w-auto object-contain"
              />
            </div>
          </Link>
          <h1 className="text-3xl font-extrabold text-white">{t('title')}</h1>
          <p className="mt-2 text-sm text-gray-400">
            {t('subtitle')}
          </p>
        </div>

        {error && (
          <div className="bg-red-900/30 border-l-4 border-red-500 p-4 rounded-md">
            <div className="flex items-center">
              <FiAlertCircle className="h-5 w-5 text-red-500" />
              <div className="ml-3">
                <p className="text-sm text-red-400">{error}</p>
              </div>
            </div>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md -space-y-px">
            <div className="mb-5">
              <label htmlFor="email" className="sr-only">{t('email')}</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMail className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                  placeholder={t('email')}
                />
              </div>
            </div>

            <div className="mb-2">
              <label htmlFor="password" className="sr-only">{t('password')}</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                  placeholder={t('password')}
                />
              </div>
            </div>
          </div>

          <div className="text-right">
            <Link href={createLocaleUrl('/forgot-password', locale)} className="text-sm font-medium text-[#417ef7] hover:text-[#5a8df8] transition duration-150">
              {t('forgotPassword')}
            </Link>
          </div>

          <div className="flex flex-col space-y-4">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary group relative w-full flex justify-center py-3 px-4 text-sm font-medium rounded-md text-white bg-gradient-to-r from-[#141493] to-[#417ef7] hover:from-[#1a1a9e] hover:to-[#5a8df8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#417ef7] transition duration-150 disabled:opacity-70"
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                <FiLogIn className="h-5 w-5 text-blue-200 group-hover:text-white" />
              </span>
              {isLoading ? t('signingIn') : t('signInButton')}
            </button>

            <div className="relative my-2">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-700"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-gray-900 text-gray-400">Or continue with</span>
              </div>
            </div>

            <button
              type="button"
              onClick={handleGoogleSignIn}
              disabled={googleLoading}
              className="btn-secondary group relative w-full flex justify-center py-3 px-4 text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#417ef7] transition duration-150"
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                <FcGoogle className="h-5 w-5" />
              </span>
              {googleLoading ? 'Connecting...' : t('signInWithGoogle')}
            </button>
          </div>
        </form>

        <div className="text-center pt-4 border-t border-gray-800">
          <p className="text-sm text-gray-400">
            {t('noAccount')}{' '}
            <Link href={createLocaleUrl('/signup', locale)} className="font-medium text-[#417ef7] hover:text-[#5a8df8] transition duration-150">
              {t('signUpInstead')} <FiArrowRight className="inline h-3 w-3" />
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function Login() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center bg-black">
      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#417ef7]"></div>
    </div>}>
      <LoginForm />
    </Suspense>
  );
}