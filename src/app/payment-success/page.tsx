"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON>, FiLoader } from 'react-icons/fi';

export default function PaymentSuccessPage() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);
  const [subscriptionType, setSubscriptionType] = useState<string | null>(null);
  
  useEffect(() => {
    // Check if the user has a successful payment and get redirect URL
    const checkPaymentSuccess = async () => {
      try {
        const response = await fetch('/api/check-payment-success');
        if (response.ok) {
          const data = await response.json();
          
          if (data.success && data.redirectUrl) {
            setSubscriptionType(data.subscriptionType || 'standard');
            
            // Start countdown and redirect
            const timer = setInterval(() => {
              setCountdown((prev) => {
                if (prev <= 1) {
                  clearInterval(timer);
                  router.push(data.redirectUrl);
                  return 0;
                }
                return prev - 1;
              });
            }, 1000);
            
            return () => clearInterval(timer);
          } else {
            // No successful payment found, redirect to subscription page
            router.push('/dashboard/subscription');
          }
        } else {
          // Error checking payment, redirect to subscription page
          router.push('/dashboard/subscription');
        }
      } catch (error) {
        console.error('Error checking payment status:', error);
        router.push('/dashboard/subscription');
      }
    };
    
    checkPaymentSuccess();
  }, [router]);
  
  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center">
      <div className="max-w-md w-full p-6 bg-gray-900 rounded-xl border border-purple-800 shadow-lg shadow-purple-900/20">
        <div className="text-center mb-6">
          <div className="h-20 w-20 bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <FiCheck className="h-10 w-10 text-purple-500" />
          </div>
          
          <h1 className="text-2xl font-bold text-white mb-2">Payment Successful!</h1>
          
          <p className="text-gray-400">
            Thank you for your subscription to the {' '}
            <span className="text-purple-400 font-medium">
              {subscriptionType === 'premium' ? 'Premium' : 'Standard'} Plan
            </span>
          </p>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 mb-6">
          <p className="text-white text-center">
            You now have access to:
          </p>
          <ul className="mt-3 space-y-2">
            <li className="flex items-start">
              <FiCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-300">
                {subscriptionType === 'premium' 
                  ? 'All AI models and features including Video Generation' 
                  : 'All standard AI models and features'}
              </span>
            </li>
            <li className="flex items-start">
              <FiCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-300">
                {subscriptionType === 'premium' 
                  ? 'Higher daily usage limits for all tools' 
                  : 'Increased daily usage limits'}
              </span>
            </li>
            <li className="flex items-start">
              <FiCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-300">
                {subscriptionType === 'premium' 
                  ? 'Priority processing and premium support' 
                  : 'Standard support and full access to community features'}
              </span>
            </li>
          </ul>
        </div>
        
        <div className="text-center text-white">
          <p className="mb-2">Redirecting to dashboard in <span className="font-bold">{countdown}</span> seconds</p>
          <div className="flex justify-center">
            <FiLoader className="animate-spin text-purple-500 h-5 w-5" />
          </div>
          <button 
            onClick={() => router.push('/dashboard')}
            className="mt-4 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
          >
            Go to Dashboard Now
          </button>
        </div>
      </div>
    </div>
  );
} 