'use client';

import { useState, useEffect } from 'react';
import { createGuestCheckoutSession } from '@/lib/actions/subscription-actions';
import { STRIPE_PRICE_IDS, fetchPriceIDs } from '@/lib/stripe-client';

interface GuestCheckoutButtonProps {
  planType: 'Standard' | 'Premium';
  variant?: 'default' | 'outline' | 'ghost' | 'link';
  className?: string;
}

export function GuestCheckoutButton({ 
  planType, 
  variant = 'default',
  className 
}: GuestCheckoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [pricesLoaded, setPricesLoaded] = useState(false);
  
  // Fetch price IDs when component mounts
  useEffect(() => {
    async function loadPriceIDs() {
      if (STRIPE_PRICE_IDS.STANDARD || STRIPE_PRICE_IDS.PREMIUM) {
        // Price IDs already loaded
        setPricesLoaded(true);
        return;
      }
      
      try {
        const success = await fetchPriceIDs();
        setPricesLoaded(success);
      } catch (error) {
        console.error('Error loading price IDs:', error);
        setPricesLoaded(false);
      }
    }
    
    loadPriceIDs();
  }, []);
  
  const handleDirectCheckout = async () => {
    try {
      setIsLoading(true);
      
      // Make sure price IDs are loaded
      if (!pricesLoaded) {
        await fetchPriceIDs();
      }
      
      // Get the price ID for the plan type
      const priceId = 
        planType === 'Standard' 
          ? STRIPE_PRICE_IDS.STANDARD 
          : STRIPE_PRICE_IDS.PREMIUM;
      
      if (!priceId) {
        throw new Error('Price ID not available for ' + planType + ' plan');
      }
      
      console.log(`Creating guest checkout for ${planType} with price ID ${priceId}`);
      
      // Call createGuestCheckoutSession with price ID and email
      const result = await createGuestCheckoutSession(
        planType,   // Plan type
        undefined,  // Email (will be collected by Stripe)
        priceId     // Custom price ID
      );
      
      if (result?.url) {
        window.location.href = result.url;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Error starting checkout:', error);
      setIsLoading(false);
    }
  };
  
  const btnClass = variant === 'link' 
    ? className || 'text-gray-400 hover:text-white' 
    : `py-2 px-4 rounded-md font-medium text-sm transition-colors ${
        variant === 'outline' 
          ? 'bg-transparent border border-gray-700 text-gray-300 hover:bg-gray-800' 
          : 'bg-gradient-to-r from-purple-600 to-cyan-400 text-white hover:from-purple-700 hover:to-cyan-500'
      } ${className || ''}`;
      
  return (
    <button
      className={btnClass}
      onClick={handleDirectCheckout}
      disabled={isLoading}
    >
      {isLoading ? 'Processing...' : 'Pay without Registration'}
    </button>
  );
} 