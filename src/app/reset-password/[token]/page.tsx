"use client";

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { FiLock, FiAlertCircle, FiCheckCircle, FiArrowLeft } from 'react-icons/fi';
import { useLocale, createLocaleUrl } from '@/hooks/useLocale';
import { useTranslations } from '@/hooks/useTranslations';
import { z } from 'zod';

// Validation schema
const resetPasswordSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

function ResetPasswordForm() {
  const router = useRouter();
  const params = useParams();
  const locale = useLocale();
  const t = useTranslations('auth.resetPassword');
  const token = params?.token as string;
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [isValidToken, setIsValidToken] = useState(false);
  const [isTokenChecking, setIsTokenChecking] = useState(true);

  // Check if the token is valid
  useEffect(() => {
    async function validateToken() {
      if (!token) {
        setError(t('errors.tokenInvalid'));
        setIsTokenChecking(false);
        return;
      }

      try {
        const response = await fetch(`/api/auth/validate-reset-token?token=${token}`);
        const data = await response.json();
        
        if (!response.ok || !data.valid) {
          if (data.error === 'TOKEN_EXPIRED') {
            setError(t('errors.tokenExpired'));
          } else {
            setError(t('errors.tokenInvalid'));
          }
          return;
        }
        
        setIsValidToken(true);
      } catch (err) {
        setError(t('errors.tokenInvalid'));
      } finally {
        setIsTokenChecking(false);
      }
    }
    
    validateToken();
  }, [token, t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    // Validate form data
    try {
      resetPasswordSchema.parse({ password, confirmPassword });
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        const firstError = validationError.errors[0];
        if (firstError.path[0] === 'confirmPassword') {
          setError(t('errors.passwordMismatch'));
        } else {
          setError(t('errors.passwordTooShort'));
        }
        return;
      }
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.error === 'TOKEN_EXPIRED') {
          setError(t('errors.tokenExpired'));
        } else if (data.error === 'TOKEN_INVALID') {
          setError(t('errors.tokenInvalid'));
        } else {
          setError(t('errors.resetFailed'));
        }
        return;
      }
      
      setSuccess(true);
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push(createLocaleUrl('/login', locale));
      }, 3000);
    } catch (err) {
      setError(t('errors.resetFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  if (isTokenChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black relative overflow-hidden px-4">
        {/* Background effects with blue gradient theme */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#141493]/20 via-black to-[#417ef7]/20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-[#141493]/10 to-[#417ef7]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-[#417ef7]/10 to-[#141493]/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-md w-full space-y-8 glass-effect p-10 shadow-2xl">
          <div className="text-center">
            <Link href={createLocaleUrl('/', locale)} className="inline-block mb-6">
              <Image
                src="/Logo Web.webp"
                alt="AstroStudio AI"
                width={200}
                height={70}
                className="mb-4"
              />
            </Link>
            <h1 className="text-3xl font-extrabold text-white">{t('title')}</h1>
            <p className="mt-2 text-sm text-gray-400">
              {t('validating')}
            </p>
          </div>
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#417ef7]"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-black relative overflow-hidden px-4">
      {/* Background effects with blue gradient theme */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#141493]/20 via-black to-[#417ef7]/20"></div>
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-[#141493]/10 to-[#417ef7]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-[#417ef7]/10 to-[#141493]/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-md w-full space-y-8 glass-effect p-10 shadow-2xl">
        <div className="text-center">
          <Link href={createLocaleUrl('/', locale)} className="inline-block mb-6">
            <Image
              src="/Logo Web.webp"
              alt="AstroStudio AI"
              width={200}
              height={70}
              className="mb-4"
            />
          </Link>
          <h1 className="text-3xl font-extrabold text-white">{t('title')}</h1>
          <p className="mt-2 text-sm text-gray-400">
            {t('subtitle')}
          </p>
        </div>
        
        {!isValidToken && !success ? (
          <div className="mt-6">
            <div className="bg-red-900/20 border border-red-500/30 p-4 rounded-md mb-6">
              <div className="flex items-center">
                <FiAlertCircle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-300">
                    {error || t('errors.tokenInvalid')}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="text-center">
              <Link href={createLocaleUrl('/forgot-password', locale)} className="inline-flex items-center text-sm font-medium text-[#417ef7] hover:text-[#2563eb] transition duration-150">
                <FiArrowLeft className="mr-2 h-4 w-4" />
                Request a new reset link
              </Link>
            </div>
          </div>
        ) : success ? (
          <div className="mt-6">
            <div className="bg-green-900/20 border border-green-500/30 p-4 rounded-md mb-6">
              <div className="flex items-center">
                <FiCheckCircle className="h-5 w-5 text-green-400" />
                <div className="ml-3">
                  <p className="text-sm text-green-300">
                    {t('success')}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="text-center">
              <Link href={createLocaleUrl('/login', locale)} className="inline-flex items-center text-sm font-medium text-[#417ef7] hover:text-[#2563eb] transition duration-150">
                <FiArrowLeft className="mr-2 h-4 w-4" />
                {t('backToLogin')}
              </Link>
            </div>
          </div>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-900/20 border border-red-500/30 p-4 rounded-md">
                <div className="flex items-center">
                  <FiAlertCircle className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <p className="text-sm text-red-300">{error}</p>
                  </div>
                </div>
              </div>
            )}
            
            <div className="space-y-5">
              <div>
                <label htmlFor="password" className="sr-only">{t('newPassword')}</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiLock className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                    placeholder={t('newPassword')}
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="confirmPassword" className="sr-only">{t('confirmPassword')}</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiLock className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                    placeholder={t('confirmPassword')}
                  />
                </div>
              </div>
            </div>
            
            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-[#141493] to-[#417ef7] hover:from-[#0f0f7a] hover:to-[#2563eb] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#417ef7] transition duration-150 disabled:opacity-70"
              >
                {isLoading ? t('resetting') : t('resetButton')}
              </button>
            </div>
            
            <div className="text-center pt-4">
              <Link href={createLocaleUrl('/login', locale)} className="inline-flex items-center text-sm font-medium text-[#417ef7] hover:text-[#2563eb] transition duration-150">
                <FiArrowLeft className="mr-2 h-4 w-4" />
                {t('backToLogin')}
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center bg-black">
      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#417ef7]"></div>
    </div>}>
      <ResetPasswordForm />
    </Suspense>
  );
}
