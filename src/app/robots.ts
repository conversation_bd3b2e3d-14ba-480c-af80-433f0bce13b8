import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/_next/',
          '/_vercel/',
          '/dashboard/',
          '/admin/',
          '/.env*',
          '/config/'
        ]
      },
      {
        userAgent: ['AhrefsBot', 'SemrushBot', 'MJ12bot'],
        crawlDelay: 10
      }
    ],
    sitemap: [
      'https://astrostudioai.com/sitemap.xml',
      'https://www.astrostudioai.com/sitemap.xml'
    ],
    host: 'https://astrostudioai.com'
  }
}
