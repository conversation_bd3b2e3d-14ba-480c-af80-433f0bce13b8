"use client";

import { useState, Suspense } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { FiMail, FiLock, FiUser, FiUserPlus, FiArrowLeft, FiAlertCircle, FiCheckCircle } from 'react-icons/fi';
import { FcGoogle } from 'react-icons/fc';
import { useLocale, createLocaleUrl } from '@/hooks/useLocale';
import { useTranslations } from '@/hooks/useTranslations';

function SignupForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const locale = useLocale();
  const t = useTranslations('auth.signup');
  const plan = searchParams?.get('plan') || '';
  
  const [isLoading, setIsLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    // Validate form
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          plan: plan || 'free'
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed');
      }

      // Show success message
      setSuccess(true);
      
      // Redirect to login after 2 seconds
      setTimeout(() => {
        router.push(plan ? createLocaleUrl(`/login?plan=${plan}`, locale) : createLocaleUrl('/login', locale));
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setGoogleLoading(true);
    try {
      const callbackUrl = plan ? createLocaleUrl(`/dashboard?plan=${plan}`, locale) : createLocaleUrl('/dashboard', locale);
      await signIn('google', { callbackUrl });
    } catch (error) {
      console.error('Google signup error:', error);
      setError('Failed to sign up with Google');
    } finally {
      setGoogleLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-black px-4">
      {/* Background gradient effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#141493]/20 via-black to-[#417ef7]/20"></div>
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#141493]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#417ef7]/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-md w-full space-y-8 glass-effect p-10 shadow-2xl">
        <div className="text-center">
          <Link href={createLocaleUrl('/', locale)} className="inline-block mb-6">
            <div className="flex items-center justify-center">
              <img
                src="/Logo Web.webp"
                alt="AstroStudio AI"
                className="h-12 w-auto object-contain"
              />
            </div>
          </Link>
          <h1 className="text-3xl font-extrabold text-white">{t('title')}</h1>
          <p className="mt-2 text-sm text-gray-400">
            {t('hasAccount')}{' '}
            <Link href={createLocaleUrl('/login', locale)} className="font-medium text-[#417ef7] hover:text-[#5a8df8] transition duration-150">
              {t('signInInstead')} <FiArrowLeft className="inline h-3 w-3" />
            </Link>
          </p>
        </div>
        
        {success ? (
          <div className="bg-green-900/30 border-l-4 border-green-500 p-4 rounded-md">
            <div className="flex items-center">
              <FiCheckCircle className="h-5 w-5 text-green-500" />
              <div className="ml-3">
                <p className="text-sm text-green-400">{t('success')}</p>
              </div>
            </div>
          </div>
        ) : (
          <>
            {error && (
              <div className="bg-red-900/30 border-l-4 border-red-500 p-4 rounded-md">
                <div className="flex items-center">
                  <FiAlertCircle className="h-5 w-5 text-red-500" />
                  <div className="ml-3">
                    <p className="text-sm text-red-400">{error}</p>
                  </div>
                </div>
              </div>
            )}
            
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <div className="rounded-md space-y-5">
                <div>
                  <label htmlFor="name" className="sr-only">{t('name')}</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiUser className="h-5 w-5 text-gray-500" />
                    </div>
                    <input
                      id="name"
                      name="name"
                      type="text"
                      autoComplete="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                      placeholder={t('name')}
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="email" className="sr-only">{t('email')}</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiMail className="h-5 w-5 text-gray-500" />
                    </div>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                      placeholder={t('email')}
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="password" className="sr-only">{t('password')}</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiLock className="h-5 w-5 text-gray-500" />
                    </div>
                    <input
                      id="password"
                      name="password"
                      type="password"
                      autoComplete="new-password"
                      required
                      value={formData.password}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                      placeholder={t('password')}
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="confirmPassword" className="sr-only">{t('confirmPassword')}</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiLock className="h-5 w-5 text-gray-500" />
                    </div>
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      autoComplete="new-password"
                      required
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-3 border border-gray-800 bg-gray-800/50 rounded-md text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition duration-150"
                      placeholder={t('confirmPassword')}
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn-primary group relative w-full flex justify-center py-3 px-4 border border-transparent rounded-md text-white bg-gradient-to-r from-[#141493] to-[#417ef7] hover:from-[#1a1a9e] hover:to-[#5a8df8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#417ef7] font-medium text-base transition duration-150 disabled:opacity-50"
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('processing')}
                    </span>
                  ) : (
                    <>
                      <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                        <FiUserPlus className="h-5 w-5 text-blue-200 group-hover:text-white" />
                      </span>
                      {t('signUpButton')}
                    </>
                  )}
                </button>
              </div>
              
              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-700"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-gray-900 text-gray-400">Or continue with</span>
                  </div>
                </div>
                
                <div className="mt-6">
                  <button
                    type="button"
                    onClick={handleGoogleSignUp}
                    disabled={googleLoading}
                    className="btn-secondary w-full flex justify-center items-center py-3 px-4 border border-gray-700 rounded-md shadow-sm bg-gray-800 text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#417ef7] transition duration-150 disabled:opacity-50"
                  >
                    {googleLoading ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t('processing')}
                      </span>
                    ) : (
                      <>
                        <FcGoogle className="h-5 w-5 mr-2" />
                        {t('signUpWithGoogle')}
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
}

export default function SignupPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center bg-black">
      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#417ef7]"></div>
    </div>}>
      <SignupForm />
    </Suspense>
  );
}