import { MetadataRoute } from 'next'

const baseUrl = 'https://astrostudioai.com'

// Define all available routes
const routes = [
  '',
  '/login',
  '/signup',
  '/terms',
  '/privacy',
  '/dashboard',
  '/dashboard/image-generation',
  '/dashboard/image-editing',
  '/dashboard/video-generation',
  '/dashboard/music-generation',
  '/dashboard/podcast-generation',
  '/dashboard/speech-to-text',
  '/dashboard/assistant',
  '/dashboard/subscription',
  '/dashboard/library'
]

// Define route priorities and change frequencies
const routeConfig: Record<string, { priority: number; changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never' }> = {
  '': { priority: 1.0, changefreq: 'daily' },
  '/login': { priority: 0.6, changefreq: 'monthly' },
  '/signup': { priority: 0.6, changefreq: 'monthly' },
  '/terms': { priority: 0.4, changefreq: 'yearly' },
  '/privacy': { priority: 0.4, changefreq: 'yearly' },
  '/dashboard': { priority: 0.8, changefreq: 'daily' },
  '/dashboard/image-generation': { priority: 0.7, changefreq: 'weekly' },
  '/dashboard/image-editing': { priority: 0.7, changefreq: 'weekly' },
  '/dashboard/video-generation': { priority: 0.7, changefreq: 'weekly' },
  '/dashboard/music-generation': { priority: 0.7, changefreq: 'weekly' },
  '/dashboard/podcast-generation': { priority: 0.7, changefreq: 'weekly' },
  '/dashboard/speech-to-text': { priority: 0.7, changefreq: 'weekly' },
  '/dashboard/assistant': { priority: 0.7, changefreq: 'weekly' },
  '/dashboard/subscription': { priority: 0.6, changefreq: 'monthly' },
  '/dashboard/library': { priority: 0.6, changefreq: 'weekly' }
}

export default function sitemap(): MetadataRoute.Sitemap {
  const currentDate = new Date().toISOString()
  
  const sitemapEntries: MetadataRoute.Sitemap = []

  // Generate entries for each route in both languages
  routes.forEach(route => {
    const config = routeConfig[route] || { priority: 0.5, changefreq: 'weekly' as const }
    
    // English version
    sitemapEntries.push({
      url: `${baseUrl}${route}`,
      lastModified: currentDate,
      changeFrequency: config.changefreq,
      priority: config.priority,
      alternates: {
        languages: {
          en: `${baseUrl}${route}`,
          es: `${baseUrl}/es${route}`
        }
      }
    })

    // Spanish version
    sitemapEntries.push({
      url: `${baseUrl}/es${route}`,
      lastModified: currentDate,
      changeFrequency: config.changefreq,
      priority: config.priority,
      alternates: {
        languages: {
          en: `${baseUrl}${route}`,
          es: `${baseUrl}/es${route}`
        }
      }
    })
  })

  return sitemapEntries
}
