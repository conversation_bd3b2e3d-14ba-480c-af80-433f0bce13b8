"use client";

import React, { useState, useEffect, useRef } from 'react';
import { FiMessageSquare, FiSend, FiX, FiChevronDown } from 'react-icons/fi';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';

type Message = {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
};

export default function Chatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Add welcome message when chat is first opened
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      setMessages([
        {
          id: '1',
          text: "Hello! 👋 I'm the AstroStudio AI assistant. How can I help you with our AI creation tools today?",
          isUser: false,
          timestamp: new Date()
        }
      ]);
    }
  }, [isOpen, messages.length]);

  // Auto-scroll to the bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus the input field when chat is opened
  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus();
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputMessage,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    try {
      // Prepare conversation history for the API
      const conversationHistory = messages.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.text
      }));

      // Add the current message
      conversationHistory.push({
        role: 'user',
        content: inputMessage
      });

      // Call your AI assistant API here
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          message: inputMessage,
          model: 'together-ai/meta-llama/Llama-3.3-70B-Instruct-Turbo-Free',
          provider: 'together-ai',
          messages: conversationHistory,
          temperature: 0.7,
          max_tokens: 1000
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || "Failed to get response from AI");
      }
      
      // Small delay to simulate typing
      setTimeout(() => {
        const botMessage: Message = {
          id: Date.now().toString(),
          text: data.message || "I'm sorry, I couldn't process your request. Please try again.",
          isUser: false,
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, botMessage]);
        setIsTyping(false);
      }, 1000);
    } catch (error) {
      console.error('Error sending message to AI:', error);
      
      setTimeout(() => {
        const errorMessage: Message = {
          id: Date.now().toString(),
          text: "I'm sorry, there was an error processing your request. Please try again later.",
          isUser: false,
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, errorMessage]);
        setIsTyping(false);
      }, 1000);
    }
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="fixed bottom-5 right-5 z-50">
      {/* Chat window */}
      {isOpen && (
        <div className="w-80 sm:w-96 h-96 bg-gray-900 rounded-lg shadow-xl border border-purple-900/30 flex flex-col mb-4 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-cyan-400 p-4 flex justify-between items-center">
            <h3 className="text-white font-medium flex items-center">
            <span className="w-10 h-10">
                                <svg viewBox="0 0 520 424" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M48.1572 393.154C47.2258 393.01 46.3608 392.584 45.6792 391.933C44.9975 391.282 44.5321 390.438 44.3457 389.514C43.3473 384.124 54.1788 360.682 54.1788 360.682C54.1788 360.682 48.3835 354.606 51.4203 343.189C52.6657 338.509 62.0413 318.606 68.5309 311.852C72.8833 307.32 80.7508 302.913 87.2508 304.455C87.5108 304.514 85.6544 306.212 98.6622 290.841C112.962 273.941 148.015 245.185 177.588 231.712C208.621 217.573 260.941 205.972 307.328 214.201C369.156 225.186 409.31 260.907 435.097 288.337C445.414 288.194 454.298 296.335 461.097 304.091C471.825 316.311 481.621 330.72 482.297 346.967C482.778 351.648 481.58 356.348 478.917 360.227C481.998 367.647 484.625 375.247 486.782 382.985C486.982 383.758 487.025 384.564 486.906 385.354C486.788 386.144 486.511 386.902 486.093 387.583C485.676 388.264 485.125 388.853 484.474 389.316C483.823 389.779 483.085 390.106 482.305 390.278C449.069 397.644 276.988 431.241 48.1572 393.154Z" fill="white" />
                                    <path d="M461.149 320.12C459.875 321.126 477.776 345.407 467.691 346.442C467.542 343.045 466.655 339.721 465.091 336.702C462.202 330.694 449.751 314.582 446.776 311.29C444.148 308.089 440.575 305.801 436.569 304.754C438.3 294.978 451.909 309.379 452.982 307.874C453.44 307.19 443.971 296.114 437.559 298.464C433.919 299.725 431.579 304.624 433.589 310.273C436.8 319.345 457.488 344.427 458.809 345.735C462.22 348.935 465.852 351.138 470.22 349.57C482.849 343.101 462.155 319.311 461.149 320.12ZM452.569 332.181C448.669 327.197 437.055 313.045 436.472 306.727C443.076 308.906 447.252 316.43 454.03 325.346C458.71 331.692 464.544 338.439 465.543 346.099C460.912 344.378 456.214 336.64 452.582 332.181H452.569Z" fill="black" />
                                    <path d="M400.431 364.177C399.061 339.477 359.034 326.37 358.535 330.151C358.22 331.469 372.861 334.472 386.048 347.656C398.627 360.412 395.808 371.797 389.948 373.776C391.297 366.387 386.604 359.476 380.786 354.775C372.653 348.174 361.689 345.569 351.481 343.075C337.308 339.739 315.897 335.975 307.128 347.916C301.561 324.75 346.458 331.224 346.832 329.612C347.745 326.602 303.781 319.413 301.213 342.202C300.793 345.966 301.496 349.77 303.232 353.136C304.968 356.503 307.66 359.281 310.97 361.122C319.428 366.322 329.584 365.084 338.918 366.28C362.913 369.122 371.314 378.997 385.086 379.85C395.385 380.346 400.793 373.945 400.431 364.177ZM353.475 363.47C331.248 357.406 316.839 363.285 308.911 351.824C314.311 340.054 338.517 344.742 350.41 347.571C362.968 350.584 380.689 354.697 385.817 367.253C386.791 369.471 387.049 371.939 386.555 374.312C377.187 374.478 368.919 367.679 353.475 363.47V363.47Z" fill="black" />
                                    <path d="M292.674 271.201C286.731 260.494 268.627 261.991 268.978 263.785C269.04 264.487 272.509 264.391 277.524 265.92C283.296 267.74 290.732 271.814 289.172 277.698C288.062 282.227 288.626 278.527 278.886 274.534C265.301 268.884 244.026 270.993 237.695 280.561C234.736 274.924 239.156 270.161 244.813 267.355C249.441 265.228 256.773 262.797 250.294 263.102C227.287 264.451 227.848 287.04 243.451 290.019C249.314 291.14 255.484 289.377 264.945 289.38C271.866 289.26 281.845 291.93 288.649 286.286C294.112 281.902 295.225 275.644 292.674 271.201ZM264.94 284.749C251.995 284.749 244.66 287.947 239.322 282.729C244.509 275.249 268.182 270.262 286.533 281.991C281.125 287.154 273.237 284.695 264.94 284.749Z" fill="black" />
                                    <path d="M70.993 333.551C65.6084 343.522 61.8618 351.993 60.7308 351.634C59.6908 351.299 61.7708 341.928 67.3478 331.58C72.9248 321.232 79.6718 314.376 80.5272 315.078C81.4398 315.84 76.3802 323.58 70.993 333.551Z" fill="black" />
                                    <path d="M201.76 339.24C189.069 323.016 159.146 338.582 160.521 340.74C161.218 341.981 179.954 333.811 191.864 338.278C198.656 341.422 198.825 344.877 195.939 348.678C195.125 349.752 192.249 346.713 181.639 346.835C163.056 346.986 143.159 357.596 133.682 372.549C128.801 370.334 128.029 366.785 130.25 361.889C134.716 352.04 147.36 345.27 146.741 344.024C145.821 341.856 121.576 353.842 123.861 368.865C125.715 377.551 135.221 380.227 144.877 378.838C155.004 377.286 213.743 358.067 201.76 339.24ZM137.004 373.48C149.687 358.088 173.326 347.942 192.988 351.699C183.3 360.136 149.126 375.224 137.004 373.48Z" fill="black" />
                                    <path d="M357.053 303.576C352.989 299.364 353.909 295.181 357.453 294.408C362.595 294.154 367.679 295.583 371.935 298.48C376.355 301.34 385.364 307.889 370.677 300.739C362.331 296.676 357.144 298.007 357.976 297.973C357.976 297.973 357.958 297.937 357.994 298.142C357.833 302.775 383.276 315.702 379.67 307.005C378.001 303.508 379.582 304.743 380.869 306.241C383.747 309.585 380.656 318.253 362.528 307.424C360.524 306.417 358.679 305.121 357.053 303.576V303.576Z" fill="black" />
                                    <path d="M163.418 293.579C156.84 294.081 145.553 298.368 149.157 303.841C152.298 308.464 161.158 306.92 166.689 305.685C182.226 301.964 181.701 292.456 163.418 293.579ZM174.426 299.645C168.805 303.584 144.381 305.341 155.22 299.242C165.896 293.233 180.7 295.243 174.426 299.645Z" fill="black" />
                                    <path d="M235.152 321.212C225.066 330.335 217.012 325.632 218.379 319.982C218.899 317.808 222.053 314.446 220.514 318.422C220.24 318.901 220.084 319.437 220.059 319.988C220.034 320.539 220.14 321.088 220.368 321.59C220.597 322.092 220.941 322.532 221.374 322.874C221.806 323.217 222.313 323.452 222.854 323.559C226.309 324.373 233.87 319.217 234.346 316.786C234.66 314.319 234.679 312.078 227.435 314.428C214.422 318.648 225.875 312.244 230.035 311.074C233.826 310.294 236.959 310.786 237.515 314.628C237.728 315.837 237.622 317.079 237.208 318.234C236.793 319.388 236.085 320.415 235.152 321.212V321.212Z" fill="black" />
                                    <path d="M92.716 372.988C88.4104 369.546 93.7872 363.142 99.5228 360.768C104.057 358.896 105.651 360.882 105.316 361.085C103.597 361.207 101.942 361.79 100.526 362.773C94.133 366.756 92.4664 371.997 96.8656 370.495C99.5808 369.364 102.009 367.642 103.974 365.453C104.588 364.926 105.039 364.236 105.277 363.463C105.514 362.69 105.528 361.866 105.316 361.085C105.576 360.383 108.412 362.645 105.768 366.805C103.124 370.965 96.5042 376.02 92.716 372.988Z" fill="black" />
                                    <path d="M436.21 375.201C435.689 376.076 434.863 376.728 433.892 377.033C432.92 377.338 431.87 377.274 430.942 376.854C424.793 374.535 421.299 365.763 423.007 363.797C423.644 362.923 423.568 364.182 423.823 364.946C425.025 367.856 426.942 370.415 429.398 372.385C433.298 375.619 435.427 373.989 431.608 368.558C430.321 366.738 427.063 362.991 424.725 363.144C424.013 363.189 423.628 363.633 423.425 363.516C423.262 363.493 423.165 362.611 424.333 361.974C428.54 359.699 438.986 369.483 436.21 375.201Z" fill="black" />
                                    <path d="M287.599 355.88C285.059 348.907 285.626 344.227 285.979 344.25C286.967 344.133 288.839 365.354 308.415 373.11C316.979 376.508 326.134 376.516 335.301 378.248C342.454 379.6 346.494 381.823 346.304 382.202C346.104 382.691 341.949 381.36 334.948 380.661C321.201 379.293 296.72 380.897 287.599 355.88Z" fill="black" />
                                    <path d="M246.002 299.845C231.416 298.217 223.696 288.769 224.648 288.033C225.516 287.253 231.226 294.988 246.366 296.696C257.891 297.996 270.286 295.677 270.457 296.956C270.592 297.994 256.487 301.02 246.002 299.845Z" fill="black" />
                                    <path d="M209.877 360.448C199.956 372.049 179.696 381.121 178.984 379.444C178.516 378.339 196.869 369.504 206.804 357.846C213.166 350.378 213.808 343.678 210.501 336.349C208.421 331.718 205.171 327.558 205.699 327.111C206.393 326.518 225.49 342.194 209.877 360.448Z" fill="black" />
                                    <path d="M362.016 316.792C353.639 312.608 348.598 305.737 349.333 305.092C350.069 304.447 355.313 309.878 363.433 313.955C371.553 318.032 379.054 318.812 379.012 319.751C378.966 320.845 370.412 320.986 362.016 316.792Z" fill="black" />
                                    <path d="M486.143 372C481.042 356.959 481.286 361.878 483.2 357.729C492.164 337.3 463.762 289.013 435.1 285.342C342.93 175.274 174.593 185.762 87.3262 301.704C69.3524 297.12 48.464 332.904 47.8374 349.164C47.7895 353.411 49.0524 357.569 51.454 361.072C49.6444 365.232 47.9327 369.435 46.319 373.68C43.2562 381.74 42.4788 388.822 43.9556 389.238C46.5192 389.969 52.039 373.786 55.9676 364.637C67.3166 369.47 81.9286 350.815 79.7498 349.359C78.6916 348.652 64.7088 363.233 58.318 359.718C45.318 353.454 62.5638 321.843 71.3934 313.474C76.3334 308.677 85.7818 304.358 88.5534 308.599C92.9266 313.482 88.3792 332.935 89.6974 333.447C91.1222 334.043 95.1236 323.986 95.3472 315.554C159.143 230.921 268.629 203.101 360.851 245.697C379.179 254.121 383.536 257.657 383.789 257.241C383.921 257.036 375.339 251.737 375.128 251.615C285.074 198.892 165.464 219.978 94.9416 310.084C94.5114 307.539 93.1841 305.234 91.2002 303.584C177.068 196.898 334.623 180.942 429.385 285.311C427.418 285.51 425.512 286.101 423.778 287.05C422.044 287.999 420.519 289.286 419.292 290.836C409.25 303.994 429.829 330.369 431.327 329.295C432.83 328.284 416.081 303.329 423.602 294.159C426.403 290.589 431.509 290.259 435.95 291.106C441.675 292.438 447.011 295.087 451.532 298.844C464.867 309.454 477.355 327.917 479.279 347.563C480.207 357.016 474.573 361.753 467.477 361.259C459.828 360.687 453.58 353.927 448.084 348.72C439.033 339.999 428.74 331.596 445.206 351.395C451.277 358.493 457.642 365.75 466.968 366.68C470.626 367.094 474.318 366.27 477.454 364.34C478.653 367.46 479.791 370.605 480.867 373.773C488.02 394.719 490.984 386.178 486.143 372Z" fill="black" />
                                    <path d="M145.08 230.04C149.64 225.573 157.266 215.612 156.208 209.362C155.038 202.464 133.929 181.82 112.297 178.526C102.378 165.744 77.2954 150.048 69.056 146.751C61.6928 143.808 51.5918 142.669 45.7314 146.533C34.7048 153.813 38.8414 178.789 38.9714 184.42C39.2938 199.968 51.5736 220.794 51.1368 225.227C50.804 228.625 46.5582 228.43 49.2544 240.991C50.9474 249.268 53.1177 257.441 55.7544 265.467C57.2052 269.744 59.228 274.359 63.349 276.205C69.355 278.889 81.315 275.561 87.4614 272.212C89.5778 271.06 90.6386 270.896 92.4014 273.033C95.8672 277.261 100.032 285.612 107.328 285.04C109.408 284.876 109.928 283.758 109.827 281.707C109.636 277.299 108.466 272.988 106.4 269.089C108.901 268.094 111.16 266.575 113.027 264.635C115.729 267.35 124.556 275.555 128.448 275.834C132.34 276.112 135.018 271.564 134.623 267.709C133.786 263.912 132.216 260.316 130 257.121C128.44 254.235 129.069 254.389 135.309 246.025C139.901 252.197 141.827 255.283 144.669 256.809C145.737 257.455 146.984 257.742 148.226 257.628C149.469 257.513 150.642 257.004 151.575 256.175C155.067 252.392 150.535 245.039 144.248 234.764C143.057 232.827 143.091 231.969 145.08 230.04Z" fill="#FFC5F6" />
                                    <path d="M67.652 205.709C67.1715 209.96 68.1411 214.248 70.4036 217.879C72.6661 221.51 76.0887 224.269 80.1164 225.711C82.8489 226.703 85.7985 226.94 88.6545 226.398C91.5106 225.855 94.1676 224.553 96.3459 222.627C98.5241 220.702 100.143 218.225 101.033 215.458C101.922 212.69 102.049 209.734 101.4 206.9C97.7756 189.813 71.175 186.901 67.652 205.709Z" fill="#92FCFF" />
                                    <path d="M96.5173 218.86C94.0145 221.934 90.4037 223.901 86.4638 224.336C82.5239 224.771 78.5707 223.64 75.4573 221.187C68.5153 216.172 68.8299 208.967 67.7665 209.027C67.0359 209.061 66.4665 217.711 73.4007 223.732C76.0132 226.095 79.2496 227.658 82.7249 228.234C86.2001 228.811 89.7678 228.376 93.0031 226.983C96.2385 225.589 99.0053 223.295 100.974 220.374C102.943 217.453 104.031 214.028 104.109 210.506C104.221 199.038 93.8913 189.035 82.3395 190.879C73.9857 192.096 68.0395 198.128 67.3921 203.528C66.9735 206.357 67.4597 207.865 67.7353 207.859C68.0915 207.872 68.2553 206.357 69.1315 203.993C71.6067 196.874 82.4175 192.145 90.4255 196.024C98.7637 199.961 102.547 211.32 96.5173 218.86Z" fill="black" />
                                    <path d="M89.0864 236.977C111.706 234.959 121.784 208.585 106.145 190.991C101.564 185.781 95.1968 182.475 88.2995 181.727C81.4022 180.978 74.4745 182.84 68.8818 186.945C67.1659 187.979 65.7191 189.405 64.6594 191.105C64.8362 191.29 66.3 189.766 69.4876 187.876C93.4414 173.62 117 197.28 109.426 218.441C103.839 234.546 82.2276 240.37 68.0549 227.136C59.0979 218.878 59.514 208.294 58.7418 208.356C58.2218 208.356 58.0787 220.038 66.6536 228.589C69.5682 231.52 73.0816 233.787 76.9533 235.235C80.825 236.682 84.9637 237.277 89.0864 236.977V236.977Z" fill="black" />
                                    <path d="M152.214 198.894C141.528 187.878 128.087 179.924 113.287 175.858C100.573 162.54 85.4796 151.718 68.7856 143.951C26.6656 127.158 28.1294 182.632 47.684 223.03C45.4591 239.668 48.1222 256.593 55.3488 271.744C56.0088 273.743 57.198 275.526 58.7903 276.904C60.3825 278.281 62.3182 279.202 64.3916 279.567C72.397 281.286 83.4106 277.794 90.7712 273.553C92.3312 275.501 97.3102 281.442 99.0184 283.134C106.857 290.908 113.87 283.878 111.023 275.854C107.903 267.014 105.412 272.37 114.101 267.274C118.622 272.227 123.934 279.307 130.203 277.347C131.784 276.803 133.17 275.805 134.187 274.478C135.205 273.151 135.808 271.553 135.923 269.885C136.37 265.353 133.897 260.525 131.355 255.816C130.135 253.549 130.736 256.206 135.356 248.796C140.585 255.151 142.956 258.247 146.7 259.038C148.286 259.309 149.916 258.945 151.236 258.025C152.556 257.104 153.46 255.701 153.754 254.118C154.775 249.896 152.139 244.99 149.594 240.598C143.588 230.302 143.335 237.798 153.205 224.263C163.22 209.934 158.774 205.67 152.214 198.894ZM41.08 182.873C45.0785 181.334 48.9292 179.435 52.585 177.2C56.3134 174.839 58.305 172.933 58.0268 172.52C57.6134 171.875 54.301 173.924 40.5834 179.163C38.8492 164.156 40.7732 145.652 55.8558 146.385C63.011 146.679 71.539 150.995 77.6958 154.648C60.6788 170.079 60.0782 170.44 60.3486 170.866C60.619 171.293 63.6454 169.982 68.0056 167C72.5191 163.805 76.8408 160.347 80.9484 156.644C114.413 177.821 133.024 207.311 135.902 223.464C131.742 226.605 121.789 234.806 117.806 238.412C108.506 232.908 100.422 240.988 101.319 249.194C96.603 251.394 86.1328 256.841 81.5594 259.487C64.922 247.161 46.384 217.893 41.08 182.873ZM61.7188 271.996C58.9576 268.008 51.7582 250.38 51.2876 229.923C59.4828 244.483 74.0142 263.387 90.4124 269.014C83.2 272.745 65.9178 278.059 61.7188 271.996ZM106.27 272.62C107.323 275.087 108.976 278.525 107.968 281.002C105.591 286.839 99.5748 278.387 93.0306 272.162C96.7694 269.742 94.1304 270.478 99.658 271.07C106.808 271.788 104.78 269.102 106.27 272.62V272.62ZM100.084 267.095C94.6545 266.351 89.4371 264.492 84.76 261.635C90.9111 259.274 96.759 256.189 102.18 252.444C103.784 256.308 107.312 259.838 111.587 264.516C108.106 266.529 104.092 267.429 100.084 267.095V267.095ZM131.469 269.357C131.061 272.914 128.229 274.63 124.137 271.159C120.568 267.855 117.209 264.332 114.078 260.611C109.21 255.333 100.732 248.422 107.757 241.685C108.759 240.73 110.028 240.102 111.395 239.885C112.762 239.668 114.163 239.873 115.411 240.471C120.866 243.529 132.101 263.762 131.469 269.357V269.357ZM120.398 240.362C126.246 236.724 131.628 232.387 136.425 227.445C136.803 231.751 136.326 236.089 135.021 240.21C133.716 244.331 131.61 248.153 128.822 251.456C126.435 247.456 123.61 243.735 120.398 240.362V240.362ZM149.289 247.738C151.718 253.487 149.373 255.933 147.3 255.572C144.804 255.192 142.766 252.317 136.674 246.472C137.887 244.158 138.837 241.716 139.508 239.192C140.65 238.152 141.864 237.01 143.122 235.778C145.487 239.598 147.549 243.596 149.289 247.738V247.738ZM140.66 232.419C142.015 215.285 129.449 195.145 118.747 182.098C137.504 190.759 153.587 206.359 153.65 210.332C153.002 217.536 145.413 226.834 140.66 232.419Z" fill="black" />
                                    <path d="M336.484 80.9068C344.094 72.1812 344.058 73.71 350.574 76.4582C374.343 86.4812 453.469 127.015 453.729 127.158C467.685 135.192 473.912 135.977 467.857 150.275C461.617 165.022 444.821 197.73 434.923 209.96C430.313 215.161 426.255 220.824 422.812 226.86C417.487 236.951 403.312 244.036 407.636 245.885C425.446 253.484 429.494 261.095 434.244 277.051C434.491 277.88 434.411 278.229 433.55 278.725C430.461 280.514 423.15 284.17 420.129 282.243C417.628 280.644 416.915 274.994 411.983 268.138C409.111 264.419 405.295 261.537 400.932 259.794C396.569 258.05 391.818 257.508 387.174 258.224C378.594 260.138 382.626 260.117 298.774 306.236C275.486 319.043 269.654 316.423 263.219 329.436C258.11 339.763 257.949 351.65 250.513 354.377C247.499 355.482 240.7 354.952 238.033 352.206C233.48 347.526 237.814 331.042 239.702 324.779C241.797 317.829 240.206 319.782 246.873 313.765C252.041 309.101 245.456 305.588 166.291 249.896C165.124 249.276 163.793 249.032 162.482 249.2C153.033 269.89 166.059 284.253 146.767 282.376C143.055 282.017 142.041 281.25 140.787 278.476C131.703 258.235 140.865 232.895 163.667 220.756C175.107 214.666 149.022 229.411 260.941 171.34C290.529 156.005 299.133 123.731 336.484 80.9068Z" fill="#E2E2E2" />
                                    <path d="M465.876 131.856C447.541 119.878 424.336 110.656 404.401 99.3042C389.02 90.277 372.003 83.5015 355.82 74.9033C339.685 66.3233 339.591 70.8499 295.321 132.623C271.895 165.417 266.695 164.148 224.861 186.212C164.096 217.82 152.399 221.177 148.2 230.04C133.429 246.818 132.246 273.125 138.84 282.443C141.372 285.87 145.012 286.504 149.266 286.083C166.299 284.058 156.595 275.93 163.623 252.13C182.504 265.291 159.19 246.67 245.159 311.41C236.262 318.102 233.631 330.761 232.664 341.83C232.292 348.278 232.664 354.546 240.828 357.284C247.712 359.686 255.354 356.79 257.756 351.2C259.818 346.403 261.695 330.92 272.605 321.056C278.663 319.441 284.509 317.12 290.025 314.14C294.32 311.982 355.09 278.611 385.107 260.652C388.343 260.067 391.663 260.142 394.869 260.872C398.075 261.602 401.101 262.973 403.764 264.901C417.498 274.963 411.564 292.586 430.893 284.401C435.731 282.355 438.004 280.241 436.613 274.807C432.936 261.063 424.211 249.067 410.491 244.288C433.282 224.611 469.472 156.104 473.429 143.587C475.441 137.116 469.752 134.542 465.876 131.856ZM154.612 272.706C154.185 279.076 154.765 279.224 148.632 279.804C146.619 279.991 145.116 279.989 143.952 278.538C140.244 273.299 140.052 253.549 148.611 238.898C151.88 242.883 155.66 246.42 159.853 249.418C156.828 256.837 155.057 264.707 154.612 272.706V272.706ZM256.698 335.699C253.648 342.961 252.814 347.399 252.184 348.644C250.526 352.518 242.408 353.064 239.671 349.164C237.669 346.84 238.147 322.746 248.929 314.16C253.286 317.361 258.235 319.665 263.489 320.939C268.492 321.937 263.006 320.52 256.698 335.699ZM423.14 260.398C426.581 265.595 429.097 271.349 430.576 277.404C428.75 278.392 426.835 279.204 424.856 279.827C417.433 281.507 422.037 270.174 407.007 260.067C392.496 250.778 387.036 261.318 406.375 247.546C413.14 250.025 418.988 254.509 423.14 260.398V260.398ZM468.351 142.069C465.878 150.516 430.44 222.965 406.211 242.099C395.811 250.471 298.847 303.435 287.466 309.062C275.902 314.769 270.696 316.441 264.711 315.2C256.131 313.422 258.484 313.276 186.93 260.143C175.456 251.844 151.965 238.636 153.774 233.259C155.945 227.019 174.093 219.791 179.99 216.832C288.369 160.412 273.681 175.086 317.27 112.052C321.581 105.846 341.572 76.7077 347.461 77.7659C350.399 78.2859 451.113 129.306 457.891 133.263C468.582 139.706 468.965 140.031 468.351 142.069V142.069Z" fill="black" />
                                    <path d="M440.869 111.761C441.032 100.515 438.892 89.3544 434.581 78.9662C430.27 68.5779 423.88 59.1815 415.802 51.355C388.081 23.8288 344.432 41.5244 344.432 41.5244L340.301 46.4384C340.301 46.4384 337.745 41.5244 326.932 43.8852C316.118 46.246 299.465 71.5128 297.487 79.2868C294.733 90.1002 302.552 100.313 302.552 100.313C302.552 100.313 301.681 99.9854 297.398 107.835C293.116 115.684 300.165 138.437 300.165 138.437C300.165 138.437 287.685 119.077 287.495 104.801C287.44 100.547 287.123 86.3406 283.819 83.686C283.072 82.4042 278.938 79.604 280.875 70.4806C282.68 61.6406 290.93 61.3676 294.224 56.7578C295.386 54.8509 295.816 52.5867 295.433 50.3865C295.051 48.1864 293.882 46.2002 292.144 44.7978C279.924 35.5106 269.461 53.6378 268.91 52.8448C265.52 47.6763 261.38 43.0409 256.625 39.0908C245.341 30.6018 235.55 32.9678 233.145 37.367C231.34 40.5754 233.862 43.8488 235.802 47.013C224.991 45.6194 213.351 48.3494 217.56 56.633C218.876 59.233 222.573 59.6048 231.182 63.5854C223.46 65.3742 215.322 66.5728 216.265 72.3708C217.766 80.3762 228.834 76.5802 238.867 82.6616C240.746 83.5517 242.287 85.0257 243.259 86.8636C244.232 88.7014 244.583 90.8045 244.262 92.8588C243.178 100.035 240.729 103.472 247.159 111.501C252.28 117.736 255.829 125.11 257.507 133.003C258.578 140.496 269.552 158.964 269.552 158.964L252.689 171.366L235.269 166.8C235.269 166.8 239.07 146.557 233.724 136.414C228.379 126.271 224.016 120.486 209.945 118.414C195.874 116.342 189.439 117.322 189.548 127.904C189.657 138.486 191.511 177.426 191.729 184.844C191.948 192.262 202.855 194.771 207.217 194.984C226.169 195.933 201.903 191.719 201.357 209.206C200.959 221.964 199.407 247.054 199.407 247.054C191.931 247.265 184.707 249.797 178.734 254.298C168.789 261.82 167.515 274.698 172.494 280.054C177.473 285.41 219.294 286.034 219.294 286.034C219.294 286.034 234.338 287.308 238.547 281.187C242.757 275.067 242.624 255.944 242.624 255.944C242.624 255.944 259.199 273.028 272.332 275.834C285.464 278.639 308.794 267.42 319.631 260.918C330.468 254.415 374.452 216.296 374.452 216.296C374.452 216.296 401.864 218.462 414.866 211.96C427.869 205.457 435.903 192.2 435.776 181.108C435.7 174.43 435.017 167.772 433.735 161.218C450.216 142.233 447.281 116.42 440.869 111.761Z" fill="white" />
                                    <path d="M327.036 60.5199C321.68 64.6677 316.621 69.1854 311.896 74.0399C307.386 79.4439 304.58 86.064 303.832 93.0629C303.085 100.062 304.43 107.125 307.697 113.36C309.026 115.791 309.624 118.916 311.191 121.201C320.746 135.119 332.457 148.267 349.151 150.841C361.943 152.81 377.263 148.782 385.414 138.728C387.943 135.608 389.574 132.566 392.215 129.537C402.272 117.988 404.466 100.277 389.246 78.4807C384.468 72.3663 378.921 66.8933 372.744 62.1969C360.004 51.3263 339.027 50.4969 335.53 51.8281C331.24 53.4297 330.53 57.5767 327.036 60.5199Z" fill="black" />
                                    <path d="M254.306 248.76C269.849 270.941 290.966 261.947 291.434 263.546C292.505 265.384 266.952 275.311 250.38 251.495C234.676 228.925 248.511 206.474 250.245 208.026C251.586 208.803 239.33 227.404 254.306 248.76Z" fill="black" />
                                    <path d="M443.537 113.724C446.982 106.977 440.284 80.813 435.427 72.1758C419.12 43.1832 393.64 31.92 361.013 34.78C348.605 36.86 344.087 37.614 338.949 42.9076C325.231 36.47 312.751 48.0816 303.657 60.273C293.94 73.273 288.977 87.6276 298.896 100.552C292.635 106.574 292.895 114.35 294.694 123.003C286.062 97.804 292.245 93.7662 288.454 84.9964C286.634 80.7064 280.199 71.3984 288.015 65.577C290.995 63.3254 296.293 61.5106 298.178 56.3002C302.5 43.7162 284.076 29.71 269.979 49.6156C252.286 17.8956 224.219 28.1058 228.444 42.5748C213.385 40.682 205.803 56.919 218.202 63.068C216.58 63.5772 215.126 64.5189 213.999 65.7919C212.871 67.0649 212.111 68.6211 211.801 70.2934C210.855 85.1368 230.654 83.0594 236.501 86.5746C244.447 91.1948 235.368 97.1514 238.911 105.245C241.673 111.862 246.836 110.931 252.564 126.105C256.443 136.435 257.689 144.263 266.638 157.859C261.103 160.142 256.011 163.379 251.594 167.422C246.612 165.804 241.539 164.48 236.402 163.454C241.158 146.616 236.787 122.374 217.451 116.524C210.389 114.506 193.752 111.743 188.768 117.304C185.388 121.079 185.934 121.724 186.358 148.546C186.651 167.019 187.455 175.047 188.237 185.206C189.576 193.461 195.593 196.69 203.923 197.686C189.215 212.222 196.677 246.462 194.1 246.54C175.49 247.226 164.533 261.484 166.374 275.03C167.84 289.263 191.212 287.588 204.074 288.774C240.084 291.717 246.277 285.774 245.812 263.133C245.762 260.806 244.907 262.158 252.53 268.411C264.384 278.031 278.317 282.568 292.773 277.396C313.053 270.142 344.833 250.666 375.944 219.388C408.652 222.341 437.759 212.547 438.651 180.648C438.624 173.924 437.938 167.218 436.602 160.628C446.644 147.082 453.877 126.256 443.537 113.724ZM338 54.1916C345.02 51.1028 371.337 58.9158 388.786 84.0474C395.546 93.8364 399.727 103.027 395.59 114.116C389.961 129.735 376.982 146.356 360.526 148.673C341.198 151.793 312.601 132.121 306.249 100.352C307.549 96.0802 306.459 84.2476 322.655 68.2082C330.2 60.8138 333.694 59.3084 338 54.1916ZM299.374 83.92C300.864 72.2876 319.805 42.983 335.668 46.74C325.72 59.2798 304.785 69.0844 300.971 94.9752C299.416 91.5118 298.863 87.6819 299.374 83.92V83.92ZM218.92 52.1714C220.425 49.5896 225.42 48.066 228.423 48.4898C235.271 49.6676 233.119 50.2136 238.183 53.0346C243.412 55.8946 247.845 58.4946 251.519 60.9464C241.758 69.061 249.244 69.3184 240.422 65.759C237.757 64.745 240.209 65.4288 224.211 58.6506C222.399 58.1432 220.729 57.222 219.333 55.9596C218.566 54.5244 218.179 53.5 218.92 52.1714ZM246.696 106.082C241.613 100.469 246.074 98.3006 246.191 91.7174C255.866 93.6206 263.887 89.9598 263.57 89.0108C263.214 87.872 255.385 90.3524 245.63 87.8018C242.902 79.331 230.168 77.2718 222.006 75.9016C214.321 72.441 218.122 66.955 228.506 67.0382C242.754 72.4254 243.539 71.3724 243.116 72.5528C242.219 75.0592 242.141 76.7414 242.596 76.9104C243.604 77.2666 246.275 71.687 251.29 66.2504C256.23 60.741 261.526 57.556 261.074 56.5836C260.866 56.1416 259.197 56.3756 256.786 57.5092C253.487 59.0458 258.5 58.8092 241.228 48.0452C227.448 38.9972 237.297 32.6168 247.759 36.223C255.962 39.0388 262.426 46.8128 267.379 53.903C263.679 61.2298 263.479 67.0954 264.259 67.0772C264.911 67.1266 265.639 61.144 271.071 53.6482C280.823 40.3622 290.113 42.9882 292.984 48.846C297.375 57.5794 285.644 58.206 281.167 65.1064C279.91 66.9665 279.087 69.0854 278.759 71.3064C278.432 73.5274 278.607 75.7936 279.274 77.9374C278.596 79.4918 277.544 80.8541 276.211 81.9024C271.97 85.1056 263.851 87.7576 264.189 88.8002C264.488 89.744 276.752 87.4092 280.922 82.1728C283.223 87.0374 284.531 86.3068 284.266 94.6346C276.9 104.538 257.436 110.053 246.696 106.082ZM266.617 110.37C273.355 108.808 279.487 105.307 284.258 100.3C284.433 106.028 285.423 111.702 287.199 117.151C278.043 121.531 268.405 124.82 258.482 126.95C250.739 101.907 244.941 116.111 266.617 110.37ZM259.077 128.9C269.235 127.68 279.037 124.394 287.877 119.244C288.605 121.425 289.526 123.924 290.454 126.173C281.129 130.657 271.31 134.03 261.199 136.224C260.502 133.905 259.828 131.453 259.069 128.9H259.077ZM238.961 272.259C237.401 287.113 216.471 282.456 190.601 281.85C184.696 281.647 173.628 280.769 172.058 275.067C201.669 278.325 229.297 277.42 229.286 276.232C229.276 275.043 205.572 275.067 171.753 271.188C171.857 268.817 172.335 266.477 173.17 264.256C178.399 251.708 195.205 247.379 207.36 251.802C214.258 254.314 216.908 258.026 217.381 257.548C217.901 257.111 212.612 249.353 201.289 247.127C201.354 235.568 199.82 213.925 206.736 204.612C210.311 199.799 216.762 196.973 222.318 199.295C238.191 205.935 240.149 261.635 238.953 272.259H238.961ZM348.855 214.749C348.772 215.995 368.571 218.59 369.936 218.753C348.338 237.359 317.088 261.393 290.612 271.245C273.712 277.542 257.917 268.159 245.552 255.044C241.912 169.335 211.44 198.957 197.192 188.864C196.478 188.391 195.883 187.761 195.451 187.022C195.02 186.282 194.764 185.454 194.704 184.6C193.924 174.504 193.144 166.863 192.852 148.416C192.439 122.247 192.252 122.577 193.783 121.558C195.422 120.809 197.203 120.425 199.004 120.432C200.551 141.097 199.815 181.727 201.591 181.685C202.61 181.662 204.373 141.721 202.842 120.489C207.18 120.795 211.487 121.453 215.719 122.457C234.377 127.714 237.206 155.833 229.307 173.157C227.157 177.666 225.501 180 225.888 180.331C226.499 180.851 231.182 175.911 234.039 169.84C252.104 173.292 271.479 180.671 283.652 194.755C294.367 207.001 311.472 246.495 314.616 244.657C315.424 244.194 312.967 238.038 308.929 228.082C308.066 225.953 307.692 227.141 316.282 232.242C327.649 238.906 327.938 237.268 318.217 229.468C302.877 216.676 304.957 218.187 301.272 210.66C292.079 191.979 282.727 179.584 258.539 169.923C262.526 167.742 266.184 165.007 269.404 161.8C271.305 164.367 277.394 171.621 278.153 170.934C279.084 170.123 269.888 160.399 262.675 140.847C260.954 135.977 260.824 140.192 276.497 134.867C298.384 127.457 286.029 122.486 298.951 142.311C305.809 152.755 319.47 169.127 334.916 176.631C342.378 180.195 346.954 180.843 347.032 180.531C347.552 178.971 327.717 174.59 312.117 151.471C306.322 142.919 301.891 131.451 303.241 133.674C309.393 144.563 318.583 153.425 329.688 159.18C345.886 167.5 367.848 169.335 383.011 160.443C388.462 157.083 393.257 152.761 397.163 147.688C395.244 155.644 395.021 164.367 400.044 170.744C389.802 176.436 378.519 180.007 366.868 181.243C356.398 182.187 349.869 180.414 349.64 181.316C349.224 182.231 365.57 189.737 390.79 180.822C390.612 182.769 390.04 184.659 389.111 186.378C387.122 190.146 352.838 189.758 352.778 191.625C352.711 193.877 377.723 194.16 384.043 193.185C386.322 197.863 389.696 201.921 393.88 205.015C398.063 208.108 402.933 210.146 408.073 210.953C385.099 218.79 349.066 211.349 348.847 214.749H348.855ZM412.396 209.24C407.059 208.652 401.924 206.867 397.374 204.016C392.824 201.165 388.977 197.323 386.121 192.777C388.19 192.407 390.136 191.531 391.786 190.229C400.359 198.187 411.724 202.444 423.415 202.077C420.155 205.05 416.43 207.47 412.389 209.24H412.396ZM425.396 200.036C413.675 199.879 402.33 195.879 393.102 188.651C393.92 187.226 394.478 185.667 394.748 184.046C396.17 176.379 392.332 181.423 404.544 174.592C413.408 179.46 424.198 174.369 431.683 166.506C433.103 178.703 433.373 190.946 425.383 200.036H425.396ZM443.95 129.54C442.598 144.62 426.475 170.57 411.473 169.941C393.609 169.195 402.922 128.364 426.691 111.961C430.102 109.764 432.531 109.678 432.513 109.143C432.695 108.35 425.664 106.517 413.728 118.149C419.567 98.9688 411.848 86.5538 411.341 86.7904C409.357 87.5392 424.679 105.708 397.093 141.497C386.155 155.618 375.955 162.479 357.274 161.751C319.207 160.474 297.656 123.45 300.204 109.091C300.813 107.391 301.868 105.887 303.259 104.736C311.922 144.061 360.573 177.855 392.054 132.896C406.476 111.758 405.712 98.9714 393.076 81.0262C378.092 59.9844 355.165 49.1164 341.819 49.4388C343.197 47.6259 344.884 46.0713 346.804 44.8472C351.877 43.9637 357.096 44.5186 361.871 46.4488C392.688 57.4052 410.387 86.3744 411.011 86.078C413.655 85.246 394.961 53.3336 363.392 42.0756C360.643 41.1188 360.698 41.4022 361.85 41.2072C398.208 38.0378 422.952 54.8 433.42 83.8628C435.518 90.1392 438.482 104.678 438.001 109.566C438.001 109.754 435.289 108.828 435.154 109.447C434.881 110.323 445.492 113.399 443.937 129.54H443.95Z" fill="black" />
                                    <path d="M183.851 64.6409C180.448 81.2471 180.453 80.9533 180.508 81.2159C181.028 83.8159 188.997 73.8891 198.026 72.8959C194.205 77.6677 191.24 83.0665 189.264 88.8521L201.484 86.4757C204.955 85.7997 208.159 86.8137 207.046 88.1111C201.179 92.5378 195.619 97.358 190.406 102.538C168.784 123.713 169.606 127.472 166.163 132.259C164.405 134.29 162.762 136.417 161.242 138.632C159.016 142.597 158.363 148.821 153.2 146.827C148.447 144.991 138.949 143.496 138.609 137.048C138.107 127.641 153.234 89.1199 183.851 64.6409Z" fill="#E2E2E2" />
                                    <path d="M127.197 138.642C124.106 131.864 139.064 137.22 140.4 137.233C144.56 137.256 145.613 132.228 152.498 129.085C156.187 127.4 155.561 141.182 156.936 143.158C157.414 143.741 158.013 144.213 158.691 144.542C161.697 146.29 164.56 148.276 167.25 150.48C168.891 151.824 168.394 152.953 166.553 153.925C159.071 157.874 156.039 158.264 155.418 158.972C155.074 159.364 155.241 159.06 151.798 172.208C150.896 175.658 144.69 167.648 142.129 164.741C141.68 164.038 141.016 163.498 140.237 163.2C139.458 162.902 138.604 162.861 137.8 163.085C136.105 163.249 126.326 164.211 125.245 162.892C123.373 160.615 131.771 151.77 131.906 149.336C132.046 146.999 127.197 140.86 127.197 138.642Z" fill="#FDFE1F" />
                                    <path d="M207.964 87.1934C208.465 85.7608 197.4 83.6288 189.901 88.0098C190.083 87.7498 200.681 71.4218 199.984 70.8498C199.412 69.5238 186.576 75.483 181.1 81.3954C181.472 80.3008 185.78 64.2354 184.506 63.835C184.148 63.3592 155.906 80.2306 140.358 123.008C138.875 126.815 137.828 130.778 137.238 134.82C131.178 133.455 127.694 131.776 125.538 135.145C123.828 137.818 125.193 139.201 130.039 149.705C124.155 159.614 122.52 161.036 124.16 163.927C126.071 167.284 128.939 166.051 139.831 164.764L147.371 173.344C147.911 173.956 148.611 174.404 149.392 174.639C150.173 174.874 151.004 174.887 151.792 174.675C152.58 174.463 153.293 174.035 153.851 173.44C154.409 172.845 154.789 172.106 154.95 171.306L157.175 160.1L167.671 155.581C168.421 155.258 169.065 154.732 169.531 154.062C169.998 153.391 170.267 152.604 170.309 151.789C170.351 150.974 170.165 150.163 169.77 149.448C169.376 148.734 168.789 148.144 168.077 147.745L160.42 143.46C182.203 105.318 209.243 88.704 207.964 87.1934ZM166.218 152.178C153.299 157.739 153.962 157.321 153.865 157.815C151.195 171.415 151.45 171.127 150.667 171.127C149.76 171.127 141.937 160.768 140.99 160.893C129.654 162.375 128.25 162.432 127.975 162.432C126.295 162.432 127.748 160.838 134.051 150.212C134.311 149.775 134.519 150.537 128.63 137.766C128.576 137.653 128.556 137.526 128.571 137.402C128.586 137.277 128.635 137.16 128.714 137.062C128.793 136.964 128.897 136.891 129.016 136.85C129.134 136.809 129.262 136.802 129.384 136.83C139.123 138.996 141.97 139.711 142.236 139.711C142.618 139.711 141.955 140.207 152.251 130.689C152.343 130.604 152.457 130.546 152.58 130.523C152.703 130.499 152.83 130.51 152.947 130.555C153.064 130.6 153.165 130.677 153.241 130.777C153.317 130.877 153.363 130.996 153.374 131.12C154.697 145.16 154.479 144.365 154.918 144.609C164.736 150.111 168.064 151.385 166.218 152.178ZM158.036 141.292L157.063 130.785C156.965 129.698 156.463 128.688 155.655 127.954C154.848 127.219 153.794 126.815 152.703 126.82C150.314 126.82 149.932 127.803 141.346 135.743L140.265 135.499C140.931 131.615 142.022 127.816 143.52 124.171C154.284 95.9788 165.1 83.6938 183.136 65.5562C179.683 72.389 178.976 82.7838 180.242 82.854C180.692 83.4338 185.097 80.7376 190.489 77.4512C199.384 71.9756 196.339 72.896 192.53 78.8942C189.225 84.05 188.006 88.9718 188.682 89.1954C188.96 89.9 193.164 88.7326 198.354 88.1138C202.072 87.6172 205.309 87.7238 206.913 87.5626C195.299 93.6726 172.136 114.826 158.036 141.292Z" fill="black" />
                                </svg>
                            </span>
                AstroStudio AI Assistant
            </h3>
            <button onClick={toggleChat} className="text-white hover:text-gray-200 transition-colors">
              <FiX className="h-5 w-5" />
            </button>
          </div>
          
          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-800/40 backdrop-blur-md">
            {messages.map((msg) => (
              <div
                key={msg.id}
                className={`mb-3 ${
                  msg.isUser ? 'ml-auto max-w-[80%]' : 'mr-auto max-w-[80%]'
                }`}
              >
                <div
                  className={`p-3 rounded-lg ${
                    msg.isUser
                      ? 'bg-purple-600 text-white rounded-br-none'
                      : 'bg-gray-700 text-gray-100 rounded-bl-none'
                  }`}
                >
                  {msg.isUser ? (
                    msg.text
                  ) : (
                    <ReactMarkdown
                      className="prose prose-invert prose-sm max-w-none"
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeRaw, rehypeSanitize]}
                      components={{
                        code({className, children, inline, ...props}: any) {
                          const match = /language-(\w+)/.exec(className || '');
                          return !inline && match ? (
                            <SyntaxHighlighter
                              {...props}
                              style={atomDark as any}
                              language={match[1]}
                              PreTag="div"
                              wrapLongLines={true}
                              showLineNumbers={true}
                            >
                              {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                          ) : (
                            <code className={`bg-gray-800 px-1 py-0.5 rounded ${className || ''}`} {...props}>
                              {children}
                            </code>
                          )
                        },
                        pre({children, ...props}: any) {
                          return (
                            <pre className="rounded-md overflow-hidden my-4" {...props}>
                              {children}
                            </pre>
                          )
                        },
                        table({children, ...props}: any) {
                          return (
                            <div className="overflow-x-auto my-4 rounded-md border border-gray-600">
                              <table className="min-w-full text-sm" {...props}>
                                {children}
                              </table>
                            </div>
                          )
                        },
                        thead({children, ...props}: any) {
                          return (
                            <thead className="bg-gray-800" {...props}>
                              {children}
                            </thead>
                          )
                        },
                        th({children, ...props}: any) {
                          return (
                            <th className="px-4 py-3 border-b border-gray-600 font-medium text-left" {...props}>
                              {children}
                            </th>
                          )
                        },
                        td({children, ...props}: any) {
                          return (
                            <td className="px-4 py-3 border-b border-gray-600" {...props}>
                              {children}
                            </td>
                          )
                        },
                        a({children, ...props}: any) {
                          return (
                            <a className="text-cyan-400 hover:underline" target="_blank" rel="noopener noreferrer" {...props}>
                              {children}
                            </a>
                          )
                        },
                        p({children, ...props}: any) {
                          return (
                            <p className="mb-3" {...props}>
                              {children}
                            </p>
                          )
                        },
                        h1({children, ...props}: any) {
                          return (
                            <h1 className="text-xl font-bold mt-5 mb-3 pb-2 border-b border-gray-700" {...props}>
                              {children}
                            </h1>
                          )
                        },
                        h2({children, ...props}: any) {
                          return (
                            <h2 className="text-lg font-bold mt-4 mb-3" {...props}>
                              {children}
                            </h2>
                          )
                        },
                        h3({children, ...props}: any) {
                          return (
                            <h3 className="text-md font-bold mt-3 mb-2" {...props}>
                              {children}
                            </h3>
                          )
                        },
                        ul({children, ...props}: any) {
                          return (
                            <ul className="list-disc list-inside my-3 pl-2 space-y-1" {...props}>
                              {children}
                            </ul>
                          )
                        },
                        ol({children, ...props}: any) {
                          return (
                            <ol className="list-decimal list-inside my-3 pl-2 space-y-1" {...props}>
                              {children}
                            </ol>
                          )
                        },
                        li({children, ...props}: any) {
                          return (
                            <li className="mb-1" {...props}>
                              {children}
                            </li>
                          )
                        },
                        blockquote({children, ...props}: any) {
                          return (
                            <blockquote className="border-l-4 border-purple-500 pl-4 my-3 italic bg-gray-800/30 py-2 pr-2 rounded-r" {...props}>
                              {children}
                            </blockquote>
                          )
                        },
                        hr({...props}: any) {
                          return (
                            <hr className="my-4 border-gray-600" {...props} />
                          )
                        },
                        img({...props}: any) {
                          return (
                            <img className="max-w-full h-auto rounded-md my-3" {...props} alt={props.alt || "Image"} />
                          )
                        }
                      }}
                    >
                      {msg.text}
                    </ReactMarkdown>
                  )}
                </div>
                <div
                  className={`text-xs mt-1 text-gray-400 ${
                    msg.isUser ? 'text-right' : 'text-left'
                  }`}
                >
                  {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="mr-auto max-w-[80%] mb-3">
                <div className="bg-gray-700 text-white p-3 rounded-lg rounded-bl-none">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
          
          {/* Input */}
          <form onSubmit={handleSubmit} className="p-3 bg-gray-800 border-t border-gray-700 flex">
            <input
              ref={inputRef}
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <button
              type="submit"
              className="bg-gradient-to-r from-purple-600 to-cyan-400 text-white px-4 py-2 rounded-r-md hover:from-purple-700 hover:to-cyan-500 focus:outline-none"
            >
              <FiSend />
            </button>
          </form>
        </div>
      )}
      
      {/* Chat button */}
      <button
        onClick={toggleChat}
        className={`bg-gradient-to-r from-purple-600 to-cyan-400 hover:from-purple-700 hover:to-cyan-500 text-white p-4 rounded-full shadow-lg flex items-center justify-center transform transition-transform duration-300 ${
          isOpen ? 'rotate-90 scale-0 opacity-0' : 'scale-100 opacity-100'
        }`}
      >
        {isOpen ? <FiX className="h-6 w-6" /> : <FiMessageSquare className="h-6 w-6" />}
      </button>
      
      {/* Quick close button for mobile */}
      {isOpen && (
        <button
          onClick={toggleChat}
          className="md:hidden fixed bottom-20 right-5 bg-gray-800 text-white p-2 rounded-full shadow-lg flex items-center justify-center"
        >
          <FiChevronDown className="h-6 w-6" />
        </button>
      )}
    </div>
  );
} 