import React from 'react';
import { Message, MessageData } from '@/lib/message-utils';

export interface MessageRendererProps {
  message: Message;
  onSelectImage?: (image: any) => void;
  onDownloadImage?: (url: string) => void;
  onSaveImage?: (url: string, index: number) => Promise<string | null>;
  isSaving?: boolean;
  ReplicateImagesGrid?: React.ComponentType<any>;
  GeneratedImagesGrid?: React.ComponentType<any>;
}

export const MessageRenderer: React.FC<MessageRendererProps> = ({
  message,
  onSelectImage,
  onDownloadImage,
  onSaveImage,
  isSaving = false,
  ReplicateImagesGrid,
  GeneratedImagesGrid
}) => {
  // If message has data, reconstruct the content from it
  if (message.data) {
    const { data } = message;
    
    switch (data.type) {
      case 'text':
        return (
          <div>
            {data.uploadedImages && data.uploadedImages.length > 0 ? (
              <div className="flex flex-wrap gap-2 mb-2">
                {data.uploadedImages.map((img, index) => (
                  <img key={index} src={img} alt={`Uploaded ${index + 1}`} className="max-h-32 rounded" />
                ))}
              </div>
            ) : data.uploadedImage && (
              <img src={data.uploadedImage} alt="Uploaded" className="max-h-32 mb-2 rounded" />
            )}
            <span>{data.text}</span>
          </div>
        );
        
      case 'image-grid':
        if (data.images && data.images.length > 0) {
          const isReplicate = data.provider === 'Replicate' || data.modelProvider === 'Replicate';
          
          if (isReplicate && ReplicateImagesGrid) {
            return (
              <ReplicateImagesGrid
                images={data.images}
                isGenerating={false}
                numImages={data.numImages || data.images.length}
                selectedModel={data.selectedModel || ''}
                modelProvider={data.provider || 'Replicate'}
                isSaving={isSaving}
                onSelectImage={onSelectImage}
                onDownloadImage={onDownloadImage}
              />
            );
          } else if (!isReplicate && GeneratedImagesGrid) {
            return (
              <GeneratedImagesGrid
                images={data.images}
                isGenerating={false}
                numImages={data.numImages || data.images.length}
                selectedModel={data.selectedModel || ''}
                onSaveImage={onSaveImage}
                isSaving={isSaving}
                onSelectImage={onSelectImage}
                onDownloadImage={onDownloadImage}
              />
            );
          }
        }
        return <div>Generated images (grid component not available)</div>;
        
      case 'image-upload':
        return (
          <div className="flex flex-col gap-2">
            <p>{data.text}</p>
            <div className="relative rounded-lg overflow-hidden bg-checkerboard">
              <img
                src={data.uploadedImage}
                alt="Pasted image"
                className="max-h-[300px] w-full object-contain"
              />
            </div>
          </div>
        );
        
      default:
        return <div>Unknown message type</div>;
    }
  }
  
  // Fallback to original content if no data
  return <>{message.content}</>;
};

export default MessageRenderer;
