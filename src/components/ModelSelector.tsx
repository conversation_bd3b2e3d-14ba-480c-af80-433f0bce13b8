import { useState } from 'react';
import { FiSliders } from 'react-icons/fi';

// Base model interface that all tools will use
export interface AIModel {
  id: string;
  name: string;
  description?: string;
  provider: string;
  capabilities?: string[];
  maxDuration?: number;
  [key: string]: any; // For additional tool-specific properties
}

// Categories of models
export interface ModelCategories {
  [category: string]: AIModel[];
}

interface ModelSelectorProps {
  modelsByCategory: ModelCategories;
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  defaultCategory?: string;
  showAdvancedSettings?: boolean;
  onToggleAdvancedSettings?: () => void;
  renderCapability?: (capability: string, index: number) => React.ReactNode;
  filterCategories?: (category: string) => boolean;
}

/**
 * Standardized model selector component that can be used across all AI tools
 */
export default function ModelSelector({
  modelsByCategory,
  selectedModel,
  onModelChange,
  defaultCategory = Object.keys(modelsByCategory)[0],
  showAdvancedSettings = false,
  onToggleAdvancedSettings,
  renderCapability,
  filterCategories,
}: ModelSelectorProps) {
  // Filter categories if filterCategories prop is provided
  const visibleCategories = filterCategories 
    ? Object.keys(modelsByCategory).filter(filterCategories)
    : Object.keys(modelsByCategory);

  // Use the first visible category as default if the provided default is not visible
  const effectiveDefaultCategory = visibleCategories.includes(defaultCategory)
    ? defaultCategory
    : visibleCategories[0];
  
  const [activeCategory, setActiveCategory] = useState<string>(effectiveDefaultCategory);

  // Handle category change
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    // Don't auto-select first model when changing category to preserve user selection if possible
    if (!modelsByCategory[category].some(model => model.id === selectedModel)) {
      onModelChange(modelsByCategory[category][0].id);
    }
  };

  // Get the currently selected model
  const getSelectedModel = (): AIModel | undefined => {
    for (const category of Object.keys(modelsByCategory)) {
      const model = modelsByCategory[category].find(m => m.id === selectedModel);
      if (model) {
        return model;
      }
    }
    return undefined;
  };

  // Default capability renderer
  const defaultRenderCapability = (capability: string, index: number) => {
    return (
      <span 
        key={index}
        className="inline-block bg-purple-900/30 text-purple-300 text-xs px-2 py-1 rounded-md"
      >
        {capability}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-white mb-4">Model Selection</h2>
      
      {/* Model Categories */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Model Category
        </label>
        <div className="relative">
          <div className="overflow-x-auto pb-1 hide-scrollbar">
            <div className="tabs flex space-x-2 border-b border-gray-700 whitespace-nowrap">
              {visibleCategories.map((category, index) => (
                <button 
                  key={category}
                  type="button" 
                  className={`tab-btn py-2 px-3 text-xs font-medium rounded-t-lg transition-all duration-200 ${
                    category === activeCategory 
                      ? 'bg-purple-900/30 text-purple-400 border-b-2 border-purple-500' 
                      : 'text-gray-400 hover:text-purple-300 hover:bg-purple-900/20'
                  }`}
                  onClick={() => handleCategoryChange(category)}
                >
                  {category}
                  {category === activeCategory && (
                    <span className="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-[10px] text-white bg-purple-600 rounded-full">
                      {modelsByCategory[category].length}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile scroll hint - Removed as requested */}

          {/* Tabs scroll indicator */}
          <div className="absolute -bottom-2 left-0 right-0 flex justify-center space-x-1 tabs-indicator">
            {visibleCategories.map((category) => (
              <span 
                key={category}
                className={`h-1.5 ${
                  category === activeCategory 
                    ? 'w-8 bg-purple-500' 
                    : 'w-1.5 bg-gray-700'
                } rounded-full transition-all duration-300`}
              ></span>
            ))}
          </div>
        </div>
      </div>
      
      {/* Models */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium text-gray-300">
            Select Model
          </label>
          {onToggleAdvancedSettings && (
            <button 
              onClick={onToggleAdvancedSettings}
              className="text-xs text-purple-400 hover:text-purple-300 flex items-center"
            >
              <FiSliders className="mr-1 h-3 w-3" />
              {showAdvancedSettings ? 'Hide' : 'Show'} Advanced Settings
            </button>
          )}
        </div>
        
        {/* Model cards by category */}
        <div className="model-categories mt-4">
          <div key={activeCategory} className="model-category space-y-3">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-sm font-medium text-purple-400">
                {activeCategory}
              </h3>
              <div className="text-xs text-purple-400 bg-purple-900/30 px-2 py-1 rounded-md">
                {modelsByCategory[activeCategory].length} models
              </div>
            </div>
            
            <div className="space-y-3 max-h-[340px] overflow-y-auto hide-scrollbar pr-1">
              {modelsByCategory[activeCategory].map((model) => (
                <div 
                  key={model.id}
                  onClick={() => onModelChange(model.id)}
                  className={`cursor-pointer rounded-lg p-3 transition-all ${
                    selectedModel === model.id
                      ? 'bg-purple-900/30 border border-purple-500'
                      : 'bg-gray-800/70 hover:bg-gray-800 border border-gray-700 hover:border-gray-600'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-center">
                      {/* Provider Icon */}
                      <div className={`w-8 h-8 rounded-full mr-3 flex items-center justify-center ${
                        selectedModel === model.id ? 'bg-purple-700' : 'bg-gray-700'
                      }`}>
                        {model.provider.toLowerCase().includes('together') && (
                          <span className="text-xs font-bold">T</span>
                        )}
                        {model.provider.toLowerCase().includes('replicate') && (
                          <span className="text-xs font-bold">R</span>
                        )}
                        {model.provider.toLowerCase().includes('stability') && (
                          <span className="text-xs font-bold">S</span>
                        )}
                        {model.provider.toLowerCase().includes('google') && (
                          <span className="text-xs font-bold">G</span>
                        )}
                        {model.provider.toLowerCase().includes('meta') && (
                          <span className="text-xs font-bold">M</span>
                        )}
                        {model.provider.toLowerCase().includes('openai') && (
                          <span className="text-xs font-bold">O</span>
                        )}
                        {model.provider.toLowerCase().includes('anthropic') && (
                          <span className="text-xs font-bold">A</span>
                        )}
                        {model.provider.toLowerCase().includes('elevenlabs') && (
                          <span className="text-xs font-bold">E</span>
                        )}
                        {model.provider.toLowerCase().includes('deepgram') && (
                          <span className="text-xs font-bold">D</span>
                        )}
                        {/* Additional providers can be added as needed */}
                      </div>
                      <div>
                        <div className="font-medium text-white flex items-center">
                          {model.name}
                          {selectedModel === model.id && (
                            <span className="ml-2 text-xs bg-purple-600 text-white px-2 py-0.5 rounded-full">
                              Selected
                            </span>
                          )}
                        </div>
                        <div className="mt-0.5 text-xs text-gray-400 capitalize">{model.provider}</div>
                      </div>
                    </div>
                  </div>
                  {model.description && (
                    <p className="mt-2 text-sm text-gray-300 pl-11">{model.description}</p>
                  )}
                  {/* Model Features/Capabilities */}
                  {model.capabilities && model.capabilities.length > 0 && (
                    <div className="mt-3 pl-11 flex flex-wrap gap-2">
                      {model.capabilities.map((capability, index) => (
                        renderCapability 
                          ? renderCapability(capability, index) 
                          : defaultRenderCapability(capability, index)
                      ))}
                    </div>
                  )}
                  {/* Max Duration (for audio/music generation) */}
                  {model.maxDuration && (
                    <div className="mt-2 pl-11 text-xs text-gray-500">
                      Max duration: {model.maxDuration} seconds
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Custom Styles for Scrollbar */}
      <style jsx>{`
        .hide-scrollbar::-webkit-scrollbar {
          width: 5px;
          height: 5px;
        }
        
        .hide-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        
        .hide-scrollbar::-webkit-scrollbar-thumb {
          background: #4B5563;
          border-radius: 10px;
        }
        
        .hide-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #6366F1;
        }
        
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  );
} 