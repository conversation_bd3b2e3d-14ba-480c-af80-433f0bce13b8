'use client';

import { useEffect, useRef } from 'react';

interface PerformanceMonitorProps {
  name: string;
  enabled?: boolean;
  children?: React.ReactNode;
}

/**
 * Performance monitoring component to track re-renders and performance metrics
 * Only active in development mode
 */
export default function PerformanceMonitor({ 
  name, 
  enabled = process.env.NODE_ENV === 'development',
  children 
}: PerformanceMonitorProps) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  const mountTime = useRef(Date.now());

  useEffect(() => {
    if (!enabled) return;

    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    const timeSinceMount = now - mountTime.current;
    
    // Log performance metrics
    console.group(`🔍 Performance Monitor: ${name}`);
    console.log(`Render #${renderCount.current}`);
    console.log(`Time since last render: ${timeSinceLastRender}ms`);
    console.log(`Time since mount: ${timeSinceMount}ms`);
    
    // Warn about frequent re-renders
    if (renderCount.current > 10 && timeSinceMount < 5000) {
      console.warn(`⚠️ Frequent re-renders detected for ${name}! Consider optimizing with useMemo/useCallback`);
    }
    
    // Warn about slow renders
    if (timeSinceLastRender > 100) {
      console.warn(`⚠️ Slow render detected for ${name}! Render took ${timeSinceLastRender}ms`);
    }
    
    console.groupEnd();
    
    lastRenderTime.current = now;
  });

  // Track component unmount
  useEffect(() => {
    if (!enabled) return;
    
    return () => {
      console.log(`🔄 Component unmounted: ${name} (Total renders: ${renderCount.current})`);
    };
  }, [name, enabled]);

  return children ? <>{children}</> : null;
}

/**
 * Hook to monitor component performance
 */
export function usePerformanceMonitor(name: string, dependencies: any[] = []) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;
    
    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    
    console.log(`🔍 ${name} - Render #${renderCount.current} (${timeSinceLastRender}ms since last)`);
    
    if (dependencies.length > 0) {
      console.log(`Dependencies:`, dependencies);
    }
    
    lastRenderTime.current = now;
  });
}
