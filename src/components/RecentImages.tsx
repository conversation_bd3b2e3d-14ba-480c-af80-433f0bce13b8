'use client';

import { useState, useEffect } from 'react';
import { getRecentImages, deleteImage } from '@/lib/blob';
import Image from 'next/image';
import { FiTrash2, FiDownload, FiInfo } from 'react-icons/fi';

interface RecentImagesProps {
  userId: string;
  limit?: number;
}

interface ImageItem {
  id: string;
  url: string;
  filename: string;
  prompt: string;
  createdAt: string;
}

export default function RecentImages({ userId, limit = 8 }: RecentImagesProps) {
  const [images, setImages] = useState<ImageItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);

  useEffect(() => {
    const fetchImages = async () => {
      try {
        setLoading(true);
        const recentImages = await getRecentImages(userId, limit);
        setImages(recentImages);
        setError(null);
      } catch (err) {
        console.error('Error fetching images:', err);
        setError('Failed to load recent images');
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
  }, [userId, limit]);

  const handleDelete = async (imageId: string) => {
    try {
      const success = await deleteImage(imageId, userId);
      if (success) {
        setImages(images.filter(img => img.id !== imageId));
        if (selectedImage?.id === imageId) {
          setSelectedImage(null);
        }
      } else {
        setError('Failed to delete image');
      }
    } catch (err) {
      console.error('Error deleting image:', err);
      setError('Failed to delete image');
    }
  };

  const handleDownload = (url: string, filename: string) => {
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 py-4">
        {error}
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        No images found. Generate some images to see them here!
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold">Recent Generations</h3>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {images.map(image => (
          <div 
            key={image.id} 
            className="relative group rounded-lg overflow-hidden border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="relative aspect-square">
              <Image
                src={image.url}
                alt={image.prompt || 'Generated image'}
                fill
                sizes="(max-width: 640px) 45vw, (max-width: 768px) 30vw, 25vw"
                className="object-cover cursor-pointer"
                onClick={() => setSelectedImage(image)}
              />
            </div>
            
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-end justify-end">
              <div className="p-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => handleDelete(image.id)}
                  className="text-white bg-red-500 hover:bg-red-600 p-1.5 rounded-full"
                  title="Delete image"
                >
                  <FiTrash2 size={16} />
                </button>
                <button
                  onClick={() => handleDownload(image.url, image.filename)}
                  className="text-white bg-indigo-500 hover:bg-indigo-600 p-1.5 rounded-full"
                  title="Download image"
                >
                  <FiDownload size={16} />
                </button>
                <button
                  onClick={() => setSelectedImage(image)}
                  className="text-white bg-gray-700 hover:bg-gray-800 p-1.5 rounded-full"
                  title="View details"
                >
                  <FiInfo size={16} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Image details modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col md:flex-row">
            <div className="relative md:w-2/3 h-64 md:h-auto">
              <Image
                src={selectedImage.url}
                alt={selectedImage.prompt || 'Generated image'}
                fill
                className="object-contain"
              />
            </div>
            
            <div className="p-6 md:w-1/3 flex flex-col h-full overflow-y-auto">
              <h3 className="text-xl font-semibold mb-4 truncate">
                {selectedImage.filename}
              </h3>
              
              <div className="space-y-4 flex-grow">
                <div>
                  <p className="text-sm font-medium text-gray-500">Prompt</p>
                  <p className="text-gray-800">{selectedImage.prompt || 'No prompt available'}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-500">Created</p>
                  <p className="text-gray-800">{formatDate(selectedImage.createdAt)}</p>
                </div>
              </div>
              
              <div className="flex mt-6 space-x-3">
                <button
                  onClick={() => handleDownload(selectedImage.url, selectedImage.filename)}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
                >
                  <FiDownload />
                  Download
                </button>
                <button
                  onClick={() => handleDelete(selectedImage.id)}
                  className="flex items-center justify-center gap-2 px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700"
                >
                  <FiTrash2 />
                  Delete
                </button>
              </div>
              
              <button
                onClick={() => setSelectedImage(null)}
                className="mt-4 text-gray-600 hover:text-gray-800 text-center text-sm"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 