'use client';

import { useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentUserSubscription } from '@/lib/actions/subscription-actions';
import { Subscription } from '@/lib/stripe-client';
import { FiLock, FiLoader } from 'react-icons/fi';
import Link from 'next/link';
import { StripeSyncService } from '@/lib/services/stripe-sync-service';
import { useSession } from 'next-auth/react';

interface SubscriptionGuardProps {
  children: ReactNode;
  requirePremium?: boolean;
  fallbackUrl?: string;
}

export default function SubscriptionGuard({ 
  children, 
  requirePremium = false,
  fallbackUrl = '/dashboard/subscription' 
}: SubscriptionGuardProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function checkSubscription() {
      try {
        setIsLoading(true);
        
        if (!session?.user?.email) {
          setError('You must be logged in to access this feature');
          return;
        }

        // Check if this is a Google user (by email domain or profile picture)
        const isGoogleUser = (session.user?.email?.includes('gmail.com') || 
                             session.user?.image?.includes('googleusercontent.com')) ?? false;
        
        if (isGoogleUser) {
          console.log('Detected Google user, using direct validation method');
          // Use the direct validation method for Google users
          const validation = await StripeSyncService.validateAndRegisterGoogleUser(session.user.email);
          
          if (validation.isValid) {
            const subscriptionData: Subscription = {
              id: 'google_validated',
              type: validation.subscriptionType || 'Standard',
              status: 'active',
              startDate: new Date().toISOString(),
              currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // Approximate
              stripeCustomerId: 'google_user',
              stripeSubscriptionId: 'google_validated',
              stripePriceId: validation.subscriptionType === 'Premium' 
                ? process.env.STRIPE_PREMIUM_PRICE_ID ?? ''
                : process.env.STRIPE_STANDARD_PRICE_ID ?? ''
            };
            
            setSubscription(subscriptionData);
            
            if (requirePremium && subscriptionData.type !== 'Premium') {
              setError('This feature requires a Premium subscription');
            } else {
              setError(null);
            }
            return;
          }
        }

        // First, try to get from Neo4j through server action
        try {
          const sub = await getCurrentUserSubscription();
          if (sub && (sub.status === 'active' || sub.status === 'trialing')) {
            setSubscription(sub);
            if (requirePremium && sub.type !== 'Premium') {
              setError('This feature requires a Premium subscription');
            } else {
              setError(null);
            }
            return;
          }
        } catch (error) {
          console.warn('Error fetching subscription from Neo4j:', error);
          // Continue to Stripe validation
        }

        // Try Stripe validation
        try {
          const validation = await StripeSyncService.validateSubscription(session.user.email);
          
          if (validation.isValid) {
            // Create a subscription object from the validation result
            const subscriptionData: Subscription = {
              id: 'stripe_validated',
              type: validation.subscriptionType || 'Standard',
              status: 'active',
              startDate: new Date().toISOString(),
              currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // Approximate
              stripeCustomerId: validation.customerId || '',
              stripeSubscriptionId: 'stripe_validated',
              stripePriceId: validation.priceId || ''
            };
            
            setSubscription(subscriptionData);
            
            if (requirePremium && subscriptionData.type !== 'Premium') {
              setError('This feature requires a Premium subscription');
            } else {
              setError(null);
            }
            return;
          }
        } catch (error) {
          console.warn('Error validating with Stripe directly:', error);
        }

        // Final attempt - use our new sync method to ensure data consistency
        try {
          // Import dynamically to avoid circular dependencies
          const { SubscriptionService } = await import('@/lib/services/subscription-service');
          const syncedSubscription = await SubscriptionService.synchronizeSubscriptionFromStripe(session.user.email);
          
          if (syncedSubscription && (syncedSubscription.status === 'active' || syncedSubscription.status === 'trialing')) {
            const subscriptionData: Subscription = {
              id: syncedSubscription.id,
              type: syncedSubscription.type,
              status: syncedSubscription.status,
              startDate: syncedSubscription.startDate,
              currentPeriodEnd: syncedSubscription.endDate,
              stripeCustomerId: syncedSubscription.stripeCustomerId,
              stripeSubscriptionId: syncedSubscription.stripeSubscriptionId,
              stripePriceId: syncedSubscription.stripePriceId
            };
            
            setSubscription(subscriptionData);
            
            if (requirePremium && subscriptionData.type !== 'Premium') {
              setError('This feature requires a Premium subscription');
            } else {
              setError(null);
            }
            return;
          }
        } catch (error) {
          console.error('Error in final subscription sync attempt:', error);
        }

        // If all validations fail, show error
        setError('This feature requires an active subscription');
      } catch (err) {
        console.error('Error fetching subscription:', err);
        setError('Unable to verify your subscription. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    }

    // Set a timeout to prevent long loading times
    const timeoutId = setTimeout(() => {
      if (isLoading) {
        setIsLoading(false);
        setError('Subscription verification timed out. Please try again.');
      }
    }, 5000); // Increase timeout to 5 seconds for more validation steps

    checkSubscription();
    
    return () => clearTimeout(timeoutId);
  }, [session, requirePremium]);

  if (isLoading) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="animate-spin h-10 w-10 mx-auto text-purple-500 mb-4" />
          <p className="text-gray-400">Verifying your subscription...</p>
        </div>
      </div>
    );
  }

  // If there's an error and premium is required, show upgrade prompt
  if (error && requirePremium) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="max-w-md p-6 bg-gray-900 rounded-xl border border-gray-800 text-center">
          <div className="mx-auto w-16 h-16 bg-purple-900/30 rounded-full flex items-center justify-center mb-4">
            <FiLock className="h-8 w-8 text-purple-400" />
          </div>
          <h2 className="text-xl font-bold text-white mb-2">Premium Feature</h2>
          <p className="text-gray-400 mb-6">{error}</p>
          
          <div className="space-y-3">
            <Link 
              href={fallbackUrl}
              className="block w-full py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-md text-white transition-colors"
            >
              Upgrade to Premium
            </Link>
            
            <button 
              onClick={() => router.back()}
              className="block w-full py-2 px-4 bg-gray-800 hover:bg-gray-700 rounded-md text-white transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If all checks pass or no premium required, render children
  return <>{children}</>;
} 