'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Loa<PERSON>, FiStar, FiX} from 'react-icons/fi';
import { SUBSCRIPTION_PLANS, SUBSCRIPTION_TYPES, fetchPriceIDs, updatePlanPriceIds} from '@/lib/stripe-client';
import { cancelUserSubscription, getCurrentUserSubscription } from '@/lib/actions/subscription-actions';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';
import { useSubscriptionTranslations } from '@/hooks/useTranslations';


interface SubscriptionManagerProps {
  readonly userId: string;
  readonly currentPlan?: string;
}

export default function SubscriptionManager({currentPlan }: SubscriptionManagerProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const t = useSubscriptionTranslations();
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [pricesLoaded, setPricesLoaded] = useState(false);
  const [priceError, setPriceError] = useState(false);
  const [validatedPlan, setValidatedPlan] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(true);
  
  // Fetch price IDs when component mounts
  useEffect(() => {
    async function loadPriceIDs() {
      try {
        const success = await fetchPriceIDs();
        if (success) {
          // Update the plan price IDs
          updatePlanPriceIds();
          setPricesLoaded(true);
        } else {
          setPriceError(true);
        }
      } catch (error) {
        console.error('Error loading pricing:', error);
        setPriceError(true);
      }
    }
    
    loadPriceIDs();
  }, []);
  
  // Validate subscription from both Neo4j and Stripe
  useEffect(() => {
    async function validateSubscription() {
      setIsValidating(true);
      try {
        // Skip validation if no session
        if (!session?.user?.email) {
          setValidatedPlan(currentPlan ?? null);
          setIsValidating(false);
          return;
        }
        
        // First try to get subscription from server action (this includes both Neo4j and Stripe validation)
        const sub = await getCurrentUserSubscription();
        
        if (sub && (sub.status === 'active' || sub.status === 'trialing')) {
          // Use subscription data from server action
          setValidatedPlan(sub.type);
          console.log(`Validated subscription plan: ${sub.type}`);
        } else {
          // Fallback to current plan or null
          setValidatedPlan(currentPlan ?? null);
          console.log(`Using current plan: ${currentPlan ?? 'none'}`);
        }
      } catch (error) {
        console.error('Error validating subscription:', error);
        // Fallback to current plan
        setValidatedPlan(currentPlan ?? null);
      } finally {
        setIsValidating(false);
      }
    }
    
    validateSubscription();
  }, [currentPlan, session]);
  
  const handleSubscribe = async (planId: string) => {
    try {
      setIsLoading(planId);
      
      // Find the selected plan
      const selectedPlan = SUBSCRIPTION_PLANS.find(plan => plan.id === planId);
      
      if (!selectedPlan) {
        throw new Error(t('subscriptionManager.invalidPlan'));
      }
      
      // Get price ID from the plan
      const priceId = selectedPlan.priceId;
      
      if (!priceId) {
        throw new Error(t('subscriptionManager.priceNotConfigured'));
      }
      
      console.log(`Subscribing to plan ${planId} with price ID ${priceId}`);
      
      // Create checkout session
      const response = await fetch('/api/payments/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          planType: selectedPlan.planType === SUBSCRIPTION_TYPES.STANDARD ? 'Standard' : 'Premium'
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error ?? t('subscriptionManager.failedToCreateCheckout'));
      }

      const { url } = await response.json();
      
      if (!url) {
        throw new Error(t('subscriptionManager.noCheckoutUrl'));
      }

      // Redirect to Stripe checkout
      window.location.href = url;
      
    } catch (error) {
      console.error('Error starting subscription:', error);
      toast.error(t('subscriptionManager.failedToSubscribe'));
      setIsLoading(null);
    }
  };
  
  const handleCancelSubscription = async () => {
    try {
      setIsLoading('cancel');
      
      const result = await cancelUserSubscription();
      
      if (result.success) {
        toast.success(t('subscriptionManager.subscriptionCanceled'));
        router.refresh();
      } else {
        throw new Error(t('subscriptionManager.failedToCancelInternal'));
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error(t('subscriptionManager.failedToCancel'));
    } finally {
      setIsLoading(null);
      setShowCancelConfirm(false);
    }
  };
  
  // Use validated plan instead of directly using currentPlan
  const effectivePlan = validatedPlan ?? currentPlan;
  
  if (isValidating) {
    return (
      <div className="flex items-center justify-center p-12">
        <FiLoader className="w-8 h-8 animate-spin text-[#417ef7]" />
        <span className="ml-2 text-gray-300">{t('subscriptionManager.validatingSubscription')}</span>
      </div>
    );
  }
  
  if (priceError) {
    return (
      <div className="p-4 bg-red-900/20 border border-red-800 rounded-lg">
        <h3 className="text-lg font-medium text-white mb-2">{t('subscriptionManager.paymentConfigError')}</h3>
        <p className="text-red-300 mb-4">
          {t('subscriptionManager.errorLoadingPrices')}
        </p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          {t('subscriptionManager.refreshPage')}
        </button>
      </div>
    );
  }
  
  if (!pricesLoaded) {
    return (
      <div className="flex items-center justify-center p-12">
        <FiLoader className="w-8 h-8 animate-spin text-[#141493]" />
        <span className="ml-2 text-gray-300">{t('subscriptionManager.loadingOptions')}</span>
      </div>
    );
  }
  
  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {SUBSCRIPTION_PLANS.map((plan) => {
          // Use validated plan for checking if plan is active
          const isPlanActive = effectivePlan?.toLowerCase() === plan.planType;
          
          return (
            <div 
              key={plan.id} 
              className={`rounded-xl p-6 relative ${
                plan.recommended 
                  ? 'bg-[#141493] border border-[#417ef7]' 
                  : 'bg-[#111] border border-gray-700'
              }`}
            >
              {plan.recommended && (
                <div className="absolute top-0 right-6 transform -translate-y-1/2 bg-[#417ef7] text-white text-xs px-3 py-1 rounded-full flex items-center">
                  <FiStar className="mr-1" />
                  {t('subscriptionManager.recommended')}
                </div>
              )}
              
              <h3 className="text-xl font-semibold text-white mb-2">{plan.name}</h3>
              <p className="text-3xl font-bold text-white mb-4">{plan.price}</p>
              
              <ul className="space-y-2 mb-6">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <FiCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>
              
              {isPlanActive ? (
                <button
                  disabled
                  className="w-full py-2 px-4 bg-[#417ef7] text-white rounded-md cursor-not-allowed"
                >
                  {t('subscriptionManager.currentPlan')}
                </button>
              ) : (
                <div className="space-y-3">
                  <button
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={!!isLoading}
                    className={`w-full py-2 px-4 rounded-md text-white transition-colors ${
                      isLoading === plan.id
                        ? 'bg-gradient-to-r from-[#417ef7] to-[#141493] cursor-not-allowed'
                        : plan.recommended
                          ? 'bg-gradient-to-r from-[#417ef7] to-[#141493] hover:bg-[#417ef7]'
                          : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    {isLoading === plan.id ? (
                      <span className="flex items-center justify-center">
                        <FiLoader className="animate-spin mr-2" />
                        {t('subscriptionManager.processing')}
                      </span>
                    ) : (
                      <>{t('subscriptionManager.subscribe')}</>
                    )}
                  </button>
                </div>
              )}
            </div>
          );
        })}
      </div>
      
    </div>
  );
} 