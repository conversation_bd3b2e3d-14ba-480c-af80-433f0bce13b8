'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { getCurrentUserSubscription } from '@/lib/actions/subscription-actions';
import type { Subscription } from '@/lib/stripe-client';

interface PaymentStatusHandlerProps {
  onPaymentStatus: (paymentMessage: string | null, subscription: Subscription | null, isLoading: boolean) => void;
}

export default function PaymentStatusHandler({ onPaymentStatus }: PaymentStatusHandlerProps) {
  const searchParams = useSearchParams();
  const paymentStatus = searchParams?.get('payment') || null;
  const sessionId = searchParams?.get('session_id') || null;

  const [paymentMessage, setPaymentMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [subscription, setSubscription] = useState<Subscription | null>(null);

  // Function to fetch subscription status using server action
  const fetchSubscriptionStatus = async () => {
    try {
      setIsLoading(true);
      const subscriptionData = await getCurrentUserSubscription();
      setSubscription(subscriptionData);
    } catch (error) {
      console.error('Error fetching subscription status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Check payment status and session ID
  useEffect(() => {
    if (paymentStatus === 'success' && sessionId) {
      setIsLoading(true);
      setPaymentMessage('Payment successful! Loading your subscription details...');

      // Remove the query parameters from the URL without refreshing the page
      const url = new URL(window.location.href);
      url.searchParams.delete('payment');
      url.searchParams.delete('session_id');
      window.history.replaceState({}, '', url.toString());

      // Fetch subscription status
      fetchSubscriptionStatus();
    } else {
      // Fetch subscription status on initial load only if not already loading
      fetchSubscriptionStatus();
    }
  }, [paymentStatus, sessionId]);

  // Update parent component whenever state changes
  useEffect(() => {
    onPaymentStatus(paymentMessage, subscription, isLoading);
  }, [paymentMessage, subscription, isLoading, onPaymentStatus]);

  // This component doesn't render anything, it just handles the logic
  return null;
}
