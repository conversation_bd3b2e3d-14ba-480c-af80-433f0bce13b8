"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>X, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiExternalLink } from "react-icons/fi";
import { useState, useRef, useEffect } from "react";
import { GeneratedImage, AIModel, downloadImageDirectly } from "@/lib/image-utils";
import { toast } from "sonner";

interface DownloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedImage: GeneratedImage;
  selectedModel: string;
  allModels: AIModel[];
}

export default function DownloadModal({
  isOpen,
  onClose,
  selectedImage,
  selectedModel,
  allModels,
}: DownloadModalProps) {
  const [copied, setCopied] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle copy to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast.success("Copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy text");
    }
  };

  // Handle outside click
  const handleClickOutside = (e: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  // Handle ESC key
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "Escape") {
      onClose();
    }
  };

  // Effect for click outside and ESC key
  useEffect(() => {
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleKeyDown);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen]);

  // Check if image is from Replicate
  const isReplicateImage = (url: string) => {
    return url.includes('replicate.delivery') || url.includes('replicate.com') || 
           selectedImage.provider === 'replicate' || selectedModel.includes('replicate') ||
           selectedModel.toLowerCase().includes('flux') || selectedModel.toLowerCase().includes('sdxl');
  };

  // Download image directly for Replicate (bypass conversion)
  const downloadReplicateImageDirect = async (url: string, format: string) => {
    try {
      toast.info('Preparing Replicate image download...');
      
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: url,
          format: format
        }),
      });

      if (!response.ok) {
        throw new Error(`Download API failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (!data.success || !data.dataUrl) {
        throw new Error('Invalid response from download API');
      }

      // Convert data URL to blob and download
      const response2 = await fetch(data.dataUrl);
      const blob = await response2.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      
      // Generate filename based on model and timestamp
      const modelName = selectedModel.split('/').pop()?.replace(/[^a-z0-9]/gi, '-') || 'replicate';
      const timestamp = new Date().getTime();
      const extension = format === 'jpg' ? 'jpg' : 'png';
      const filename = `${modelName}-${timestamp}.${extension}`;
      
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(downloadUrl);
      
      toast.success(`Downloaded Replicate image as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Error downloading Replicate image:', error);
      toast.error('Failed to download image. Opening direct link...');
      // Fallback to direct download
      window.open(url, '_blank');
    }
  };

  // Download image in different formats
  const downloadImage = async (url: string, format: string) => {
    // Handle Replicate images differently
    if (isReplicateImage(url)) {
      await downloadReplicateImageDirect(url, format);
      return;
    }

    // Use conversion API for other images
    try {
      toast.info(`Converting image to ${format.toUpperCase()}...`);
      
      const response = await fetch('/api/convert-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: url,
          format: format,
          filename: `generated-image-${new Date().getTime()}`
        }),
      });

      if (!response.ok) {
        throw new Error(`Conversion failed: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const filename = `generated-image-${new Date().getTime()}.${format}`;
      
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(downloadUrl);
      
      toast.success(`Downloaded image as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error('Failed to convert image. Please try again.');
    }
  };

  if (!isOpen) return null;

  // Custom animation styles
  const modalAnimations = `
    @keyframes modalFadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .modal-animation {
      animation: modalFadeIn 0.2s ease-out;
    }
  `;

  return (
    <>
      <style jsx global>
        {modalAnimations}
      </style>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        <div className="fixed inset-0 bg-black/70" onClick={onClose}></div>
        <div ref={modalRef} className="bg-black z-10 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col relative modal-animation">
          <div className="flex justify-between items-center p-4 border-b border-[#222]">
            <h3 className="text-lg font-medium text-white">
              Download Options
            </h3>
            <button
              onClick={onClose}
              className="p-2 rounded-md hover:bg-[#111] text-gray-400 hover:text-white transition-colors"
            >
              <FiX size={20} />
            </button>
          </div>

          <div className="overflow-y-auto flex-1 p-4">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Image Preview */}
              <div>
                <h4 className="text-white mb-3 font-medium">Image Preview</h4>
                <div className="bg-[#111] rounded-lg overflow-hidden border border-[#222]">
                  <div className="bg-checkerboard aspect-square">
                    <img
                      src={selectedImage.url}
                      alt="Generated image"
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>
                <div className="mt-3">
                  <p className="text-xs text-gray-400">
                    Model: {selectedModel.split('/').pop()}
                  </p>
                  <p className="text-xs text-gray-400">
                    Size: {selectedImage.width || 'unknown'}×
                    {selectedImage.height || 'unknown'}
                  </p>
                </div>
              </div>

              {/* Download Options */}
              <div>
                <h4 className="text-white mb-3 font-medium">Options</h4>
                <div className="space-y-4">
                  {/* Image URL */}
                  <div className="bg-[#111] p-3 rounded-lg border border-[#222]">
                    <label className="block text-sm text-gray-400 mb-1">
                      Direct Image URL
                    </label>
                    <div className="flex">
                      <input
                        type="text"
                        value={selectedImage.url}
                        readOnly
                        className="w-full bg-[#111] border border-[#333] text-gray-300 rounded-l-md px-3 py-2 text-sm"
                      />
                      <button
                        onClick={() => copyToClipboard(selectedImage.url)}
                        className="bg-[#333] hover:bg-[#444] text-white rounded-r-md px-3 flex items-center border border-[#333] border-l-0"
                      >
                        {copied ? <FiCheck /> : <FiCopy />}
                      </button>
                    </div>
                  </div>

                  {/* Download Options */}
                  <div className="bg-[#111] p-3 rounded-lg border border-[#222]">
                    <label className="block text-sm text-gray-400 mb-1">
                      Download Image
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => downloadImage(selectedImage.url, "png")}
                        className="w-full bg-[#111] hover:bg-[#181818] text-gray-300 rounded-md p-2 text-sm flex items-center justify-center"
                      >
                        <FiDownload className="mr-1.5" /> PNG
                      </button>
                      <button
                        onClick={() => downloadImage(selectedImage.url, "jpg")}
                        className="w-full bg-[#111] hover:bg-[#181818] text-gray-300 rounded-md p-2 text-sm flex items-center justify-center"
                      >
                        <FiDownload className="mr-1.5" /> JPG
                      </button>
                      <button
                        onClick={() => downloadImage(selectedImage.url, "webp")}
                        className="w-full bg-[#111] hover:bg-[#181818] text-gray-300 rounded-md p-2 text-sm flex items-center justify-center"
                      >
                        <FiDownload className="mr-1.5" /> WebP
                      </button>
                      {selectedModel.includes('vector') && (
                        <button
                          onClick={() => downloadImage(selectedImage.url, "svg")}
                          className="w-full bg-[#111] hover:bg-[#181818] text-gray-300 rounded-md p-2 text-sm flex items-center justify-center"
                        >
                          <FiDownload className="mr-1.5" /> SVG
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Open in new tab */}
                  <div className="bg-[#111] p-3 rounded-lg border border-[#222]">
                    <label className="block text-sm text-gray-400 mb-1">
                      Other Options
                    </label>
                    <button
                      onClick={() => window.open(selectedImage.url, "_blank")}
                      className="w-full bg-[#111] hover:bg-[#181818] text-gray-300 rounded-md p-2 text-sm flex items-center justify-center"
                    >
                      <FiExternalLink className="mr-1.5" /> Open in New Tab
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Prompt Information */}
            {selectedImage.prompt && (
              <div className="mt-6">
                <h4 className="text-white mb-3 font-medium">Prompt</h4>
                <div className="bg-[#111] p-3 rounded-lg border border-[#222]">
                  <p className="text-sm text-gray-300 mb-2">{selectedImage.prompt}</p>
                  <button
                    onClick={() => copyToClipboard(selectedImage.prompt || "")}
                    className="text-xs bg-[#181818] hover:bg-[#222] text-gray-300 rounded-md px-2 py-1 flex items-center w-auto"
                  >
                    {copied ? <FiCheck className="mr-1" /> : <FiCopy className="mr-1" />}
                    Copy Prompt
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="p-4 border-t border-[#222] flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-[#181818] hover:bg-[#222] text-gray-300 rounded-md"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </>
  );
}