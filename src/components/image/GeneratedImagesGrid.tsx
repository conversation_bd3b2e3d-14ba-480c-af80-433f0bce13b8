"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { FiImage, FiDownload, FiShare2, FiSave, <PERSON>L<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiExternalLink } from "react-icons/fi";
import { GeneratedImage } from "@/lib/image-utils";
import DownloadModal from "./DownloadModal";
import { getAllModels } from "@/lib/models-data";
import { toast } from "sonner";

interface GeneratedImagesGridProps {
  images: GeneratedImage[];
  isGenerating: boolean;
  numImages: number;
  selectedModel: string;
  onSaveImage: (imageUrl: string, index: number) => Promise<string | null>;
  isSaving: boolean;
  onSelectImage?: (image: GeneratedImage) => void;
  onDownloadImage?: (url: string) => Promise<void>;
}

const GeneratedImagesGrid = ({
  images,
  isGenerating,
  numImages,
  selectedModel,
  onSaveImage,
  isSaving,
  onSelectImage,
  onDownloadImage
}: GeneratedImagesGridProps) => {
  const [selectedImagesCount, setSelectedImagesCount] = useState<number[]>([]);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<GeneratedImage | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const allModels = getAllModels();
  
  // Handle download modal open
  const openDownloadModal = (image: GeneratedImage) => {
    setSelectedImage(image);
    setShowDownloadModal(true);
  };
  
  // Add toggleImageSelection function to select/deselect images
  const toggleImageSelection = (index: number) => {
    setSelectedImagesCount((prev) => {
      if (prev.includes(index)) {
        return prev.filter((i) => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };
  
  /**
   * Convert a blob to data URL
   */
  const blobToDataURL = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  };

  /**
   * Process a single image for saving
   */
  const processSingleImageForSave = async (imageUrl: string): Promise<string> => {
    console.log(`Processing image for save: ${imageUrl.substring(0, 50)}...`);
    
    // If it's already a data URL, return it directly
    if (imageUrl.startsWith('data:')) {
      console.log('Image is already a data URL');
      return imageUrl;
    }
    
    try {
      // First try the download-image API (server-side approach)
      console.log('Attempting to convert via server API...');
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imageUrl })
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.dataUrl) {
          console.log('Successfully converted image via server API');
          return result.dataUrl;
        }
        console.warn('Server API response was OK but data was invalid:', result);
      } else {
        console.warn(`Server API returned error: ${response.status} ${response.statusText}`);
      }
      
      // Fallback to client-side fetching
      console.log('Falling back to direct client-side fetch...');
      const fetchResponse = await fetch(imageUrl);
      if (!fetchResponse.ok) {
        console.error(`Failed to fetch image: ${fetchResponse.status} ${fetchResponse.statusText}`);
        throw new Error(`Failed to fetch image from ${imageUrl}`);
      }
      
      const blob = await fetchResponse.blob();
      console.log(`Successfully fetched image as blob: type=${blob.type}, size=${blob.size}bytes`);
      
      return await blobToDataURL(blob);
    } catch (error) {
      console.error('Error processing image for save:', error);
      // If all conversion attempts fail, return the original URL as fallback
      console.log('All conversion attempts failed, using original URL as fallback');
      return imageUrl;
    }
  };

  /**
   * Save a single image directly
   */
  const saveSingleImage = async (imageUrl: string, index: number) => {
    const button = document.querySelector(`button[data-url="${imageUrl}"]`) as HTMLButtonElement;
    if (button) {
      button.disabled = true;
    }
    console.log(`Saving single image: ${imageUrl.substring(0, 50)}...`);
    
    try {
      // Process the image before saving
      const processedImageUrl = await processSingleImageForSave(imageUrl);
      console.log('Image processed successfully, attempting to save');
      
      const saveResponse = await onSaveImage(processedImageUrl, index);
      console.log('Save completed with response:', saveResponse);
      
      toast("Success", {
        description: "Image saved successfully",
      });
    } catch (error) {
      console.error('Error saving image:', error);
      toast("Error", {
        description: "Failed to save image. Please try again.",
      });
    } finally {
      if (button) {
        button.disabled = false;
      }
    }
  };

  /**
   * Save all selected images
   */
  const saveSelectedImages = async () => {
    console.log(`Attempting to save ${selectedImagesCount.length} selected images`);
    
    if (selectedImagesCount.length === 0) {
      toast("No images selected", {
        description: "Please select at least one image to save",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const savedImages = [];
      let failedCount = 0;
      
      // Process images sequentially to avoid overwhelming the server
      for (const index of selectedImagesCount) {
        try {
          console.log(`Processing selected image: ${images[index].url.substring(0, 50)}...`);
          const processedImageUrl = await processSingleImageForSave(images[index].url);
          
          console.log('Saving processed image...');
          const saveResponse = await onSaveImage(processedImageUrl, index);
          console.log('Save completed:', saveResponse);
          
          savedImages.push(images[index].url);
        } catch (error) {
          console.error(`Failed to save image: ${images[index].url.substring(0, 50)}...`, error);
          failedCount++;
        }
      }
      
      const successCount = savedImages.length;
      console.log(`Saved ${successCount} images, ${failedCount} failed`);
      
      if (successCount > 0) {
        toast("Success", {
          description: `Saved ${successCount} ${successCount === 1 ? "image" : "images"} ${failedCount > 0 ? `(${failedCount} failed)` : ""}`,
        });
        
        if (failedCount > 0) {
          toast("Warning", {
            description: `${failedCount} ${failedCount === 1 ? "image" : "images"} failed to save`,
          });
        }
        
        setSelectedImagesCount([]);
      } else {
        toast("Error", {
          description: "Failed to save any images. Please try again.",
        });
      }
    } catch (error) {
      console.error('Error in saveSelectedImages:', error);
      toast("Error", {
        description: "An unexpected error occurred while saving images",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle saving a single image
  const handleSaveImage = async (imageUrl: string, index: number) => {
    if (isSaving) return;
    await onSaveImage(imageUrl, index);
  };

  return (
    <div className="bg-[#111] p-4 rounded-xl border border-[#222]">
      <div className="grid grid-cols-2 gap-3">
        {images.map((image, index) => (
          <div key={image.id} className="relative group">
            <div 
              className={`bg-[#1a1a1a] rounded-lg overflow-hidden aspect-square ${onSelectImage ? 'cursor-pointer' : ''}`}
              onClick={() => onSelectImage && onSelectImage(image)}
            >
              <img
                src={image.url}
                alt={image.prompt || 'Generated image'}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </div>
            
            {/* Hover overlay with actions */}
            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 rounded-lg transition-opacity flex items-center justify-center gap-2">
              {onDownloadImage && (
                <button
                  onClick={() => onDownloadImage(image.url)}
                  className="p-2 rounded-full bg-[#333] hover:bg-[#444] text-white transition-colors"
                  title="Download image"
                >
                  <FiDownload size={16} />
                </button>
              )}
              <button
                onClick={() => openDownloadModal(image)}
                className="p-2 rounded-full bg-[#333] hover:bg-[#444] text-white transition-colors"
                title="Open download options"
              >
                <FiExternalLink size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {/* Download Modal */}
      {showDownloadModal && selectedImage && (
        <DownloadModal
          isOpen={showDownloadModal}
          onClose={() => setShowDownloadModal(false)}
          selectedImage={selectedImage}
          selectedModel={selectedModel}
          allModels={allModels}
        />
      )}
    </div>
  );
};

export default GeneratedImagesGrid; 