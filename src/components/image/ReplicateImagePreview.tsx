"use client";

import React, { useState } from 'react';
import { FiImage, FiLoader, FiDownload, FiCopy, FiExternalLink } from "react-icons/fi";
import { GeneratedImage } from "@/lib/image-utils";
import { toast } from "sonner";
import DownloadModal from "./DownloadModal";
import { getAllModels } from "@/lib/models-data";

interface ReplicateImagePreviewProps {
  image: GeneratedImage | null;
  isGenerating: boolean;
  onCopyPrompt?: (text: string) => Promise<void>;
  onDownloadImage?: (url: string) => Promise<void>;
}

const ReplicateImagePreview = ({ 
  image, 
  isGenerating, 
  onCopyPrompt, 
  onDownloadImage 
}: ReplicateImagePreviewProps) => {
  const [isImageLoading, setIsImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [allModels, setAllModels] = useState<any[]>([]);

  // Load models when component mounts
  React.useEffect(() => {
    async function loadModels() {
      const models = await getAllModels();
      setAllModels(models);
    }
    loadModels();
  }, []);

  // Handle image load success
  const handleImageLoad = () => {
    setIsImageLoading(false);
    setImageError(false);
  };

  // Handle image load error
  const handleImageError = () => {
    setIsImageLoading(false);
    setImageError(true);
    toast.error("Failed to load image from Replicate");
  };

  // Check if image is from Replicate
  const isReplicateImage = (url: string) => {
    return url.includes('replicate.delivery') || url.includes('replicate.com') || 
           image?.provider === 'replicate' || image?.model?.includes('replicate') ||
           image?.model?.toLowerCase().includes('flux') || image?.model?.toLowerCase().includes('sdxl');
  };

  // Download Replicate image directly
  const downloadReplicateImageDirect = async (url: string) => {
    try {
      toast.info('Preparing Replicate image download...');
      
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: url,
          format: 'png'
        }),
      });

      if (!response.ok) {
        throw new Error(`Download API failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (!data.success || !data.dataUrl) {
        throw new Error('Invalid response from download API');
      }

      // Convert data URL to blob and download
      const response2 = await fetch(data.dataUrl);
      const blob = await response2.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      
      // Generate filename based on model and timestamp
      const modelName = image?.model?.split('/').pop()?.replace(/[^a-z0-9]/gi, '-') || 'replicate';
      const timestamp = new Date().getTime();
      const filename = `${modelName}-${timestamp}.png`;
      
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(downloadUrl);
      
      toast.success('Downloaded Replicate image as PNG');
    } catch (error) {
      console.error('Error downloading Replicate image:', error);
      toast.error('Failed to download Replicate image. Opening in new tab...');
      // Fallback to opening in new tab
      window.open(url, '_blank');
    }
  };

  // Handle download button click
  const handleDownloadClick = async (url: string) => {
    if (isReplicateImage(url)) {
      await downloadReplicateImageDirect(url);
    } else if (onDownloadImage) {
      await onDownloadImage(url);
    }
  };

  // Open download modal instead of new tab
  const openDownloadModal = () => {
    if (!image) return;
    setShowDownloadModal(true);
  };

  // Retries loading the image
  const retryLoadImage = () => {
    setIsImageLoading(true);
    setImageError(false);
    // Force image reload by appending a cache-busting query parameter
    if (image) {
      const imgElement = document.getElementById('replicate-preview-img') as HTMLImageElement;
      if (imgElement) {
        imgElement.src = `${image.url}?t=${Date.now()}`;
      }
    }
  };

  // Check if there's a direct output URL from Replicate
  const hasRawOutput = image?.raw_response && typeof image.raw_response.output === 'string';
  const rawOutputUrl = hasRawOutput ? image.raw_response.output : null;
  
  // Function to try the direct output URL if main image fails
  const tryRawOutputUrl = () => {
    if (!rawOutputUrl) return;
    
    setIsImageLoading(true);
    setImageError(false);
    
    const imgElement = document.getElementById('replicate-preview-img') as HTMLImageElement;
    if (imgElement) {
      imgElement.src = rawOutputUrl;
    }
    
    toast.info("Trying to load direct Replicate URL");
  };

  return (
    <>
      <div className="bg-[#111] rounded-xl border border-[#222] p-4">
        {image ? (
          <>
            <div className="relative bg-checkerboard rounded-lg overflow-hidden">
              {isImageLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/70 z-10">
                  <FiLoader className="animate-spin text-white" size={24} />
                </div>
              )}
              {imageError ? (
                <div className="aspect-video flex flex-col items-center justify-center bg-[#1a1a1a] p-8">
                  <FiImage className="text-red-500 text-4xl mb-3" />
                  <p className="text-gray-300 text-center mb-3">Failed to load image from Replicate</p>
                  <div className="flex gap-2">
                    <button
                      className="px-3 py-1.5 bg-white text-black hover:bg-gray-200 rounded text-sm"
                      onClick={retryLoadImage}
                    >
                      Retry Loading
                    </button>
                    {rawOutputUrl && (
                      <button
                        className="px-3 py-1.5 bg-[#333] hover:bg-[#444] rounded text-white text-sm"
                        onClick={tryRawOutputUrl}
                      >
                        Try Direct URL
                      </button>
                    )}
                  </div>
                </div>
              ) : (
                <img
                  id="replicate-preview-img"
                  src={image.url}
                  alt={image.prompt || "Generated image"}
                  className="w-full h-auto object-contain"
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              )}
            </div>

            <div className="flex justify-end mt-2 gap-2">
              {image.prompt && onCopyPrompt && (
                <button
                  onClick={() => image.prompt && onCopyPrompt(image.prompt)}
                  className="p-1.5 rounded-md bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-xs flex items-center gap-1"
                  title="Copy prompt"
                >
                  <FiCopy size={14} /> Copy prompt
                </button>
              )}
              <button
                onClick={() => handleDownloadClick(image.url)}
                className="p-1.5 rounded-md bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-xs flex items-center gap-1"
                title="Download image"
              >
                <FiDownload size={14} /> Download
              </button>
              <button
                onClick={openDownloadModal}
                className="p-1.5 rounded-md bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-xs flex items-center gap-1"
                title="Open download options"
              >
                <FiExternalLink size={14} /> Options
              </button>
            </div>
          </>
        ) : null}
      </div>
      {showDownloadModal && image && (
        <DownloadModal
          isOpen={showDownloadModal}
          onClose={() => setShowDownloadModal(false)}
          selectedImage={image}
          selectedModel={image.model || ''}
          allModels={allModels}
        />
      )}
    </>
  );
};

export default ReplicateImagePreview;