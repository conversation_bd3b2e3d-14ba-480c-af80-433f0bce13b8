"use client";

import React, { useState } from "react";
import { FiImage, FiDownload, FiSave, <PERSON>Loader, FiExternalLink } from "react-icons/fi";
import { GeneratedImage } from "@/lib/image-utils";
import { toast } from "sonner";

interface ReplicateImagesGridProps {
  images: GeneratedImage[];
  isGenerating: boolean;
  numImages: number;
  selectedModel: string;
  modelProvider: string;
  isSaving: boolean;
  onSelectImage?: (image: GeneratedImage) => void;
  onDownloadImage?: (url: string) => Promise<void>;
}

const ReplicateImagesGrid = ({
  images,
  onSelectImage,
  onDownloadImage
}: ReplicateImagesGridProps) => {
  const [loadingStates, setLoadingStates] = useState<{[key: string]: boolean}>({});

  // Track image loading state
  const handleImageLoad = (imageId: number) => {
    setLoadingStates(prev => ({...prev, [imageId]: false}));
  };

  const handleImageError = (imageId: number) => {
    setLoadingStates(prev => ({...prev, [imageId]: false}));
    toast.error("Failed to load image");
  };

  // Start loading when image is rendered
  const startLoading = (imageId: number) => {
    setLoadingStates(prev => ({...prev, [imageId]: true}));
  };

  // Handle opening the image in a new tab
  const openImageInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div className="bg-[#111] p-4 rounded-xl border border-[#222]">
      <div className="grid grid-cols-2 gap-3">
        {images.map((image, index) => (
          <div key={image.id} className="relative group">
            <div 
              className={`bg-[#1a1a1a] rounded-lg overflow-hidden aspect-square ${onSelectImage ? 'cursor-pointer' : ''}`}
              onClick={() => onSelectImage && onSelectImage(image)}
              onMouseEnter={() => startLoading(image.id)}
            >
              {loadingStates[image.id] && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/70 z-10">
                  <FiLoader className="animate-spin text-white" size={24} />
                </div>
              )}
              <img
                src={image.url}
                alt={image.prompt || 'Generated image'}
                className="w-full h-full object-cover"
                onLoad={() => handleImageLoad(image.id)}
                onError={() => handleImageError(image.id)}
                loading="lazy"
              />
            </div>
            
            {/* Hover overlay with actions */}
            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 rounded-lg transition-opacity flex items-center justify-center gap-2">
              {onDownloadImage && (
                <button
                  onClick={() => onDownloadImage(image.url)}
                  className="p-2 rounded-full bg-[#333] hover:bg-[#444] text-white transition-colors"
                  title="Download image"
                >
                  <FiDownload size={16} />
                </button>
              )}
              <button
                onClick={() => openImageInNewTab(image.url)}
                className="p-2 rounded-full bg-[#333] hover:bg-[#444] text-white transition-colors"
                title="Open in new tab"
              >
                <FiExternalLink size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReplicateImagesGrid; 