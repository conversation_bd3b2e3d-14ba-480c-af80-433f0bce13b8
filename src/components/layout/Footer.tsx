"use client";

import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ram, FiLinkedin } from 'react-icons/fi';
import Image from 'next/image';
import { useFooterTranslations } from '@/hooks/useTranslations';
import { useLocale, createLocaleUrl } from '@/hooks/useLocale';

export default function Footer() {
  const footer = useFooterTranslations();
  const locale = useLocale();

  return (
    <footer className="bg-black/50 backdrop-blur-lg border-t border-white/10 pt-12 pb-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-1">
            <Link href={createLocaleUrl('/', locale)} className="flex items-center space-x-2">
                            <span className="w-auto h-auto padding-2">
                                <Image src="/Logo Web.webp" alt="AstroStudio AI" width={200} height={70} className="mb-4" />
                            </span>
            </Link>
            <p className="mt-4 text-gray-400 text-sm">
              {footer('description')}
            </p>
            <p className="mt-4 text-gray-400 text-sm">
              {footer('location')}
            </p>
            <div className="flex mt-6 space-x-4">
              <a href="https://www.instagram.com/astrostudioai/" className="text-gray-400 hover:text-white transition-colors">
                <FiInstagram className="h-5 w-5" />
              </a>
            </div>
          </div>

          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4">{footer('sections.tools')}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={createLocaleUrl('/#features', locale)} className="text-gray-400 hover:text-white transition-colors">{footer('links.features')}</Link>
              </li>
              <li>
                <Link href={createLocaleUrl('/#pricing', locale)} className="text-gray-400 hover:text-white transition-colors">{footer('links.pricing')}</Link>
              </li>
              <li>
                <Link href={createLocaleUrl('/signup', locale)} className="text-gray-400 hover:text-white transition-colors">{footer('links.signup')}</Link>
              </li>
              <li>
                <Link href={createLocaleUrl('/login', locale)} className="text-gray-400 hover:text-white transition-colors">{footer('links.login')}</Link>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4">{footer('sections.resources')}</h3>
            <ul className="space-y-2">
              <li>
                <Link href="https://www.digitalwavesystems.com.co/blogs" className="text-gray-400 hover:text-white transition-colors">{footer('links.blog')}</Link>
              </li>
              <li>
                <Link href={createLocaleUrl('/docs', locale)} className="text-gray-400 hover:text-white transition-colors">{footer('links.documentation')}</Link>
              </li>
              <li>
                <Link href="https://www.digitalwavesystems.com.co/contact" className="text-gray-400 hover:text-white transition-colors">{footer('links.support')}</Link>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4">{footer('sections.company')}</h3>
            <ul className="space-y-2">
              <li>
                <Link href="https://www.digitalwavesystems.com.co/about" className="text-gray-400 hover:text-white transition-colors">{footer('links.aboutUs')}</Link>
              </li>
              <li>
                <Link href="https://www.digitalwavesystems.com.co/contact" className="text-gray-400 hover:text-white transition-colors">{footer('links.contact')}</Link>
              </li>
              <li>
                <Link href={createLocaleUrl('/privacy', locale)} className="text-gray-400 hover:text-white transition-colors">{footer('links.privacyPolicy')}</Link>
              </li>
              <li>
                <Link href={createLocaleUrl('/terms', locale)} className="text-gray-400 hover:text-white transition-colors">{footer('links.termsOfService')}</Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm">
          <p>&copy; {new Date().getFullYear()} {footer('copyright')}</p>
        </div>
      </div>
    </footer>
  );
} 