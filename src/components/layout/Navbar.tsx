"use client";

import Link from 'next/link';
import { FiMenu, FiX, FiUser } from 'react-icons/fi';
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useNavigationTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';
import { useLocale, createLocaleUrl } from '@/hooks/useLocale';

export default function Navbar() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const { data: session, status } = useSession();
    const router = useRouter();
    const [userEmail, setUserEmail] = useState<string | null>(null);
    const nav = useNavigationTranslations();
    const common = useCommonTranslations();
    const locale = useLocale();

    // Fetch user info if session exists
    useEffect(() => {
        const fetchUserInfo = async () => {
            if (status === 'authenticated' && session?.user?.email) {
                setUserEmail(session.user.email);
            } else if (status === 'authenticated') {
                // If session exists but email is not directly available, try to fetch from API
                try {
                    const response = await fetch('/api/auth/me');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.user && data.user.email) {
                            setUserEmail(data.user.email);
                        }
                    }
                } catch (error) {
                    console.error('Error fetching user data:', error);
                }
            }
        };

        fetchUserInfo();
    }, [session, status]);

    // Handle subscription link click based on auth status
    const handleSubscriptionClick = (e: React.MouseEvent) => {
        e.preventDefault();
        if (status === 'authenticated') {
            router.push(createLocaleUrl('/dashboard/subscription', locale));
        } else {
            router.push(createLocaleUrl('/signup', locale));
        }
    };

    return (
        <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-lg border-b border-white/10">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16 items-center">
                    {/* Logo */}
                    <div className="flex-shrink-0 flex items-center">
                        <Link href={createLocaleUrl('/', locale)} className="flex items-center">
                            <div className="relative">
                                <Image
                                    src="/Logo Web.webp"
                                    alt="AstroStudio AI"
                                    width={160}
                                    height={56}
                                    className="h-10 w-auto object-contain"
                                    priority
                                />
                            </div>
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="hidden md:flex items-center space-x-6 lg:space-x-8">
                        <Link href={createLocaleUrl('/#features', locale)} className="text-gray-300 hover:text-white transition-colors">
                            {nav('features')}
                        </Link>
                        <Link href={createLocaleUrl('/#examples', locale)} className="text-gray-300 hover:text-white transition-colors">
                            Examples
                        </Link>
                        <Link href={createLocaleUrl('/#pricing', locale)} className="text-gray-300 hover:text-white transition-colors">
                           {nav('pricing')}
                        </Link>
                        <Link href={createLocaleUrl('/#contact', locale)} className="text-gray-300 hover:text-white transition-colors">
                           Contact
                        </Link>
                        <LanguageSwitcher />
                        {status === 'authenticated' ? (
                            <>
                                <div className="flex items-center text-white px-3 py-2 rounded-full border border-[#417ef7] bg-[#141493]">
                                    <FiUser className="mr-2 h-4 w-4" />
                                    <span className="text-sm truncate max-w-[120px]">{userEmail}</span>
                                </div>
                                <Link href={createLocaleUrl('/dashboard', locale)} className="btn-secondary text-white hover:text-[#417ef7] whitespace-nowrap">
                                    {nav('dashboard')}
                                </Link>
                            </>
                        ) : (
                            <>
                                <Link href={createLocaleUrl('/login', locale)} className="btn-secondary whitespace-nowrap">
                                    {common('login')}
                                </Link>
                                <Link href={createLocaleUrl('/signup', locale)} className="btn-primary whitespace-nowrap">
                                    {common('signup')}
                                </Link>
                            </>
                        )}
                    </div>

                    {/* Mobile Menu Button */}
                    <div className="md:hidden">
                        <button
                            type="button"
                            className="text-gray-300 hover:text-white"
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                        >
                            {isMenuOpen ? (
                                <FiX className="h-6 w-6" />
                            ) : (
                                <FiMenu className="h-6 w-6" />
                            )}
                        </button>
                    </div>
                </div>
            </div>

            {/* Mobile Menu */}
            {isMenuOpen && (
                <div className="md:hidden">
                    <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-900/90 backdrop-blur-lg">
                        <Link
                            href={createLocaleUrl('/#features', locale)}
                            className="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-800"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            {nav('features')}
                        </Link>
                        <Link
                            href={createLocaleUrl('/#examples', locale)}
                            className="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-800"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            Examples
                        </Link>
                        <Link href={createLocaleUrl('/#pricing', locale)}
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-800"
                            onClick={() => setIsMenuOpen(false)}>
                           {nav('pricing')}
                        </Link>
                        <Link href={createLocaleUrl('/#contact', locale)}
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-800"
                            onClick={() => setIsMenuOpen(false)}>
                           Contact
                        </Link>

                        <div className="px-3 py-2">
                            <LanguageSwitcher showLabel={false} />
                        </div>

                        {status === 'authenticated' ? (
                            <>
                                <div className="px-3 py-2 text-[#417ef7]">
                                    <FiUser className="inline mr-2" />
                                    <span className="text-sm">{userEmail}</span>
                                </div>
                                <Link
                                    href={createLocaleUrl('/dashboard', locale)}
                                    className="block px-3 py-2 rounded-md text-base font-medium bg-gray-800 text-white"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    {nav('dashboard')}
                                </Link>
                            </>
                        ) : (
                            <>
                                <Link
                                    href={createLocaleUrl('/login', locale)}
                                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-800"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    {common('login')}
                                </Link>
                                <Link
                                    href={createLocaleUrl('/signup', locale)}
                                    className="block px-3 py-2 rounded-md text-base font-medium bg-[#417ef7] text-white"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    {common('signup')}
                                </Link>
                            </>
                        )}
                    </div>
                </div>
            )}
        </nav>
    );
} 