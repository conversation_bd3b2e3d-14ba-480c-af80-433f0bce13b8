'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';

export default function PrivacyPolicy() {
  const t = useTranslations('legal.privacy');
  const [currentDate, setCurrentDate] = useState<string>('');

  useEffect(() => {
    setCurrentDate(new Date().toLocaleDateString());
  }, []);

  return (
    <main className="min-h-screen bg-black">
      <Navbar />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-sm p-8 sm:p-12 rounded-2xl border border-[#417ef7]/20 shadow-2xl">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">{t('title')}</h1>

          <div className="prose prose-invert prose-lg max-w-none">
            <p className="text-gray-300 text-lg mb-8">
              <strong>{t('effectiveDate')}</strong> {currentDate}
            </p>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.informationCollection.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.informationCollection.content')}</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  {t.raw('sections.informationCollection.list').map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
                <p>{t('sections.informationCollection.additional')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.informationUse.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.informationUse.content')}</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  {t.raw('sections.informationUse.list').map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.informationSharing.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.informationSharing.content')}</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  {t.raw('sections.informationSharing.list').map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.dataRetention.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.dataRetention.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.dataSecurity.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.dataSecurity.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.userRights.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.userRights.content')}</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  {t.raw('sections.userRights.list').map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.cookies.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.cookies.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.childrensPrivacy.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.childrensPrivacy.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.policyChanges.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.policyChanges.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.contact.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.contact.content')}</p>
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <p><strong>{t('sections.contact.email')}</strong></p>
                  <p><strong>{t('sections.contact.address')}</strong></p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
      
      <Footer />
    </main>
  );
}
