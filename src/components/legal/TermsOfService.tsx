'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';

export default function TermsOfService() {
  const t = useTranslations('legal.terms');
  const [currentDate, setCurrentDate] = useState<string>('');

  useEffect(() => {
    setCurrentDate(new Date().toLocaleDateString());
  }, []);

  return (
    <main className="min-h-screen bg-black">
      <Navbar />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-sm p-8 sm:p-12 rounded-2xl border border-[#417ef7]/20 shadow-2xl">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">{t('title')}</h1>

          <div className="prose prose-invert prose-lg max-w-none">
            <p className="text-gray-300 text-lg mb-8">
              <strong>{t('effectiveDate')}</strong> {currentDate}
            </p>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.acceptance.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.acceptance.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.description.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.description.content')}</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  {t.raw('sections.description.list').map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.userAccounts.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.userAccounts.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.payment.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
                  <p className="text-red-300 font-semibold">{t('sections.payment.importantTitle')}</p>
                  <p className="text-red-200 mt-2">{t('sections.payment.importantContent')}</p>
                  <ul className="list-disc list-inside space-y-1 ml-4 mt-2 text-red-200">
                    {t.raw('sections.payment.list').map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
                <p>{t('sections.payment.additional')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.acceptableUse.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.acceptableUse.content')}</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  {t.raw('sections.acceptableUse.list').map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.contentOwnership.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.contentOwnership.content')}</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  {t.raw('sections.contentOwnership.list').map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.disclaimers.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.disclaimers.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.limitation.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.limitation.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.termination.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.termination.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.changes.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.changes.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.governingLaw.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.governingLaw.content')}</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('sections.contact.title')}</h2>
              <div className="text-gray-300 space-y-4">
                <p>{t('sections.contact.content')}</p>
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <p><strong>{t('sections.contact.email')}</strong></p>
                  <p><strong>{t('sections.contact.address')}</strong></p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
      
      <Footer />
    </main>
  );
}
