import { useState, useRef } from 'react';
import { FiX, <PERSON><PERSON>pload, FiMic, FiPlay, FiPause, <PERSON>Loader, <PERSON>Check } from 'react-icons/fi';
import { uploadFile } from '@/lib/storage';

interface VoiceCloneModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVoiceCloned: (data: { voiceId: string; name: string; audioUrl: string }) => void;
}

export default function VoiceCloneModal({ isOpen, onClose, onVoiceCloned }: VoiceCloneModalProps) {
  const [step, setStep] = useState<'upload' | 'review' | 'processing' | 'complete'>('upload');
  const [voiceName, setVoiceName] = useState('');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState('');
  const [progress, setProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState('');
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Check if it's an audio file
      if (!file.type.startsWith('audio/')) {
        setError('Please upload an audio file (MP3, WAV, etc.)');
        return;
      }
      
      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setError('File size exceeds 10MB limit');
        return;
      }
      
      setAudioFile(file);
      setError('');
      
      // Create a preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };
  
  const togglePlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };
  
  const handleAudioEnded = () => {
    setIsPlaying(false);
  };
  
  const handleContinue = () => {
    if (step === 'upload') {
      if (!audioFile) {
        setError('Please upload an audio file');
        return;
      }
      
      if (!voiceName.trim()) {
        setError('Please enter a name for your voice');
        return;
      }
      
      setStep('review');
    } else if (step === 'review') {
      setStep('processing');
      handleProcessVoice();
    } else if (step === 'complete') {
      onClose();
    }
  };
  
  const handleProcessVoice = async () => {
    try {
      if (!audioFile) return;
      
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 500);
      
      // Upload the audio file to Supabase
      const result = await uploadFile(
        audioFile, 
        'voice', 
        'cloned-voices', 
        `${voiceName.replace(/\s+/g, '-').toLowerCase()}`
      );
      
      clearInterval(progressInterval);
      
      if (!result) {
        throw new Error('Failed to upload audio file');
      }
      
      // In a real app, you'd call your API endpoint for voice cloning here
      // For now, we'll simulate it with a timeout
      setTimeout(() => {
        setProgress(100);
        
        // Create a mock response
        const voiceData = {
          voiceId: `cloned-${Date.now()}`,
          name: voiceName,
          audioUrl: result.url
        };
        
        onVoiceCloned(voiceData);
        setStep('complete');
      }, 2000);
      
    } catch (err: any) {
      setError(err.message || 'Failed to process voice');
      setStep('review');
    }
  };
  
  const resetForm = () => {
    setVoiceName('');
    setAudioFile(null);
    setPreviewUrl('');
    setError('');
    setProgress(0);
    setStep('upload');
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-xl border border-gray-700 max-w-xl w-full p-0 shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-800">
          <h2 className="text-xl font-semibold text-white">Voice Cloning</h2>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-800 text-gray-400 hover:text-white"
          >
            <FiX size={20} />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          {step === 'upload' && (
            <>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Voice Name
                </label>
                <input
                  type="text"
                  value={voiceName}
                  onChange={(e) => setVoiceName(e.target.value)}
                  placeholder="Enter a name for your voice"
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Voice Sample
                </label>
                <div 
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed border-gray-700 rounded-lg p-6 text-center cursor-pointer hover:border-purple-500 transition-colors"
                >
                  {!audioFile ? (
                    <div className="flex flex-col items-center justify-center">
                      <FiUpload className="text-gray-400 mb-2" size={24} />
                      <p className="text-gray-400 mb-1">Click to upload an audio sample</p>
                      <p className="text-xs text-gray-500">MP3, WAV, or M4A (max. 10MB)</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-4">
                      <FiMic className="text-purple-500" size={24} />
                      <div className="flex flex-col items-start">
                        <p className="text-white font-medium">{audioFile.name}</p>
                        <p className="text-xs text-gray-400">
                          {(audioFile.size / (1024 * 1024)).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                  )}
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="audio/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
                
                <p className="text-xs text-gray-500 mt-2">
                  For best results, upload a clear audio recording, 1-2 minutes in length.
                </p>
              </div>
            </>
          )}
          
          {step === 'review' && (
            <>
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Review Your Sample
                  </label>
                  <button 
                    onClick={resetForm}
                    className="text-xs text-purple-400 hover:text-purple-300"
                  >
                    Change Sample
                  </button>
                </div>
                
                <div className="bg-gray-800 rounded-lg p-4 flex items-center space-x-4">
                  <button
                    onClick={togglePlayback}
                    className="h-10 w-10 rounded-full bg-purple-600 hover:bg-purple-500 flex items-center justify-center flex-shrink-0"
                  >
                    {isPlaying ? (
                      <FiPause className="text-white" />
                    ) : (
                      <FiPlay className="text-white ml-1" />
                    )}
                  </button>
                  
                  <audio
                    ref={audioRef}
                    src={previewUrl}
                    onEnded={handleAudioEnded}
                    className="hidden"
                  />
                  
                  <div className="flex-1">
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div className="bg-purple-500 h-full w-0"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Voice Name
                </label>
                <p className="text-white p-2 bg-gray-800 rounded-lg">
                  {voiceName}
                </p>
              </div>
              
              <div className="mb-2">
                <h4 className="text-sm font-medium text-gray-300 mb-2">Important Notes</h4>
                <ul className="text-xs text-gray-400 space-y-1 list-disc pl-4">
                  <li>Voice cloning requires a clear audio recording with minimal background noise</li>
                  <li>The cloned voice will be available for your projects within this app</li>
                  <li>Processing may take a few minutes depending on the length of your sample</li>
                </ul>
              </div>
            </>
          )}
          
          {step === 'processing' && (
            <div className="py-6">
              <div className="flex flex-col items-center justify-center">
                <div className="mb-4 relative">
                  <div className="w-24 h-24 rounded-full bg-gray-800 flex items-center justify-center">
                    <FiLoader className="text-purple-500 animate-spin" size={32} />
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-white font-medium">{progress}%</span>
                  </div>
                </div>
                
                <h3 className="text-lg font-medium text-white mb-2">Processing Your Voice</h3>
                <p className="text-gray-400 text-sm text-center max-w-md">
                  Your voice sample is being processed. This may take a few minutes. Please don't close this window.
                </p>
              </div>
            </div>
          )}
          
          {step === 'complete' && (
            <div className="py-6">
              <div className="flex flex-col items-center justify-center">
                <div className="mb-4">
                  <div className="w-20 h-20 rounded-full bg-green-900/30 flex items-center justify-center border-4 border-green-500">
                    <FiCheck className="text-green-500" size={32} />
                  </div>
                </div>
                
                <h3 className="text-lg font-medium text-white mb-2">Voice Cloning Complete!</h3>
                <p className="text-gray-400 text-sm text-center max-w-md mb-4">
                  Your voice "{voiceName}" has been successfully cloned and is now available for use in your podcasts.
                </p>
              </div>
            </div>
          )}
          
          {error && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200 text-sm">
              {error}
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t border-gray-800 flex justify-end">
          {step !== 'processing' && (
            <div className="flex space-x-3">
              {step !== 'complete' && (
                <button
                  onClick={onClose}
                  className="px-4 py-2 rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"
                >
                  Cancel
                </button>
              )}
              
              <button
                onClick={handleContinue}
                className="px-4 py-2 rounded-md bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-500 hover:to-blue-500 text-white font-medium shadow-lg"
              >
                {step === 'upload' ? 'Continue' : 
                 step === 'review' ? 'Clone Voice' : 
                 'Close'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 