import { useState, useRef } from 'react';
import { <PERSON>X, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Loader, FiSliders, FiVolume2 } from 'react-icons/fi';

interface VoiceCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVoiceCreated: (data: { voiceId: string; name: string; audioUrl: string }) => void;
}

// Voice presets for quick selection
const voicePresets = [
  { id: 'male-deep', name: 'Male Deep', gender: 'male', accent: 'American', age: 'middle-aged', description: 'Deep, authoritative male voice' },
  { id: 'male-casual', name: 'Male Casual', gender: 'male', accent: 'American', age: 'young adult', description: 'Friendly, casual male voice' },
  { id: 'female-professional', name: 'Female Professional', gender: 'female', accent: 'British', age: 'adult', description: 'Clear, professional female voice' },
  { id: 'female-warm', name: 'Female Warm', gender: 'female', accent: 'American', age: 'adult', description: 'Warm, inviting female voice' },
  { id: 'neutral-clear', name: 'Neutral Clear', gender: 'neutral', accent: 'American', age: 'young adult', description: 'Clear, neutral voice' },
];

// Available accents
const accents = [
  'American', 'British', 'Australian', 'Indian', 'Irish', 
  'Scottish', 'South African', 'Canadian', 'New Zealand'
];

// Age ranges
const ageRanges = ['young adult', 'adult', 'middle-aged', 'senior'];

export default function VoiceCreateModal({ isOpen, onClose, onVoiceCreated }: VoiceCreateModalProps) {
  const [step, setStep] = useState<'design' | 'preview' | 'processing' | 'complete'>('design');
  const [voiceName, setVoiceName] = useState('');
  const [gender, setGender] = useState<'male' | 'female' | 'neutral'>('male');
  const [accent, setAccent] = useState('American');
  const [age, setAge] = useState('adult');
  const [clarity, setClarity] = useState(70);
  const [stability, setStability] = useState(60);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState('');
  const [previewUrl, setPreviewUrl] = useState('');
  const [progress, setProgress] = useState(0);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  const handlePresetSelect = (presetId: string) => {
    const preset = voicePresets.find(p => p.id === presetId);
    if (preset) {
      setSelectedPreset(presetId);
      setGender(preset.gender as 'male' | 'female' | 'neutral');
      setAccent(preset.accent);
      setAge(preset.age);
      
      // Set some default values based on the preset
      if (presetId === 'male-deep') {
        setClarity(75);
        setStability(80);
      } else if (presetId === 'female-professional') {
        setClarity(85);
        setStability(70);
      } else if (presetId === 'neutral-clear') {
        setClarity(90);
        setStability(50);
      } else {
        setClarity(70);
        setStability(60);
      }
    }
  };
  
  const handlePreview = async () => {
    try {
      setError('');
      
      // Validate inputs
      if (!voiceName.trim()) {
        setError('Please enter a name for your voice');
        return;
      }
      
      // In a real app, you'd call your API for voice preview
      // For now, simulate with a timeout
      setStep('preview');
      
      // Simulate voice preview generation
      setTimeout(() => {
        // Mock audio URL - in a real app, this would come from your API
        setPreviewUrl('/demo-voice-preview.mp3');
      }, 1500);
      
    } catch (err: any) {
      setError(err.message || 'Failed to generate voice preview');
    }
  };
  
  const togglePlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };
  
  const handleAudioEnded = () => {
    setIsPlaying(false);
  };
  
  const handleContinue = () => {
    if (step === 'design') {
      handlePreview();
    } else if (step === 'preview') {
      setStep('processing');
      handleGenerateVoice();
    } else if (step === 'complete') {
      onClose();
    }
  };
  
  const handleGenerateVoice = async () => {
    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 95) {
            clearInterval(progressInterval);
            return 95;
          }
          return prev + 5;
        });
      }, 300);
      
      // In a real app, you'd call your API to generate the voice
      // For now, simulate with a timeout
      setTimeout(() => {
        clearInterval(progressInterval);
        setProgress(100);
        
        // Create mock response
        const voiceData = {
          voiceId: `generated-${Date.now()}`,
          name: voiceName,
          audioUrl: '/demo-voice-complete.mp3'
        };
        
        onVoiceCreated(voiceData);
        setStep('complete');
      }, 3000);
      
    } catch (err: any) {
      setError(err.message || 'Failed to generate voice');
      setStep('preview');
    }
  };
  
  const resetForm = () => {
    setVoiceName('');
    setGender('male');
    setAccent('American');
    setAge('adult');
    setClarity(70);
    setStability(60);
    setSelectedPreset(null);
    setError('');
    setProgress(0);
    setStep('design');
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-xl border border-gray-700 max-w-2xl w-full p-0 shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-800">
          <h2 className="text-xl font-semibold text-white">Create AI Voice</h2>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-800 text-gray-400 hover:text-white"
          >
            <FiX size={20} />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          {step === 'design' && (
            <>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Voice Name
                </label>
                <input
                  type="text"
                  value={voiceName}
                  onChange={(e) => setVoiceName(e.target.value)}
                  placeholder="Enter a name for your voice"
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Quick Presets
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {voicePresets.map((preset) => (
                    <div
                      key={preset.id}
                      onClick={() => handlePresetSelect(preset.id)}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedPreset === preset.id 
                          ? 'bg-purple-900/30 border-purple-500' 
                          : 'bg-gray-800 border-gray-700 hover:border-gray-600'
                      }`}
                    >
                      <h4 className="text-sm font-medium text-white mb-1">{preset.name}</h4>
                      <p className="text-xs text-gray-400">{preset.description}</p>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Gender
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    {['male', 'female', 'neutral'].map((g) => (
                      <button
                        key={g}
                        onClick={() => setGender(g as 'male' | 'female' | 'neutral')}
                        className={`py-2 rounded-md transition-colors ${
                          gender === g
                            ? 'bg-purple-600 text-white'
                            : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                      >
                        {g.charAt(0).toUpperCase() + g.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Accent
                  </label>
                  <select
                    value={accent}
                    onChange={(e) => setAccent(e.target.value)}
                    className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                  >
                    {accents.map((a) => (
                      <option key={a} value={a}>{a}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Age
                  </label>
                  <select
                    value={age}
                    onChange={(e) => setAge(e.target.value)}
                    className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                  >
                    {ageRanges.map((a) => (
                      <option key={a} value={a}>{a.charAt(0).toUpperCase() + a.slice(1)}</option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-300">
                    <div className="flex items-center">
                      <FiSliders className="mr-2" size={16} />
                      Voice Parameters
                    </div>
                  </label>
                </div>
                
                <div className="space-y-4 bg-gray-800/50 p-4 rounded-lg border border-gray-700">
                  <div>
                    <div className="flex justify-between mb-1">
                      <label className="text-sm text-gray-400">Clarity</label>
                      <span className="text-xs text-gray-500">{clarity}%</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={clarity}
                      onChange={(e) => setClarity(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>Rough</span>
                      <span>Clear</span>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <label className="text-sm text-gray-400">Stability</label>
                      <span className="text-xs text-gray-500">{stability}%</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={stability}
                      onChange={(e) => setStability(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>Variable</span>
                      <span>Stable</span>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
          
          {step === 'preview' && (
            <>
              <div className="mb-6">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-md font-medium text-white">Voice Preview</h3>
                  <button 
                    onClick={() => setStep('design')}
                    className="text-xs text-purple-400 hover:text-purple-300"
                  >
                    Edit Voice
                  </button>
                </div>
                
                {previewUrl ? (
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="mb-4">
                      <p className="text-gray-300 mb-2 italic text-sm">
                        "The quick brown fox jumps over the lazy dog. Voice preview sample for your custom AI voice."
                      </p>
                    </div>
                    
                    <div className="flex items-center">
                      <button
                        onClick={togglePlayback}
                        className="h-10 w-10 rounded-full bg-purple-600 hover:bg-purple-500 flex items-center justify-center mr-3 flex-shrink-0"
                      >
                        <FiVolume2 className="text-white" />
                      </button>
                      
                      <audio
                        ref={audioRef}
                        src={previewUrl}
                        onEnded={handleAudioEnded}
                        className="hidden"
                      />
                      
                      <div className="flex-1">
                        <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                          <div className="bg-purple-500 h-full w-0"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gray-800 rounded-lg p-8 flex items-center justify-center">
                    <FiLoader className="text-purple-500 animate-spin mr-2" size={18} />
                    <span className="text-gray-300">Generating preview...</span>
                  </div>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-400 mb-1">Voice Type</h4>
                  <p className="text-sm text-white">
                    {gender.charAt(0).toUpperCase() + gender.slice(1)}, {age}
                  </p>
                </div>
                
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-400 mb-1">Accent</h4>
                  <p className="text-sm text-white">{accent}</p>
                </div>
                
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-400 mb-1">Clarity</h4>
                  <p className="text-sm text-white">{clarity}%</p>
                </div>
                
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-400 mb-1">Stability</h4>
                  <p className="text-sm text-white">{stability}%</p>
                </div>
              </div>
              
              <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-3 mb-4">
                <p className="text-sm text-blue-200">
                  This is a preview of your AI voice. If you're happy with it, click "Generate Voice" to create the full version.
                </p>
              </div>
            </>
          )}
          
          {step === 'processing' && (
            <div className="py-6">
              <div className="flex flex-col items-center justify-center">
                <div className="mb-4 relative">
                  <div className="w-24 h-24 rounded-full bg-gray-800 flex items-center justify-center">
                    <FiLoader className="text-purple-500 animate-spin" size={32} />
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-white font-medium">{progress}%</span>
                  </div>
                </div>
                
                <h3 className="text-lg font-medium text-white mb-2">Generating Your Voice</h3>
                <p className="text-gray-400 text-sm text-center max-w-md">
                  Your AI voice is being created. This process can take a few minutes to complete.
                </p>
              </div>
            </div>
          )}
          
          {step === 'complete' && (
            <div className="py-6">
              <div className="flex flex-col items-center justify-center">
                <div className="mb-4">
                  <div className="w-20 h-20 rounded-full bg-green-900/30 flex items-center justify-center border-4 border-green-500">
                    <FiCheck className="text-green-500" size={32} />
                  </div>
                </div>
                
                <h3 className="text-lg font-medium text-white mb-2">Voice Created Successfully!</h3>
                <p className="text-gray-400 text-sm text-center max-w-md mb-4">
                  Your AI voice "{voiceName}" has been generated and is now available for use in your podcasts.
                </p>
              </div>
            </div>
          )}
          
          {error && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200 text-sm">
              {error}
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t border-gray-800 flex justify-end">
          {step !== 'processing' && (
            <div className="flex space-x-3">
              {step !== 'complete' && (
                <button
                  onClick={onClose}
                  className="px-4 py-2 rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700"
                >
                  Cancel
                </button>
              )}
              
              <button
                onClick={handleContinue}
                disabled={step === 'preview' && !previewUrl}
                className={`px-4 py-2 rounded-md bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-500 hover:to-blue-500 text-white font-medium shadow-lg ${
                  step === 'preview' && !previewUrl ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {step === 'design' ? 'Preview Voice' : 
                 step === 'preview' ? 'Generate Voice' : 
                 'Close'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 