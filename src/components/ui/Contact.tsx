"use client";

import { useState } from 'react';
import { <PERSON>Mail, <PERSON><PERSON>ser, FiMessageSquare, <PERSON>Send, FiLoader } from 'react-icons/fi';
import { useContactTranslations } from '@/hooks/useTranslations';

export default function Contact() {
  const t = useContactTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [honeypot, setHoneypot] = useState('');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    // Bot protection: check honeypot field
    if (honeypot) {
      e.preventDefault();
      return; // Silent fail for bots
    }

    // Let the form submit naturally to FormBold
    setIsSubmitting(true);
  };

  return (
    <div id="contact" className="bg-black relative py-24 overflow-hidden">
      {/* Background glow with AstroStudio colors */}
      <div className="absolute top-1/3 right-1/4 w-[350px] h-[350px] bg-[#141493]/30 rounded-full blur-[150px]"></div>
      <div className="absolute bottom-1/3 left-1/4 w-[350px] h-[350px] bg-[#417ef7]/30 rounded-full blur-[150px]"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-base font-semibold text-[#417ef7] tracking-wide uppercase">{t('title')}</h2>
          <p className="mt-2 text-3xl font-extrabold text-white sm:text-4xl lg:text-5xl">
            {t('subtitle')}
          </p>
          <p className="mt-4 max-w-2xl text-xl text-gray-400 lg:mx-auto">
            Have questions about our platform? We're here to help you make the most of AI for your creative projects.
          </p>
        </div>

        <div className="mt-16 lg:mt-20">
          <div className="max-w-3xl mx-auto bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-sm p-8 sm:p-10 rounded-2xl border border-[#417ef7]/20 shadow-2xl">
            <form onSubmit={handleSubmit} action="https://formbold.com/s/35klE" method="POST" className="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-6">
              {/* Honeypot field for bot protection */}
              <input
                type="text"
                name="website"
                value={honeypot}
                onChange={(e) => setHoneypot(e.target.value)}
                style={{ display: 'none' }}
                tabIndex={-1}
                autoComplete="off"
              />

              <div className="sm:col-span-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-300">
                  {t('name')}
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiUser className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    type="text"
                    name="name"
                    id="name"
                    autoComplete="name"
                    required
                    className="block w-full pl-10 pr-3 py-3 border border-gray-700 bg-gray-800/50 rounded-xl text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition-all"
                    placeholder="John Doe"
                  />
                </div>
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                  {t('email')}
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiMail className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="block w-full pl-10 pr-3 py-3 border border-gray-700 bg-gray-800/50 rounded-xl text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition-all"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="message" className="block text-sm font-medium text-gray-300">
                  {t('message')}
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute top-3 left-3 pointer-events-none">
                    <FiMessageSquare className="h-5 w-5 text-gray-500" />
                  </div>
                  <textarea
                    id="message"
                    name="message"
                    rows={6}
                    required
                    className="block w-full pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 rounded-xl text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#417ef7] focus:border-[#417ef7] transition-all resize-none"
                    placeholder="Your message..."
                  ></textarea>
                </div>
              </div>

              <div className="sm:col-span-2 flex justify-end">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center px-8 py-3 text-base font-medium rounded-xl text-white bg-gradient-to-r from-[#141493] to-[#417ef7] hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#417ef7] transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl hover:shadow-[#417ef7]/25"
                >
                  {isSubmitting ? (
                    <>
                      <FiLoader className="mr-2 -ml-1 h-5 w-5 animate-spin" />
                      {t('sending')}
                    </>
                  ) : (
                    <>
                      <FiSend className="mr-2 -ml-1 h-5 w-5" />
                      {t('sendMessage')}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}