"use client";

import { useState, useCallback, useEffect, useRef, memo } from 'react';
import { FiImage, FiVideo, FiMusic, FiMic, FiArrowRight, FiAirplay, FiEdit, FiUpload } from 'react-icons/fi';
import { useExamplesTranslations } from '@/hooks/useTranslations';

type AudioExample = {
  title: string;
  url: string;
  embedUrl?: string;
  isSunoEmbed?: boolean;
}

type ImageExample = {
  title: string;
  imageUrl: string;
}

type Example = {
  id: string;
  title: string;
  description: string;
  icon: any;
  color: string;
  outputType: 'audio' | 'video' | 'image' | 'image-gallery';
  input?: string;
  output?: string;
  inputType?: 'image' | 'video';
  examples?: AudioExample[] | ImageExample[];
}

// Memoized content components to prevent unnecessary rerenders
const ImageDisplay = memo(({ src, alt, className }: { src: string; alt: string; className?: string }) => (
  <div className="aspect-square overflow-hidden rounded-lg bg-gray-800 relative">
    {/* Placeholder before image loads */}
    <div className="absolute inset-0 bg-gray-700 flex items-center justify-center">
      <FiImage className="h-8 w-8 text-gray-500" />
    </div>
    <img 
      src={src} 
      alt={alt} 
      loading="lazy"
      width="400"
      height="400"
      decoding="async"
      className={`w-full h-full object-contain md:object-cover relative z-10 ${className || ''}`}
      style={{ 
        maxWidth: '100%', 
        height: 'auto',
        contentVisibility: 'auto',
      }}
    />
  </div>
));

ImageDisplay.displayName = 'ImageDisplay';

const VideoDisplay = memo(({ src }: { src: string }) => (
  <div className="aspect-video overflow-hidden rounded-lg bg-black relative">
    {/* Placeholder before video loads */}
    <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
      <FiVideo className="h-8 w-8 text-gray-500" />
    </div>
    <video 
      src={src} 
      controls 
      playsInline
      preload="metadata"
      muted
      width="400"
      height="225"
      className="w-full h-full relative z-10"
      style={{ 
        maxWidth: '100%', 
        height: 'auto'
      }}
    />
  </div>
));

VideoDisplay.displayName = 'VideoDisplay';

const AudioPlayer = memo(({ src }: { src: string }) => (
  <audio 
    src={src} 
    controls 
    preload="none"
    className="w-full h-full"
  />
));

AudioPlayer.displayName = 'AudioPlayer';

// Chroma Grid styled tab button with enhanced visual effects
const TabButton = memo(({
  example,
  isSelected,
  onSelect
}: {
  example: Example;
  isSelected: boolean;
  onSelect: (example: Example) => void;
}) => (
  <button
    onClick={() => onSelect(example)}
    aria-label={`Select ${example.title} example`}
    aria-selected={isSelected}
    className={`group relative p-6 rounded-2xl text-white text-center transition-all duration-300 will-change-transform overflow-hidden
      ${isSelected
        ? 'scale-105 shadow-2xl shadow-blue-500/25'
        : 'hover:scale-102 hover:shadow-xl hover:shadow-gray-900/50'}`}
    style={{ WebkitTapHighlightColor: 'transparent' }}
  >
    {/* Background gradient with glassmorphism effect */}
    <div className={`absolute inset-0 rounded-2xl transition-all duration-300 ${
      isSelected
        ? `bg-gradient-to-br ${example.color} opacity-90`
        : 'bg-gradient-to-br from-gray-800/80 to-gray-900/80 group-hover:from-gray-700/80 group-hover:to-gray-800/80'
    }`} />

    {/* Glassmorphism overlay */}
    <div className="absolute inset-0 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10" />

    {/* Animated border glow */}
    {isSelected && (
      <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${example.color} opacity-20 blur-xl`} />
    )}

    {/* Content */}
    <div className="relative z-10">
      <div className={`w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center transition-all duration-300 ${
        isSelected
          ? 'bg-white/20 shadow-lg'
          : 'bg-white/10 group-hover:bg-white/15'
      }`}>
        <example.icon className="h-6 w-6" />
      </div>
      <h3 className="text-sm font-semibold tracking-wide">{example.title}</h3>
    </div>
  </button>
));

TabButton.displayName = 'TabButton';

// Chroma Grid styled mobile tab button
const MobileTabButton = memo(({
  example,
  isSelected,
  onSelect
}: {
  example: Example;
  isSelected: boolean;
  onSelect: (example: Example) => void;
}) => (
  <button
    onClick={() => onSelect(example)}
    aria-label={`Select ${example.title} example`}
    className={`group relative px-5 py-4 rounded-xl text-white transition-all duration-300 whitespace-nowrap will-change-transform overflow-hidden
      ${isSelected
        ? 'shadow-xl shadow-blue-500/25'
        : 'hover:shadow-lg hover:shadow-gray-900/50'}`}
    style={{ WebkitTapHighlightColor: 'transparent' }}
  >
    {/* Background gradient with glassmorphism effect */}
    <div className={`absolute inset-0 rounded-xl transition-all duration-300 ${
      isSelected
        ? `bg-gradient-to-r ${example.color} opacity-90`
        : 'bg-gradient-to-r from-gray-800/80 to-gray-900/80 group-hover:from-gray-700/80 group-hover:to-gray-800/80'
    }`} />

    {/* Glassmorphism overlay */}
    <div className="absolute inset-0 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10" />

    {/* Content */}
    <div className="relative z-10 flex items-center min-h-[24px] min-w-[120px]">
      <div className={`w-8 h-8 mr-3 rounded-lg flex items-center justify-center transition-all duration-300 ${
        isSelected
          ? 'bg-white/20'
          : 'bg-white/10 group-hover:bg-white/15'
      }`}>
        <example.icon className="h-4 w-4" />
      </div>
      <span className="text-sm font-semibold tracking-wide">{example.title}</span>
    </div>
  </button>
));

MobileTabButton.displayName = 'MobileTabButton';

// Move examples inside component to access translations
const getExamples = (t: any): Example[] => [
  {
    id: 'image-generation',
    title: t('imageGeneration.title'),
    description: t('imageGeneration.description'),
    icon: FiImage,
    color: 'from-amber-500 to-pink-500',
    examples: [
      {
        title: t('sampleTitles.sample1'),
        imageUrl: '/examples/image/FluxFreeMar102025.webp'
      },
      {
        title: t('sampleTitles.sample2'),
        imageUrl: '/examples/image/FLUX1FreeMar132025.png'
      }
    ],
    outputType: 'image-gallery'
  },
  {
    id: 'image-to-video',
    title: t('videoGeneration.title'),
    description: t('videoGeneration.description'),
    icon: FiAirplay,
    color: 'from-purple-600 to-indigo-600',
    input: 'https://replicate.delivery/pbxt/MZZyui7brAbh1d2AsyPtgPIByUwzSv6Uou8objC7zXEjLySc/1a8nt7yw5drm80cn05r89mjce0.png',
    output: 'https://replicate.delivery/xezq/B08EdKGBIAK8E9rbNTX9jWO9ScVNbFivMaeXZM9ZUb5HAaKKA/output.mp4',
    inputType: 'image',
    outputType: 'video'
  },
  {
    id: 'music-generation',
    title: t('musicGeneration.title'),
    description: t('musicGeneration.description'),
    icon: FiMusic,
    color: 'from-indigo-600 to-blue-600',
    examples: [
      {
        title: t('sampleTitles.example1'),
        url: 'https://replicate.delivery/czjl/gtltcFpRgjaTAxwroR8son1V67Cb0CsY2trSYjH3zF6O88eJA/tmpm_njkzw8.mp3'
      },
      {
        title: t('sampleTitles.example2'),
        url: 'https://replicate.delivery/pbxt/aWKHjHunJh4rIFKMj0q9kNLFjd9gkhNzPvapRWEeLyIeqZkSA/out.mp3'
      }
    ],
    outputType: 'audio'
  },
  {
    id: 'podcast-generation',
    title: t('podcastGeneration.title'),
    description: t('podcastGeneration.description'),
    icon: FiMic,
    color: 'from-blue-600 to-cyan-400',
    examples: [
      {
        title: t('sampleTitles.podcast1'),
        url: 'https://replicate.delivery/czjl/iuKGJMTf3GxsHiCXGEh1DvLeR1eOEnzs20tosiWc68mQCqLoA/tmpn9ym5o0d.mp3'
      },
      {
        title: t('sampleTitles.podcast2'),
        url: 'https://replicate.delivery/czjl/bIuSZOcpI4p6NtvHiEa9us7Zd272Jsl5ZhMk9tneHvmmy5CKA/tmpbuleb_9n.mp3'
      }
    ],
    outputType: 'audio'
  },
  {
    id: 'image-upscale',
    title: t('imageUpscaling.title'),
    description: t('imageUpscaling.description'),
    icon: FiEdit,
    color: 'from-cyan-400 to-teal-500',
    input: 'https://replicate.delivery/pbxt/LKnw8rSgafZf4IlAVyPhzpX1TpTVcyfRa1saoaoiSfUYZLiL/fermat_app_a_living_room_modern_and_minimalistic_39b5a58a-e05b-4281-ac24-e87435256333-1.webp',
    output: 'https://replicate.delivery/pbxt/YFlNVWt9oEpTMVEKg69wXDRGtjHSHKV02ESZeChKj69ffiYmA/output.jpg',
    inputType: 'image',
    outputType: 'image'
  },
  {
    id: 'video-upscale',
    title: t('videoUpscaling.title'),
    description: t('videoUpscaling.description'),
    icon: FiVideo,
    color: 'from-teal-500 to-green-500',
    input: 'https://replicate.delivery/pbxt/IH0PJ62bhr9rySCQNuMyo5RXyPf72LcOiCeNwLYdTqSZCZSe/TheMonkeyKing1965.mp4',
    output: 'https://replicate.delivery/pbxt/LlAV8s0puD78AJ4z7YqBtwfDrIjEhNvfAh5WJrl6f2zVVF4gA/out.mp4',
    inputType: 'video',
    outputType: 'video'
  }
];

export default function Examples() {
  const t = useExamplesTranslations();
  const examples = getExamples(t);

  const [selectedExample, setSelectedExample] = useState(examples[0]);
  const [isIOS, setIsIOS] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Detect iOS devices
  useEffect(() => {
    const userAgent = window.navigator.userAgent.toLowerCase();
    setIsIOS(/iphone|ipad|ipod/.test(userAgent));
    
    // Mark as loaded after initial render to avoid layout shifts
    setIsLoaded(true);
  }, []);

  // Use useCallback to prevent unnecessary rerenders
  const handleExampleSelect = useCallback((example: Example) => {
    if (example.id === selectedExample.id) return;
    
    setSelectedExample(example);
    
    // Scroll to content area on mobile after selection
    if (window.innerWidth < 768 && contentRef.current) {
      // Use requestAnimationFrame to ensure smoother interaction
      requestAnimationFrame(() => {
        contentRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'nearest',
        });
      });
    }
  }, [selectedExample.id]);

  // Pre-reserve the height of the container to prevent layout shifts
  const getInitialContentHeight = useCallback(() => {
    // Apply a sensible default height based on content type
    const example = selectedExample;
    
    if (example.input && example.output) {
      return 'min-h-[600px] md:min-h-[400px]';
    } else if (example.outputType === 'audio') {
      return 'min-h-[300px]';
    } else if (example.outputType === 'image-gallery') {
      return 'min-h-[450px]';
    }
    
    return 'min-h-[350px]';
  }, [selectedExample]);
 
  return (
    <div id="examples" className="py-16 md:py-24 bg-black relative overflow-hidden">
      {/* Background glow - reduced for performance */}
      <div aria-hidden="true" className="absolute top-1/3 right-0 w-[200px] md:w-[300px] h-[200px] md:h-[300px] bg-purple-600/20 rounded-full blur-[120px] opacity-60"></div>
      <div aria-hidden="true" className="absolute bottom-1/3 left-0 w-[200px] md:w-[300px] h-[200px] md:h-[300px] bg-cyan-400/20 rounded-full blur-[120px] opacity-60"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-base text-[#417ef7] font-semibold tracking-wide uppercase">{t('title')}</h2>
          <p className="mt-2 text-2xl md:text-3xl leading-8 font-extrabold tracking-tight text-white sm:text-4xl">
            {t('subtitle')}
          </p>
          <p className="mt-4 max-w-2xl text-lg md:text-xl text-gray-400 mx-auto">
            {t('description')}
          </p>
        </div>

        <div className="mt-10 md:mt-16">
          {/* Mobile-optimized scrollable tabs for small screens with iOS/Android optimizations */}
          <div 
            ref={scrollContainerRef}
            className="md:hidden mb-8 overflow-x-auto pb-3 -mx-4 px-4 flex space-x-3 no-scrollbar"
            style={{
              WebkitOverflowScrolling: 'touch',
              scrollSnapType: isIOS ? 'x mandatory' : 'none',
              scrollBehavior: 'smooth'
            }}
            role="tablist"
            aria-orientation="horizontal"
          >
            {examples.map((example) => (
              <div key={example.id} className="flex-shrink-0" style={{ scrollSnapAlign: isIOS ? 'start' : 'none' }}>
                <MobileTabButton 
                  example={example} 
                  isSelected={selectedExample.id === example.id}
                  onSelect={handleExampleSelect}
                />
              </div>
            ))}
          </div>

          {/* Chroma Grid for larger screens */}
          <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-16" role="tablist" aria-orientation="horizontal">
            {examples.map((example) => (
              <TabButton
                key={example.id}
                example={example}
                isSelected={selectedExample.id === example.id}
                onSelect={handleExampleSelect}
              />
            ))}
          </div>

          <div
            ref={contentRef}
            id="example-content"
            role="tabpanel"
            aria-labelledby={`tab-${selectedExample.id}`}
            className={`relative overflow-hidden rounded-2xl p-6 md:p-8 shadow-2xl transition-all duration-500 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'} ${getInitialContentHeight()}`}
          >
            {/* Background with glassmorphism effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl" />
            <div className="absolute inset-0 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl" />

            {/* Animated gradient border */}
            <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${selectedExample.color} opacity-20 blur-xl`} />

            {/* Content */}
            <div className="relative z-10">
              <div className="flex items-center mb-4">
                <div className={`w-10 h-10 rounded-xl bg-gradient-to-r ${selectedExample.color} flex items-center justify-center mr-4 shadow-lg`}>
                  <selectedExample.icon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{selectedExample.title}</h3>
                  <p className="text-gray-300 text-sm">{selectedExample.description}</p>
                </div>
              </div>

            {selectedExample.input && selectedExample.output && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 p-6 backdrop-blur-sm border border-white/10">
                  <h4 className="text-white font-semibold mb-4 flex items-center">
                    <div className="w-8 h-8 rounded-lg bg-green-500/20 flex items-center justify-center mr-3">
                      <FiUpload className="h-4 w-4 text-green-400" />
                    </div>
                    {t('input')}
                  </h4>
                  {selectedExample.inputType === 'image' && (
                    <ImageDisplay src={selectedExample.input} alt="Input" />
                  )}
                  {selectedExample.inputType === 'video' && (
                    <VideoDisplay src={selectedExample.input} />
                  )}
                </div>

                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 p-6 backdrop-blur-sm border border-white/10">
                  <h4 className="text-white font-semibold mb-4 flex items-center">
                    <div className="w-8 h-8 rounded-lg bg-blue-500/20 flex items-center justify-center mr-3">
                      <FiArrowRight className="h-4 w-4 text-blue-400" />
                    </div>
                    {t('output')}
                  </h4>
                  {selectedExample.outputType === 'image' && (
                    <ImageDisplay src={selectedExample.output} alt="Output" />
                  )}
                  {selectedExample.outputType === 'video' && (
                    <VideoDisplay src={selectedExample.output} />
                  )}
                </div>
              </div>
            )}

            {selectedExample.examples && selectedExample.outputType === 'audio' && (
              <div className="space-y-6">
                {(selectedExample.examples as AudioExample[]).map((example, index) => (
                  <div key={index} className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 p-6 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/10">
                    <h4 className="text-white font-semibold mb-4 flex items-center">
                      <FiMusic className="h-5 w-5 mr-2 text-blue-400" />
                      {example.title}
                    </h4>
                    <AudioPlayer src={example.url} />
                  </div>
                ))}
              </div>
            )}

            {selectedExample.outputType === 'image-gallery' && selectedExample.examples && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 md:gap-8">
                {(selectedExample.examples as ImageExample[]).map((example, index) => (
                  <div key={index} className="group relative">
                    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 p-4 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/10">
                      <h4 className="text-white font-semibold mb-3 text-center">{example.title}</h4>
                      <div className="relative overflow-hidden rounded-xl aspect-square bg-gray-900/50">
                        <img
                          src={example.imageUrl}
                          alt={example.title}
                          loading="lazy"
                          width="400"
                          height="400"
                          decoding="async"
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                          style={{
                            maxWidth: '100%',
                            contentVisibility: 'auto',
                          }}
                          onError={(e) => {
                            (e.target as HTMLImageElement).style.display = 'none';
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'w-full h-full flex items-center justify-center text-gray-400';
                            errorDiv.innerText = 'Image could not be loaded';
                            (e.target as HTMLImageElement).parentNode?.appendChild(errorDiv);
                          }}
                        />
                        {/* Overlay gradient */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}