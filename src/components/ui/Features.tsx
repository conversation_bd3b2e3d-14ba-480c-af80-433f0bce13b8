"use client";

import {
  FiImage,
  FiEdit3,
  FiVideo,
  FiMic,
  FiMusic,
  FiCpu
} from 'react-icons/fi';
import { useFeaturesTranslations } from '@/hooks/useTranslations';

export default function Features() {
  const t = useFeaturesTranslations();

  const features = [
    {
      name: t('imageGeneration.title'),
      description: t('imageGeneration.description'),
      icon: FiImage,
      color: 'from-[#141493] to-[#417ef7]',
    },
    {
      name: t('imageEditing.title'),
      description: t('imageEditing.description'),
      icon: FiEdit3,
      color: 'from-[#141493] to-[#417ef7]',
    },
    {
      name: t('videoGeneration.title'),
      description: t('videoGeneration.description'),
      icon: FiVideo,
      color: 'from-[#141493] to-[#417ef7]',
    },
    {
      name: t('podcastCreation.title'),
      description: t('podcastCreation.description'),
      icon: FiMic,
      color: 'from-[#141493] to-[#417ef7]',
    },
    {
      name: t('musicComposition.title'),
      description: t('musicComposition.description'),
      icon: FiMusic,
      color: 'from-[#141493] to-[#417ef7]',
    },
    {
      name: t('advancedModels.title'),
      description: t('advancedModels.description'),
      icon: FiCpu,
      color: 'from-[#141493] to-[#417ef7]',
    },
  ];

  return (
    <div id="features" className="py-24 bg-black relative overflow-hidden">
      {/* Background glow */}
      <div className="absolute top-1/3 right-0 w-[300px] h-[300px] bg-gradient-to-r from-[#141493]/20 to-[#417ef7]/20 rounded-full blur-[120px]"></div>
      <div className="absolute bottom-1/3 left-0 w-[300px] h-[300px] bg-[#417ef7]/10 rounded-full blur-[120px]"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:text-center">
          <h2 className="text-base bg-gradient-to-r from-[#141493] to-[#417ef7] bg-clip-text text-transparent font-semibold tracking-wide uppercase">Features</h2>
          <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-white sm:text-4xl">
            Everything you need for AI-powered creativity
          </p>
          <p className="mt-4 max-w-2xl text-xl text-gray-400 lg:mx-auto">
            Our platform offers a comprehensive suite of AI tools for all your creative needs.
          </p>
        </div>

        <div className="mt-16">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((feature) => (
              <div key={feature.name} className="card group hover:translate-y-[-5px]">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-r ${feature.color} text-white group-hover:scale-110 transition-all duration-300`}>
                    <feature.icon className="h-6 w-6" aria-hidden="true" />
                  </div>
                  <h3 className="ml-4 text-lg leading-6 font-medium text-white">{feature.name}</h3>
                </div>
                <p className="mt-5 text-base text-gray-400">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}