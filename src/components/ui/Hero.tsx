"use client";

import Link from 'next/link';
import { FiArrowRight, FiStar, FiZap, FiShield } from 'react-icons/fi';
import { useEffect, useState, useRef } from 'react';
import { useHeroTranslations, useCommonTranslations } from '@/hooks/useTranslations';

export default function Hero() {
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const heroRef = useRef<HTMLDivElement>(null);
  const t = useHeroTranslations();
  const common = useCommonTranslations();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Splash cursor effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (heroRef.current) {
        const rect = heroRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    };

    const heroElement = heroRef.current;
    if (heroElement) {
      heroElement.addEventListener('mousemove', handleMouseMove);
      return () => heroElement.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  return (
    <div
      ref={heroRef}
      className="relative overflow-hidden pt-[80px] sm:pt-[120px] pb-12 sm:pb-16 lg:pb-32 min-h-screen flex items-center"
    >
      {/* Splash cursor effect - hidden on mobile for performance */}
      <div
        className="absolute pointer-events-none z-10 w-48 h-48 sm:w-96 sm:h-96 rounded-full opacity-20 transition-all duration-300 ease-out hidden sm:block"
        style={{
          left: mousePosition.x - 96,
          top: mousePosition.y - 96,
          background: 'radial-gradient(circle, rgba(20, 20, 147, 0.3) 0%, rgba(65, 126, 247, 0.2) 50%, transparent 70%)',
          filter: 'blur(40px)',
        }}
      />

      {/* Background gradient with modern design - responsive sizes */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-black"></div>
        <div className="absolute top-1/4 left-1/2 -translate-x-1/2 w-[400px] h-[400px] sm:w-[600px] sm:h-[600px] lg:w-[800px] lg:h-[800px] rounded-full bg-gradient-to-r from-[#141493]/30 to-[#417ef7]/20 blur-[60px] sm:blur-[120px]"></div>
        <div className="absolute bottom-1/4 right-1/4 w-[300px] h-[300px] sm:w-[450px] sm:h-[450px] lg:w-[600px] lg:h-[600px] rounded-full bg-[#417ef7]/10 blur-[50px] sm:blur-[100px]"></div>
        <div className="absolute top-2/3 left-1/3 w-[200px] h-[200px] sm:w-[300px] sm:h-[300px] lg:w-[400px] lg:h-[400px] rounded-full bg-[#141493]/20 blur-[40px] sm:blur-[80px]"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-20 mt-4 sm:mt-8">
        <div className="lg:grid lg:grid-cols-12 lg:gap-8">
          <div className={`text-center sm:text-center md:max-w-2xl md:mx-auto lg:col-span-6 lg:text-left transition-all duration-1000 transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <div className="mb-4 sm:mb-6">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-gradient-to-r from-[#141493] to-[#417ef7] text-white">
                {t('badge')}
              </span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-extrabold tracking-tight">
              <span className="block text-white mb-1 sm:mb-2">{t('title')}</span>
              <span className="block bg-gradient-to-r from-[#141493] to-[#417ef7] bg-clip-text text-transparent pb-1 sm:pb-2">
                {t('titleHighlight')}
              </span>
            </h1>
            <p className="mt-4 sm:mt-6 text-base sm:text-lg lg:text-xl xl:text-2xl text-gray-300 max-w-2xl mx-auto lg:mx-0">
              {t('description')}
            </p>

            {/* Key benefits */}
            <div className="mt-6 sm:mt-8 grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
              <div className="flex items-center justify-center sm:justify-start space-x-2">
                <div className="w-2 h-2 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                <span className="text-xs sm:text-sm text-gray-400">{t('benefits.multiModel')}</span>
              </div>
              <div className="flex items-center justify-center sm:justify-start space-x-2">
                <div className="w-2 h-2 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                <span className="text-xs sm:text-sm text-gray-400">{t('benefits.privacyFirst')}</span>
              </div>
              <div className="flex items-center justify-center sm:justify-start space-x-2">
                <div className="w-2 h-2 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                <span className="text-xs sm:text-sm text-gray-400">{t('benefits.professionalQuality')}</span>
              </div>
            </div>

            <div className="mt-8 sm:mt-10 sm:max-w-lg sm:mx-auto text-center lg:text-left lg:mx-0">
              <div className="flex flex-col sm:grid sm:grid-cols-2 gap-3 sm:gap-4">
                <Link
                  href="/signup"
                  className="group relative inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-medium text-white bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-xl hover:shadow-lg hover:shadow-[#417ef7]/25 transform transition-all duration-300 hover:scale-105 overflow-hidden"
                >
                  <span className="relative z-10 flex items-center">
                    {t('cta.primary')}
                    <FiArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-[#417ef7] to-[#141493] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Link>
                <Link
                  href="/#features"
                  className="inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-medium text-white border-2 border-[#417ef7]/30 rounded-xl hover:border-[#417ef7] hover:bg-[#417ef7]/10 transition-all duration-300 hover:scale-105"
                >
                  {t('cta.secondary')}
                </Link>
              </div>
              <p className="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-400">
                {t('disclaimer')}
              </p>
            </div>
          </div>
          <div className={`mt-8 sm:mt-12 relative sm:max-w-lg sm:mx-auto lg:mt-0 lg:max-w-none lg:mx-0 lg:col-span-6 lg:flex lg:items-center transition-all duration-1000 delay-300 transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <div className="relative mx-auto w-full lg:max-w-2xl">
              {/* Floating cards showcase - responsive */}
              <div className="relative h-[300px] sm:h-[400px] lg:h-[500px] w-full">
                {/* Main central card - responsive sizing */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-36 sm:w-72 sm:h-40 lg:w-80 lg:h-48 bg-gradient-to-br from-[#141493]/20 to-[#417ef7]/20 backdrop-blur-xl border border-[#417ef7]/30 rounded-2xl p-4 sm:p-5 lg:p-6 shadow-2xl">
                  <div className="flex items-center justify-between mb-3 sm:mb-4">
                    <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                    <div className="text-xs text-gray-400">AstroStudio AI</div>
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold text-white mb-1 sm:mb-2">AI Creation Platform</h3>
                  <p className="text-xs sm:text-sm text-gray-300 mb-3 sm:mb-4">Generate, edit, and create with advanced AI models</p>
                  <div className="flex space-x-2">
                    <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#417ef7] rounded-full animate-pulse"></div>
                    <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#417ef7]/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#417ef7]/40 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>

                {/* Floating feature cards - responsive positioning and sizing */}
                <div className="absolute top-4 sm:top-6 lg:top-8 left-4 sm:left-6 lg:left-8 w-24 h-16 sm:w-28 sm:h-18 lg:w-32 lg:h-20 bg-gradient-to-br from-[#141493]/30 to-[#417ef7]/20 backdrop-blur-lg border border-[#417ef7]/20 rounded-xl p-2 sm:p-2.5 lg:p-3 transform rotate-[-5deg] hover:rotate-0 transition-transform duration-300">
                  <div className="text-xs font-medium text-white">Image Gen</div>
                  <div className="text-xs text-gray-400 mt-1">AI Images</div>
                </div>

                <div className="absolute top-8 sm:top-12 lg:top-16 right-6 sm:right-8 lg:right-12 w-24 h-16 sm:w-28 sm:h-18 lg:w-32 lg:h-20 bg-gradient-to-br from-[#417ef7]/30 to-[#141493]/20 backdrop-blur-lg border border-[#141493]/20 rounded-xl p-2 sm:p-2.5 lg:p-3 transform rotate-[8deg] hover:rotate-0 transition-transform duration-300">
                  <div className="text-xs font-medium text-white">Video Gen</div>
                  <div className="text-xs text-gray-400 mt-1">AI Videos</div>
                </div>

                <div className="absolute bottom-12 sm:bottom-16 lg:bottom-20 left-8 sm:left-12 lg:left-16 w-24 h-16 sm:w-28 sm:h-18 lg:w-32 lg:h-20 bg-gradient-to-br from-[#141493]/25 to-[#417ef7]/25 backdrop-blur-lg border border-[#417ef7]/25 rounded-xl p-2 sm:p-2.5 lg:p-3 transform rotate-[3deg] hover:rotate-0 transition-transform duration-300">
                  <div className="text-xs font-medium text-white">Music Gen</div>
                  <div className="text-xs text-gray-400 mt-1">AI Music</div>
                </div>

                <div className="absolute bottom-6 sm:bottom-8 lg:bottom-12 right-4 sm:right-6 lg:right-8 w-24 h-16 sm:w-28 sm:h-18 lg:w-32 lg:h-20 bg-gradient-to-br from-[#417ef7]/25 to-[#141493]/25 backdrop-blur-lg border border-[#141493]/25 rounded-xl p-2 sm:p-2.5 lg:p-3 transform rotate-[-8deg] hover:rotate-0 transition-transform duration-300">
                  <div className="text-xs font-medium text-white">Podcast Gen</div>
                  <div className="text-xs text-gray-400 mt-1">AI Podcasts</div>
                </div>

                {/* Animated background elements - responsive sizing */}
                <div className="absolute top-0 right-0 w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-gradient-to-br from-[#417ef7]/10 to-transparent rounded-full blur-xl animate-pulse"></div>
                <div className="absolute bottom-0 left-0 w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-gradient-to-tr from-[#141493]/10 to-transparent rounded-full blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 