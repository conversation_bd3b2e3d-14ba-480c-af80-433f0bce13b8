'use client';

import React, { useState } from 'react';
import { useLanguage, getOppositeLanguage } from '@/contexts/LanguageContext';
import { FiGlobe, FiChevronDown } from 'react-icons/fi';

interface LanguageSwitcherProps {
  className?: string;
  showLabel?: boolean;
}

const languageNames = {
  en: 'English',
  es: 'Español'
};

const languageFlags = {
  en: '🇺🇸',
  es: '🇪🇸'
};

export default function LanguageSwitcher({ className = '', showLabel = true }: LanguageSwitcherProps) {
  const { language, setLanguage, isLoading } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <FiGlobe className="w-4 h-4 text-gray-400 animate-pulse" />
        {showLabel && <span className="text-sm text-gray-400">Loading...</span>}
      </div>
    );
  }

  const handleLanguageChange = (newLanguage: 'en' | 'es') => {
    setLanguage(newLanguage);
    setIsOpen(false);
  };

  const currentLanguageName = languageNames[language];
  const currentFlag = languageFlags[language];
  const oppositeLanguage = getOppositeLanguage(language);
  const oppositeLanguageName = languageNames[oppositeLanguage];
  const oppositeFlag = languageFlags[oppositeLanguage];

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors border border-gray-600"
        aria-label="Change language"
      >
        <span className="text-lg">{currentFlag}</span>
        {showLabel && (
          <span className="text-sm font-medium text-white">{currentLanguageName}</span>
        )}
        <FiChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-20">
            <div className="py-1">
              {/* Current Language */}
              <div className="px-4 py-2 text-sm text-gray-400 border-b border-gray-600">
                Current Language
              </div>
              
              <button
                onClick={() => handleLanguageChange(language)}
                className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <span className="text-lg">{currentFlag}</span>
                <span className="font-medium">{currentLanguageName}</span>
                <span className="ml-auto text-xs text-blue-200">✓</span>
              </button>

              {/* Divider */}
              <div className="border-t border-gray-600 my-1" />

              {/* Other Language */}
              <button
                onClick={() => handleLanguageChange(oppositeLanguage)}
                className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 transition-colors"
              >
                <span className="text-lg">{oppositeFlag}</span>
                <span>{oppositeLanguageName}</span>
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

// Compact version for mobile or tight spaces
export function CompactLanguageSwitcher({ className = '' }: { className?: string }) {
  const { language, setLanguage, isLoading } = useLanguage();

  if (isLoading) {
    return (
      <div className={`w-8 h-8 rounded-full bg-gray-700 animate-pulse ${className}`} />
    );
  }

  const oppositeLanguage = getOppositeLanguage(language);
  const oppositeFlag = languageFlags[oppositeLanguage];

  return (
    <button
      onClick={() => setLanguage(oppositeLanguage)}
      className={`w-8 h-8 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors border border-gray-600 flex items-center justify-center ${className}`}
      aria-label={`Switch to ${languageNames[oppositeLanguage]}`}
      title={`Switch to ${languageNames[oppositeLanguage]}`}
    >
      <span className="text-sm">{oppositeFlag}</span>
    </button>
  );
}
