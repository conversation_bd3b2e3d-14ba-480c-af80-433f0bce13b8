"use client";

import { FiCheck } from 'react-icons/fi';
import { useSession } from 'next-auth/react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createGuestCheckoutSession } from '@/lib/actions/subscription-actions';
import { useLocale, createLocaleUrl } from '@/hooks/useLocale';
import { usePricingTranslations } from '@/hooks/useTranslations';

// Move tiers inside component to access translations
const getTiers = (t: any) => [
  {
    name: t('plans.free.name'),
    priceMonthly: 0,
    anual: 0,
    description: t('plans.free.description'),
    features: [
      t('plans.free.features.aiAssistant'),
      t('plans.free.features.fluxModel'),
      t('plans.free.features.limitedGenerations'),
      t('plans.free.features.basicModels'),
      t('plans.free.features.standardQuality'),
    ],
    ctaText: t('plans.free.cta'),
    plan: 'free',
    mostPopular: false,
    stripePriceId: '', // Free plan doesn't need Stripe Price ID
  },
  {
    name: t('plans.standard.name'),
    priceMonthly: 5.00,
    anual: 60.00,
    description: t('plans.standard.description'),
    features: [
      t('plans.standard.features.aiImages'),
      t('plans.standard.features.musicTracks'),
      t('plans.standard.features.speechToText'),
      t('plans.standard.features.aiWriting'),
      t('plans.standard.features.advancedModels'),
      t('plans.standard.features.higherQuality'),
      t('plans.standard.features.priorityProcessing'),
    ],
    ctaText: t('plans.standard.cta'),
    plan: 'standard',
    mostPopular: false,
    stripePriceId: 'price_standard_monthly', // Replace with your actual Stripe price ID
  },
  {
    name: t('plans.premium.name'),
    priceMonthly: 15.00,
    anual: 180.00,
    description: t('plans.premium.description'),
    features: [
      t('plans.premium.features.everythingStandard'),
      t('plans.premium.features.videoGeneration'),
      t('plans.premium.features.podcasts'),
      t('plans.premium.features.higherQuality'),
      t('plans.premium.features.priorityProcessing'),
      t('plans.premium.features.longerContent'),
      t('plans.premium.features.advancedCustomization'),
      t('plans.premium.features.modelFineTuning'),
    ],
    ctaText: t('plans.premium.cta'),
    plan: 'premium',
    mostPopular: true,
    stripePriceId: 'price_premium_monthly', // Replace with your actual Stripe price ID
  },
];

export default function Pricing() {
  const { data: session } = useSession();
  const router = useRouter();
  const locale = useLocale();
  const t = usePricingTranslations();
  const tiers = getTiers(t);
  const [isLoading, setIsLoading] = useState<{[key: string]: boolean}>({});

  // Direct checkout handler - used for both main buttons and "Pay without Registration"
  const handleDirectCheckout = async (planType: 'Standard' | 'Premium', planKey: string) => {
    try {
      // Set loading state for this specific button
      setIsLoading(prev => ({ ...prev, [planKey]: true }));
      
      // For logged-in users, redirect to subscription management
      if (session?.user) {
        router.push(createLocaleUrl('/dashboard/subscription', locale));
        return;
      }
      
      // For guests, create direct checkout session
      const result = await createGuestCheckoutSession(planType);
      
      if (result?.url) {
        window.location.href = result.url;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Error starting checkout:', error);
      setIsLoading(prev => ({ ...prev, [planKey]: false }));
    }
  };

  const handleSubscription = async (plan: string) => {
    // For free plan, handle differently
    if (plan === 'free') {
      if (session?.user) {
        // For logged-in users, just redirect to dashboard
        router.push(createLocaleUrl('/dashboard', locale));
      } else {
        // For guests, redirect to signup
        router.push(createLocaleUrl('/signup?plan=free', locale));
      }
      return;
    }

    // For paid plans, use direct checkout
    const planType = plan === 'standard' ? 'Standard' : 'Premium';
    handleDirectCheckout(planType, plan);
  };

  return (
    <div id="pricing" className="bg-black relative py-24 overflow-hidden">
      {/* Background glow */}
      <div className="absolute top-1/3 left-1/4 w-[500px] h-[500px] bg-gradient-to-r from-[#141493]/20 to-[#417ef7]/20 rounded-full blur-[150px]"></div>
      <div className="absolute bottom-1/3 right-1/4 w-[400px] h-[400px] bg-[#417ef7]/10 rounded-full blur-[120px]"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-base font-semibold bg-gradient-to-r from-[#141493] to-[#417ef7] bg-clip-text text-transparent tracking-wide uppercase">{t('title')}</h2>
          <p className="mt-2 text-3xl font-extrabold text-white sm:text-4xl lg:text-5xl">
            {t('subtitle')}
          </p>
          <p className="mt-4 max-w-2xl text-xl text-gray-400 lg:mx-auto">
            {t('description')}
          </p>
        </div>

        <div className="mt-20 space-y-12 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-8">
          {tiers.map((tier) => (
            <div
              key={tier.name}
              className={`relative p-8 bg-[#111] border rounded-2xl shadow-lg flex flex-col hover:shadow-2xl transition-all duration-300
                ${tier.mostPopular ? 'border-[#417ef7] scale-105 z-10 shadow-[#417ef7]/20' : 'border-[#222] hover:border-[#333]'}`}
            >
              {tier.mostPopular && (
                <div className="absolute top-0 right-6 -mt-4 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full px-4 py-1 text-xs font-semibold text-white uppercase tracking-wider">
                  {t('mostPopular')}
                </div>
              )}
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white">{tier.name}</h3>
                <p className="mt-4 flex items-baseline">
                  <span className="text-4xl font-extrabold text-white">${tier.priceMonthly}</span>
                  <span className="ml-1 text-xl font-semibold text-gray-400">{t('month')}</span>
                </p>
                <p className="text-2xl font-extrabold text-white">${tier.anual}
                <span className="ml-1 text-xl font-semibold text-gray-400">{t('year')}</span>
                </p>
                <p className="mt-4 text-gray-400">{tier.description}</p>

                <ul className="mt-6 space-y-4">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex items-start">
                      <div className="flex-shrink-0">
                        <FiCheck className="h-5 w-5 text-[#417ef7]" aria-hidden="true" />
                      </div>
                      <p className="ml-3 text-base text-gray-300">{feature}</p>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-8">
                <button
                  onClick={() => handleSubscription(tier.plan)}
                  disabled={isLoading[tier.plan]}
                  className={`w-full flex justify-center py-4 px-6 rounded-xl shadow-lg font-medium text-white
                    ${tier.mostPopular
                      ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] hover:from-[#417ef7] hover:to-[#141493] shadow-[#417ef7]/25'
                      : 'bg-[#222] hover:bg-[#333] border border-[#333] hover:border-[#417ef7]/50'
                    } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#417ef7] transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {isLoading[tier.plan] ? t('processing') : tier.ctaText}
                </button>
                
                {/* Add alternative text for paid plans */}
                {tier.plan !== 'free' && (
                  <div className="mt-2 text-center text-xs text-gray-500">
                    {t('noRegistration')}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 