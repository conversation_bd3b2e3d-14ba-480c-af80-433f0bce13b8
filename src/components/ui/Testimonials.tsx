"use client";

import { FiUser } from 'react-icons/fi';
import { useTestimonialsTranslations } from '@/hooks/useTranslations';

export default function Testimonials() {
  const t = useTestimonialsTranslations();

  const testimonials = [
    {
      content: t('testimonial1.content'),
      author: {
        name: t('testimonial1.author'),
        role: t('testimonial1.role'),
        initials: "S<PERSON>"
      }
    },
    {
      content: t('testimonial2.content'),
      author: {
        name: t('testimonial2.author'),
        role: t('testimonial2.role'),
        initials: "AC"
      }
    },
    {
      content: t('testimonial3.content'),
      author: {
        name: t('testimonial3.author'),
        role: t('testimonial3.role'),
        initials: "<PERSON>"
      }
    }
  ];

  return (
    <div className="bg-black py-24 relative overflow-hidden">
      {/* Background glow */}
      <div className="absolute top-1/2 left-1/3 w-[400px] h-[400px] bg-gradient-to-r from-[#141493]/10 to-[#417ef7]/10 rounded-full blur-[150px]"></div>
      <div className="absolute bottom-1/4 right-1/3 w-[300px] h-[300px] bg-[#417ef7]/10 rounded-full blur-[150px]"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-base font-semibold uppercase tracking-wide bg-gradient-to-r from-[#141493] to-[#417ef7] bg-clip-text text-transparent">{t('title')}</h2>
          <p className="mt-2 text-3xl font-extrabold text-white sm:text-4xl">
            {t('subtitle')}
          </p>
        </div>

        <div className="mt-20 grid gap-8 md:grid-cols-1 lg:grid-cols-3 lg:gap-x-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="card relative group hover:-translate-y-1 transition-all duration-300">
              <div className="h-full flex flex-col justify-between">
                <div>
                  <div className="absolute top-0 left-0 -mt-2 -ml-2 h-20 w-20 bg-gradient-to-r from-[#141493] to-[#417ef7] opacity-20 blur-2xl rounded-full transform -translate-x-1/2 -translate-y-1/2 group-hover:opacity-30 transition-opacity duration-300"></div>

                  <div className="relative">
                    <svg className="h-12 w-12 text-[#417ef7] opacity-25" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
                      <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.896 3.456-8.352 9.12-8.352 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                    </svg>
                    <p className="relative mt-6 text-base leading-7 text-gray-300">
                      {testimonial.content}
                    </p>
                  </div>
                </div>

                <div className="mt-8 flex items-center">
                  <div className="flex-shrink-0 overflow-hidden rounded-full border-2 border-white/10 bg-gray-800 h-12 w-12">
                    <div className="w-full h-full bg-gradient-to-br from-[#141493]/30 to-[#417ef7]/30 flex items-center justify-center">
                      {testimonial.author.initials ? (
                        <span className="text-white font-bold text-sm">
                          {testimonial.author.initials}
                        </span>
                      ) : (
                        <FiUser className="text-white h-5 w-5" />
                      )}
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="text-base font-medium text-white">{testimonial.author.name}</div>
                    <div className="text-xs text-gray-400">{testimonial.author.role}</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}