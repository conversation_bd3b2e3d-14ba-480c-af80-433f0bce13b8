import { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message, ChatState, ChatAction } from '@/types/chat';
import { AIModel, DEFAULT_CHAT_MODEL_CATEGORIES } from '@/types/models';
import { useSession } from 'next-auth/react';

// Initial state for the chat context
const initialState: ChatState = {
  conversations: [],
  activeConversation: null,
  messages: [],
  loading: false,
  error: null,
  pending: false,
  selectedModel: DEFAULT_CHAT_MODEL_CATEGORIES['Featured'][0], // Default to first model
  showModelSelector: false
};

// Reducer function to handle all chat actions
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_CONVERSATIONS':
      return { ...state, conversations: action.payload };
    
    case 'SET_ACTIVE_CONVERSATION':
      return { ...state, activeConversation: action.payload };
    
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    
    case 'ADD_MESSAGE':
      return { 
        ...state, 
        messages: [...state.messages, action.payload] 
      };
    
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(message => 
          message.id === action.payload.id 
            ? { ...message, ...action.payload.updates } 
            : message
        )
      };
    
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'SET_PENDING':
      return { ...state, pending: action.payload };
    
    case 'SET_SELECTED_MODEL':
      return { ...state, selectedModel: action.payload };
    
    case 'TOGGLE_MODEL_SELECTOR':
      return { 
        ...state, 
        showModelSelector: action.payload !== undefined 
          ? action.payload 
          : !state.showModelSelector 
      };
    
    case 'CLEAR_CONVERSATION':
      return {
        ...state,
        messages: [],
        activeConversation: null
      };
    
    default:
      return state;
  }
}

// Create context
const ChatContext = createContext<{
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
  startNewConversation: () => Promise<Conversation>;
  sendMessage: (content: string) => Promise<void>;
  loadConversation: (conversationId: string) => Promise<void>;
  loadConversations: () => Promise<void>;
  deleteConversation: (conversationId: string) => Promise<void>;
} | undefined>(undefined);

// Provider component
export function ChatProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const { data: session } = useSession();
  
  // Load user's conversations when session is available
  useEffect(() => {
    if (session?.user) {
      loadConversations();
    }
  }, [session?.user?.id]);
  
  // Fetch all conversations for the current user
  const loadConversations = async () => {
    if (!session?.user) return;
    
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await fetch('/api/conversations');
      const data = await response.json();
      
      if (data.success) {
        dispatch({ type: 'SET_CONVERSATIONS', payload: data.conversations });
      } else {
        dispatch({ type: 'SET_ERROR', payload: data.error || 'Failed to load conversations' });
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Network error when loading conversations' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };
  
  // Load a specific conversation and its messages
  const loadConversation = async (conversationId: string) => {
    if (!session?.user) return;
    
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await fetch(`/api/conversations/${conversationId}`);
      const data = await response.json();
      
      if (data.success) {
        dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: data.conversation });
        dispatch({ type: 'SET_MESSAGES', payload: data.messages });
      } else {
        dispatch({ type: 'SET_ERROR', payload: data.error || 'Failed to load conversation' });
      }
    } catch (error) {
      console.error('Error loading conversation:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Network error when loading conversation' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };
  
  // Start a new conversation
  const startNewConversation = async (): Promise<Conversation> => {
    if (!session?.user) throw new Error('User must be logged in');
    if (!state.selectedModel) throw new Error('No model selected');
    
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_CONVERSATION' });
      
      const newConversation: Conversation = {
        id: uuidv4(),
        userId: session.user.id as string,
        title: 'New conversation',
        model: state.selectedModel.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ conversation: newConversation, messages: [] })
      });
      
      const data = await response.json();
      
      if (data.success) {
        dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: newConversation });
        // Update the conversations list
        await loadConversations();
        return newConversation;
      } else {
        dispatch({ type: 'SET_ERROR', payload: data.error || 'Failed to create conversation' });
        throw new Error(data.error || 'Failed to create conversation');
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Network error when creating conversation' });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };
  
  // Send a message and get a response
  const sendMessage = async (content: string) => {
    if (!session?.user) return;
    if (!state.selectedModel) return;
    
    // If no active conversation, create one
    let conversationId = state.activeConversation?.id;
    
    if (!conversationId) {
      const newConversation = await startNewConversation();
      conversationId = newConversation.id;
    }
    
    try {
      // Create user message
      const userMessage: Message = {
        id: uuidv4(),
        conversationId,
        role: 'user',
        content,
        timestamp: new Date().toISOString()
      };
      
      // Add user message to the UI immediately
      dispatch({ type: 'ADD_MESSAGE', payload: userMessage });
      
      // Create assistant message with pending state
      const assistantMessage: Message = {
        id: uuidv4(),
        conversationId,
        role: 'assistant',
        content: '',
        model: state.selectedModel.id,
        timestamp: new Date().toISOString(),
        pending: true
      };
      
      // Add pending assistant message to the UI
      dispatch({ type: 'ADD_MESSAGE', payload: assistantMessage });
      dispatch({ type: 'SET_PENDING', payload: true });
      
      // Save the user message to the backend
      await fetch(`/api/conversations/${conversationId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ messages: [userMessage] })
      });
      
      // Send the message to the AI and get a response
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: content,
          conversationId,
          modelId: state.selectedModel.id
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error getting response from AI');
      }
      
      // Process the streaming response
      const reader = response.body?.getReader();
      let decoder = new TextDecoder();
      let responseText = '';
      
      if (!reader) {
        throw new Error('Failed to get response stream');
      }
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }
          
          const chunk = decoder.decode(value, { stream: true });
          responseText += chunk;
          
          // Update the assistant message as we receive chunks
          dispatch({
            type: 'UPDATE_MESSAGE',
            payload: {
              id: assistantMessage.id,
              updates: {
                content: responseText,
                pending: false
              }
            }
          });
        }
      } catch (error) {
        console.error('Error reading stream:', error);
        dispatch({
          type: 'UPDATE_MESSAGE',
          payload: {
            id: assistantMessage.id,
            updates: {
              content: 'Error receiving response. Please try again.',
              pending: false,
              error: true
            }
          }
        });
        throw error;
      } finally {
        // Complete the assistant message
        const finalAssistantMessage: Message = {
          ...assistantMessage,
          content: responseText,
          pending: false
        };
        
        // Save the assistant message to the backend
        await fetch(`/api/conversations/${conversationId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ messages: [finalAssistantMessage] })
        });
        
        // Update the conversation title if it's a new conversation
        if (state.activeConversation?.title === 'New conversation') {
          // Create a title from the first user message
          const title = content.length > 30 
            ? content.substring(0, 30) + '...' 
            : content;
          
          const updatedConversation = {
            ...state.activeConversation,
            title,
            updatedAt: new Date().toISOString()
          };
          
          await fetch('/api/conversations', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ conversation: updatedConversation })
          });
          
          dispatch({ 
            type: 'SET_ACTIVE_CONVERSATION', 
            payload: updatedConversation 
          });
          
          // Refresh conversations list to show the updated title
          await loadConversations();
        }
        
        dispatch({ type: 'SET_PENDING', payload: false });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Error sending message' });
      dispatch({ type: 'SET_PENDING', payload: false });
    }
  };
  
  // Delete a conversation
  const deleteConversation = async (conversationId: string) => {
    if (!session?.user) return;
    
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: 'DELETE'
      });
      
      const data = await response.json();
      
      if (data.success) {
        // If we're deleting the active conversation, clear it
        if (state.activeConversation?.id === conversationId) {
          dispatch({ type: 'CLEAR_CONVERSATION' });
        }
        
        // Refresh the conversations list
        await loadConversations();
      } else {
        dispatch({ type: 'SET_ERROR', payload: data.error || 'Failed to delete conversation' });
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Network error when deleting conversation' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };
  
  return (
    <ChatContext.Provider
      value={{
        state,
        dispatch,
        startNewConversation,
        sendMessage,
        loadConversation,
        loadConversations,
        deleteConversation
      }}
    >
      {children}
    </ChatContext.Provider>
  );
}

// Custom hook to use the chat context
export function useChat() {
  const context = useContext(ChatContext);
  
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  
  return context;
} 