'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { getUserPreferredLanguage, saveLanguagePreference } from '@/lib/language-detection';

type Language = 'en' | 'es';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  readonly children: ReactNode;
  readonly initialLanguage?: Language;
}

export function LanguageProvider({ children, initialLanguage }: LanguageProviderProps) {
  const [language, setLanguageState] = useState<Language>(initialLanguage ?? 'en');
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  // Initialize language on mount
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // If we have an initial language from the URL, use that
        if (initialLanguage) {
          setLanguageState(initialLanguage);
          saveLanguagePreference(initialLanguage);
          setIsLoading(false);
          return;
        }

        // Otherwise, detect the user's preferred language
        const detectedLanguage = await getUserPreferredLanguage();
        setLanguageState(detectedLanguage);
        
        // If we're on the root path and detected Spanish, redirect to Spanish version
        if (detectedLanguage === 'es' && pathname === '/') {
          router.replace('/es');
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize language:', error);
        setLanguageState('en'); // Fallback to English
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, [initialLanguage, pathname, router]);

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    saveLanguagePreference(lang);

    // Navigate to the appropriate language version
    const currentPath = pathname ?? '/';

    if (lang === 'es') {
      // If we're on English routes, redirect to Spanish
      if (!currentPath.startsWith('/es')) {
        const spanishPath = currentPath === '/' ? '/es' : `/es${currentPath}`;
        router.push(spanishPath);
      }
    } else if (currentPath.startsWith('/es')) {
      // If we're on Spanish routes, redirect to English
      const englishPath = currentPath.replace(/^\/es/, '') || '/';
      router.push(englishPath);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isLoading }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Helper hook to get the current language without the full context
export function useCurrentLanguage(): Language {
  const { language } = useLanguage();
  return language;
}

// Helper function to create locale-aware URLs
export function createLocaleUrl(path: string, locale?: Language): string {
  const currentLocale = locale ?? 'en';
  
  if (currentLocale === 'es') {
    return path === '/' ? '/es' : `/es${path}`;
  }
  
  return path;
}

// Helper function to get the opposite language
export function getOppositeLanguage(currentLang: Language): Language {
  return currentLang === 'en' ? 'es' : 'en';
}

// Helper function to extract language from pathname
export function getLanguageFromPath(pathname: string): Language {
  if (pathname.startsWith('/es')) {
    return 'es';
  }
  return 'en';
}
