'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import Cookies from 'js-cookie';
import { useSession } from 'next-auth/react';
import { getCurrentUserSubscription } from '@/lib/actions/subscription-actions';

// Cookie name constants with longer expiry
const SUBSCRIPTION_COOKIE = 'subscription_status';
const SUBSCRIPTION_TYPE_COOKIE = 'subscription_type';
const SUBSCRIPTION_ID_COOKIE = 'subscription_id';
const STRIPE_PRICE_ID_COOKIE = 'stripe_price_id';
const COOKIE_EXPIRY = 7; // 7 days for longer cache

// Client-side cache for subscription validation
const validationCache = {
  data: null as Subscription | null,
  timestamp: 0,
  ttl: 1000 * 60 * 60 * 24 // 24 hours cache TTL
};

// Types
export interface Subscription {
  id: string;
  userId?: string;
  status: 'active' | 'canceled' | 'past_due' | 'inactive' | 'trialing';
  type: 'Free' | 'Standard' | 'Premium';
  currentPeriodEnd: Date | null;
  stripePriceId: string | null;
  stripeCustomerId: string | null;
  stripeSubscriptionId?: string | null;
  startDate?: Date | null;
  createdAt?: Date;
  updatedAt?: Date;
  // Neo4j specific fields
  neo4jUserId?: string;
  features?: string[];
  usageLimit?: number;
  usageCount?: number;
  metadata?: Record<string, any>;
}

interface SubscriptionContextType {
  status: string;
  subscription: Subscription | null;
  stripePriceId: string | null;
  loading: boolean;
  validateSubscription: () => Promise<void>;
  hasAccess: (feature: string) => boolean;
  remainingUsage: () => number | null;
  refreshUsage: () => Promise<void>;
}

interface SubscriptionProviderProps {
  children: ReactNode;
}

// Create context
export const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const { data: session, status: sessionStatus } = useSession();
  const [isSubscribed, setIsSubscribed] = useState<boolean>(false);
  const [subscriptionType, setSubscriptionType] = useState<string>('Free');
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [stripePriceId, setStripePriceId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Memoized function to check subscription status using the same logic as StandardPremiumGuard
  const validateSubscription = useCallback(async () => {
    try {
      // Check cache first to avoid unnecessary API calls
      const now = Date.now();
      if (validationCache.data && now - validationCache.timestamp < validationCache.ttl) {
        console.log('Using cached subscription data');
        setIsSubscribed(validationCache.data.status === 'active' || validationCache.data.status === 'trialing');
        setSubscriptionType(validationCache.data.type);
        setSubscription(validationCache.data);
        setStripePriceId(validationCache.data.stripePriceId);
        setLoading(false);
        return;
      }
      
      setLoading(true);
      
      // If no user session, clear subscription state
      if (!session?.user) {
        console.log('No user session - setting to free tier');
        setIsSubscribed(false);
        setSubscriptionType('Free');
        setSubscription(null);
        setStripePriceId(null);
        clearCookies();
        setLoading(false);
        return;
      }

      console.log('Validating subscription for user:', session.user.email);
      
      // Try to get subscription from cookies first for immediate display
      const cookieStatus = Cookies.get(SUBSCRIPTION_COOKIE);
      const cookieType = Cookies.get(SUBSCRIPTION_TYPE_COOKIE);
      const cookiePriceId = Cookies.get(STRIPE_PRICE_ID_COOKIE);
      
      if (cookieStatus && cookieType) {
        // Set state from cookies immediately for fast UI response
        setIsSubscribed(cookieStatus === 'active');
        setSubscriptionType(cookieType);
        setStripePriceId(cookiePriceId || null);
      }
      
      // Check if subscription data is already in the session
      if (session.user.subscription) {
        console.log('Using subscription data from session:', session.user.subscription);
        const isActive = session.user.subscription.status === 'active' || 
                        session.user.subscription.status === 'trialing';
        
        setIsSubscribed(isActive);
        setSubscriptionType(session.user.subscription.type);
        
        // Create a subscription object from session data
        const subFromSession = {
          id: 'session-' + Date.now(),
          status: session.user.subscription.status as any,
          type: session.user.subscription.type as 'Free' | 'Standard' | 'Premium',
          currentPeriodEnd: new Date(session.user.subscription.currentPeriodEnd),
          stripePriceId: null,
          stripeCustomerId: null
        };
        
        setSubscription(subFromSession as Subscription);
        
        // Update cache
        validationCache.data = subFromSession as Subscription;
        validationCache.timestamp = now;
        
        // Store in cookies for persistence
        storeCookies(
          isActive ? 'active' : 'inactive',
          session.user.subscription.type,
          'session-' + Date.now(),
          null
        );
        
        setLoading(false);
        return;
      }
      
      // If we get here, we need to fetch subscription data from the server
      // Make API call with a timeout to prevent long-running requests
      const timeoutPromise = new Promise<null>((resolve) => {
        setTimeout(() => resolve(null), 5000); // 5 second timeout
      });
      
      const fetchPromise = getCurrentUserSubscription();
      const sub = await Promise.race([fetchPromise, timeoutPromise]);
      
      // If timeout occurred, use cookie data as fallback
      if (!sub && cookieStatus && cookieType) {
        console.log('API request timed out, using cookie data');
        setIsSubscribed(cookieStatus === 'active');
        setSubscriptionType(cookieType);
        setStripePriceId(cookiePriceId || null);
        setLoading(false);
        return;
      }
      
      // Store the subscription object
      setSubscription(sub);
      
      // Update cache with fresh data
      if (sub) {
        validationCache.data = sub;
        validationCache.timestamp = now;
      }
      
      // Check if subscription exists and is active or trialing
      if (!sub || (sub.status !== 'active' && sub.status !== 'trialing')) {
        console.log('No active subscription found - subscription status:', sub?.status);
        setIsSubscribed(false);
        setSubscriptionType('Free');
        setStripePriceId(null);
        storeCookies('inactive', 'Free', sub?.id || null, null);
      } else {
        // Get the price ID (critical for validation)
        const priceId = sub.stripePriceId;
        console.log(`Active subscription found - type: ${sub.type}, status: ${sub.status}, price ID: ${priceId}`);
        
        // Premium or Standard subscription
        setIsSubscribed(true);
        
        // Store the stripe price ID for feature access checks
        setStripePriceId(priceId);
        
        // Normalize the subscription type for consistency
        const normalizedType = 
          sub.type?.toLowerCase() === 'premium' ? 'Premium' :
          sub.type?.toLowerCase() === 'standard' ? 'Standard' : 'Free';
        
        setSubscriptionType(normalizedType);
        storeCookies('active', normalizedType, sub.id, priceId);
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error validating subscription:', error);
      
      // Try to use cached data on error
      if (validationCache.data) {
        console.log('Using cached data due to validation error');
        setIsSubscribed(validationCache.data.status === 'active' || validationCache.data.status === 'trialing');
        setSubscriptionType(validationCache.data.type);
        setSubscription(validationCache.data);
        setStripePriceId(validationCache.data.stripePriceId);
      } else {
        // Try to use cookie data on error
        const cookieStatus = Cookies.get(SUBSCRIPTION_COOKIE);
        const cookieType = Cookies.get(SUBSCRIPTION_TYPE_COOKIE);
        const cookiePriceId = Cookies.get(STRIPE_PRICE_ID_COOKIE);
        
        if (cookieStatus && cookieType) {
          console.log('Using cookie data due to validation error');
          setIsSubscribed(cookieStatus === 'active');
          setSubscriptionType(cookieType);
          setStripePriceId(cookiePriceId || null);
        } else {
          // Set defaults on error
          setIsSubscribed(false);
          setSubscriptionType('Free');
          setSubscription(null);
          setStripePriceId(null);
          storeCookies('inactive', 'Free', null, null);
        }
      }
      
      setLoading(false);
    }
  }, [session?.user]);
  
  // Helper to store subscription data in cookies
  const storeCookies = (status: string, type: string, id: string | null, priceId: string | null) => {
    Cookies.set(SUBSCRIPTION_COOKIE, status, { expires: COOKIE_EXPIRY });
    Cookies.set(SUBSCRIPTION_TYPE_COOKIE, type, { expires: COOKIE_EXPIRY });
    
    if (id) {
      Cookies.set(SUBSCRIPTION_ID_COOKIE, id, { expires: COOKIE_EXPIRY });
    } else {
      Cookies.remove(SUBSCRIPTION_ID_COOKIE);
    }
    
    if (priceId) {
      Cookies.set(STRIPE_PRICE_ID_COOKIE, priceId, { expires: COOKIE_EXPIRY });
    } else {
      Cookies.remove(STRIPE_PRICE_ID_COOKIE);
    }
  };
  
  // Helper to clear all subscription cookies
  const clearCookies = () => {
    Cookies.remove(SUBSCRIPTION_COOKIE);
    Cookies.remove(SUBSCRIPTION_TYPE_COOKIE);
    Cookies.remove(SUBSCRIPTION_ID_COOKIE);
    Cookies.remove(STRIPE_PRICE_ID_COOKIE);
  };

  // Memoized function to refresh usage data from Neo4j
  const refreshUsage = useCallback(async () => {
    if (!session?.user || !isSubscribed) return;
    
    try {
      // Get updated subscription data with fresh usage metrics
      const updatedSubscription = await getCurrentUserSubscription();
      
      if (updatedSubscription) {
        // Update just the usage counts without changing subscription status
        setSubscription(prev => {
          if (!prev) return updatedSubscription as Subscription;
          
          return {
            ...prev,
            usageCount: (updatedSubscription as any).usageCount || prev.usageCount,
            metadata: (updatedSubscription as any).metadata || prev.metadata
          } as Subscription;
        });
      }
    } catch (error) {
      console.error('Error refreshing usage data:', error);
    }
  }, [session?.user, isSubscribed]);

  // Memoized function to calculate remaining usage for the current subscription
  const remainingUsage = useCallback((): number | null => {
    if (!subscription || !isSubscribed) return null;

    const { usageLimit, usageCount } = subscription;
    if (typeof usageLimit === 'undefined' || typeof usageCount === 'undefined') {
      return null;
    }

    return Math.max(0, usageLimit - usageCount);
  }, [subscription, isSubscribed]);

  // Memoized function to check if user has access to a specific feature - using same logic as StandardPremiumGuard
  const hasAccess = useCallback((feature: string) => {
    console.log(`Checking access for feature: ${feature}`);
    console.log(`Subscription type: ${subscriptionType}, stripePriceId: ${stripePriceId}`);
    
    // Free tier features are always accessible
    const freeFeatures = [
      'FREE_TEXT_GENERATION',
      'FREE_IMAGE_GENERATION'
    ];
    
    if (freeFeatures.includes(feature)) {
      return true;
    }
    
    // Loading state or no subscription = no access
    if (loading || !isSubscribed) {
      return false;
    }
    
    // If subscription has explicit features list from Neo4j, use that first
    if (subscription?.features && Array.isArray(subscription.features)) {
      return subscription.features.includes(feature);
    }
    
    // Using the EXACT same logic as StandardPremiumGuard
    if (subscriptionType === 'Premium') {
      // Premium users have access to everything
      return true;
    } else if (subscriptionType === 'Standard') {
      // Standard tier access logic
      const standardFeatures = [
        'IMAGE_GENERATION', 
        'TEXT_GENERATION', 
        'STANDARD_MODELS',
        'IMAGE_EDITING',
        'UPSCALE_IMAGE',
        'MUSIC_GENERATION', 
        'SPEECH_TO_TEXT',
      ];
      
      return standardFeatures.includes(feature);
    }

    // Free tier - no access to premium features
    return false;
  }, [loading, isSubscribed, subscription?.features, subscriptionType]);

  // Effect to load subscription from cookies when context mounts
  useEffect(() => {
    const loadFromCookies = async () => {
      const cookieStatus = Cookies.get(SUBSCRIPTION_COOKIE);
      const cookieType = Cookies.get(SUBSCRIPTION_TYPE_COOKIE);
      const cookieId = Cookies.get(SUBSCRIPTION_ID_COOKIE);
      const cookiePriceId = Cookies.get(STRIPE_PRICE_ID_COOKIE);
      
      console.log('Loading from cookies:', { cookieStatus, cookieType, cookiePriceId });
      
      if (cookieStatus && cookieType) {
        // Set initial state from cookies immediately for fast UI response
        setIsSubscribed(cookieStatus === 'active');
        setSubscriptionType(cookieType);
        setStripePriceId(cookiePriceId || null);
        setLoading(false);
        
        // Check if we have cached data
        const now = Date.now();
        if (validationCache.data && now - validationCache.timestamp < validationCache.ttl) {
          console.log('Using cached subscription data on mount');
          setSubscription(validationCache.data);
          return;
        }
        
        // If user is logged in, validate in background without showing loading state
        if (session?.user) {
          validateSubscription();
        }
      } else if (session?.user) {
        // No cookies but user is logged in - need to validate
        await validateSubscription();
      } else {
        // No session, no cookies - free tier
        setIsSubscribed(false);
        setSubscriptionType('Free');
        setStripePriceId(null);
        setLoading(false);
      }
    };

    if (sessionStatus === 'loading') {
      // Wait for session to load
      return;
    }
    
    loadFromCookies();
  }, [session, sessionStatus]);

  // Add an effect to revalidate when session changes (login/logout)
  useEffect(() => {
    if (sessionStatus === 'loading') return;
    
    // Only validate on significant session changes
    const userEmail = session?.user?.email;
    const prevUserEmail = validationCache.data?.userId || '';
    
    if (session?.user && userEmail !== prevUserEmail) {
      // User logged in or changed - validate subscription
      validateSubscription();
    } else if (session === null) {
      // User logged out - clear subscription
      setIsSubscribed(false);
      setSubscriptionType('Free');
      setSubscription(null);
      setStripePriceId(null);
      clearCookies();
      
      // Clear cache on logout
      validationCache.data = null;
      validationCache.timestamp = 0;
    }
  }, [session, sessionStatus]);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue: SubscriptionContextType = useMemo(() => ({
    status: subscription?.status || 'inactive',
    subscription,
    stripePriceId,
    loading,
    validateSubscription,
    hasAccess,
    remainingUsage,
    refreshUsage
  }), [
    subscription?.status,
    subscription,
    stripePriceId,
    loading,
    validateSubscription,
    hasAccess,
    remainingUsage,
    refreshUsage
  ]);

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}
    </SubscriptionContext.Provider>
  );
};

// Hook to use the subscription context
export const useSubscription = (): SubscriptionContextType => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
}; 