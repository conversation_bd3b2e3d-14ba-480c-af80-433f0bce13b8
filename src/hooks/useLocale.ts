'use client';

import { usePathname } from 'next/navigation';

export function useLocale(): 'en' | 'es' {
  const pathname = usePathname();

  if (pathname?.startsWith('/es')) {
    return 'es';
  }

  return 'en';
}

export function createLocaleUrl(path: string, locale?: 'en' | 'es'): string {
  const currentLocale = locale ?? useLocale();
  
  if (currentLocale === 'es') {
    return path === '/' ? '/es' : `/es${path}`;
  }
  
  return path;
}
