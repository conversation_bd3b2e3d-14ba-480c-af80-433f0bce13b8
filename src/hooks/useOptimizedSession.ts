'use client';

import { useSession } from 'next-auth/react';
import { useMemo } from 'react';

/**
 * Optimized session hook that memoizes session data to prevent unnecessary re-renders
 * across dashboard components
 */
export function useOptimizedSession() {
  const { data: session, status } = useSession();

  // Memoize session data to prevent unnecessary re-renders when session object reference changes
  const optimizedSession = useMemo(() => {
    if (!session) return null;
    
    return {
      user: {
        id: session.user?.id,
        name: session.user?.name,
        email: session.user?.email,
        image: session.user?.image,
        role: session.user?.role,
        subscription: session.user?.subscription
      }
    };
  }, [
    session?.user?.id,
    session?.user?.name,
    session?.user?.email,
    session?.user?.image,
    session?.user?.role,
    session?.user?.subscription?.type,
    session?.user?.subscription?.status,
    session?.user?.subscription?.currentPeriodEnd
  ]);

  // Memoize loading and authentication states
  const isLoading = useMemo(() => status === 'loading', [status]);
  const isAuthenticated = useMemo(() => status === 'authenticated', [status]);
  const isUnauthenticated = useMemo(() => status === 'unauthenticated', [status]);

  return {
    session: optimizedSession,
    status,
    isLoading,
    isAuthenticated,
    isUnauthenticated
  };
}

/**
 * Hook to get user information with memoization
 */
export function useOptimizedUser() {
  const { session } = useOptimizedSession();
  
  return useMemo(() => {
    if (!session?.user) return null;
    
    return {
      id: session.user.id,
      name: session.user.name,
      email: session.user.email,
      image: session.user.image,
      role: session.user.role,
      hasSubscription: Boolean(session.user.subscription),
      subscriptionType: session.user.subscription?.type,
      subscriptionStatus: session.user.subscription?.status
    };
  }, [session?.user]);
}

/**
 * Hook to check authentication status with memoization
 */
export function useAuthStatus() {
  const { status, isLoading, isAuthenticated, isUnauthenticated } = useOptimizedSession();
  
  return useMemo(() => ({
    status,
    isLoading,
    isAuthenticated,
    isUnauthenticated,
    isReady: !isLoading
  }), [status, isLoading, isAuthenticated, isUnauthenticated]);
}
