'use client';

import { useContext, useEffect, useState, useMemo, useCallback } from 'react';
import { SubscriptionContext } from '@/contexts/SubscriptionContext';

// Define the type for the subscription data
type SubscriptionData = any; // Use the actual type from your context if available

// Create a client-side cache for subscription data
let subscriptionCache: {
  data: SubscriptionData | null;
  timestamp: number;
  ttl: number;
} = {
  data: null,
  timestamp: 0,
  ttl: 1000 * 60 * 60 * 24 // 1 day cache
};

/**
 * Hook for accessing subscription status from the SubscriptionContext
 * 
 * @returns {Object} Subscription status information
 * @returns {string} status - Current subscription status ('active', 'canceled', etc.)
 * @returns {Object|null} subscription - Full subscription object with all details
 * @returns {string|null} stripePriceId - Stripe price ID for the subscription
 * @returns {boolean} loading - Whether the subscription data is still loading
 * @returns {Function} validateSubscription - Function to manually validate the subscription status
 * @returns {Function} hasAccess - Function to check if user has access to a specific feature
 * @returns {Function} refreshUsage - Function to refresh usage metrics
 * @returns {Function} remainingUsage - Function to get remaining usage for the current subscription
 */
export function useSubscription() {
  const context = useContext(SubscriptionContext);
  const [isCached, setIsCached] = useState(false);
  
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }

  useEffect(() => {
    // Check if we have valid cached data
    const now = Date.now();
    if (subscriptionCache.data && now - subscriptionCache.timestamp < subscriptionCache.ttl) {
      // Use cached data
      if (!context.subscription && subscriptionCache.data) {
        console.log("Using cached subscription data");
        // We can't directly modify context, use the proper method if available
        // or just use the cached data in the component
        setIsCached(true);
      }
    } else if (!isCached && !context.loading) {
      // Cache the new data
      if (context.subscription) {
        console.log("Caching new subscription data");
        subscriptionCache = {
          data: context.subscription,
          timestamp: now,
          ttl: subscriptionCache.ttl
        };
        setIsCached(true);
      }
    }
  }, [context, isCached, context.subscription, context.loading]);

  // Memoize the validateSubscription function to prevent unnecessary re-renders
  const cachedValidateSubscription = useCallback(async () => {
    const now = Date.now();
    // Only validate if cache is expired or doesn't exist
    if (!subscriptionCache.data || now - subscriptionCache.timestamp >= subscriptionCache.ttl) {
      try {
        const result = await context.validateSubscription();

        // Update cache with new data if result is not null/undefined
        if (result !== null && result !== undefined) {
          subscriptionCache = {
            data: result,
            timestamp: now,
            ttl: subscriptionCache.ttl
          };
        }

        return result;
      } catch (error) {
        console.error("Error validating subscription:", error);
        return subscriptionCache.data; // Return cached data on error
      }
    }

    console.log("Using cached validation result");
    return subscriptionCache.data;
  }, [context.validateSubscription]);

  // Memoize the return object to prevent unnecessary re-renders
  const memoizedValue = useMemo(() => ({
    status: context.status,
    subscription: context.subscription || subscriptionCache.data,
    stripePriceId: context.stripePriceId,
    loading: context.loading,
    validateSubscription: cachedValidateSubscription,
    hasAccess: context.hasAccess,
    refreshUsage: context.refreshUsage,
    remainingUsage: context.remainingUsage
  }), [
    context.status,
    context.subscription,
    context.stripePriceId,
    context.loading,
    context.hasAccess,
    context.refreshUsage,
    context.remainingUsage,
    cachedValidateSubscription
  ]);

  return memoizedValue;
}