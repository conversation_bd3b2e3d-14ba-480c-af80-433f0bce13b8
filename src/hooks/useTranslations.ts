import { useTranslations as useNextIntlTranslations } from 'next-intl';

// Custom hook that provides type-safe translations with fallbacks
export function useTranslations(namespace?: string) {
  try {
    return useNextIntlTranslations(namespace);
  } catch (error) {
    console.warn(`Translation context not available for namespace: ${namespace}`);
    // Return a fallback function that returns the key itself
    return (key: string) => key;
  }
}

// Specific hooks for different sections
export function useCommonTranslations() {
  try {
    return useNextIntlTranslations('common');
  } catch (error) {
    console.warn('Translation context not available for common');
    return (key: string) => key;
  }
}

export function useNavigationTranslations() {
  try {
    return useNextIntlTranslations('navigation');
  } catch (error) {
    console.warn('Translation context not available for navigation');
    return (key: string) => key;
  }
}

export function useHeroTranslations() {
  return useNextIntlTranslations('hero');
}

export function useDashboardTranslations() {
  return useNextIntlTranslations('dashboard');
}

export function usePricingTranslations() {
  return useNextIntlTranslations('pricing');
}

export function useFeaturesTranslations() {
  return useNextIntlTranslations('features');
}

export function useTestimonialsTranslations() {
  return useNextIntlTranslations('testimonials');
}

export function useImageGenerationTranslations() {
  return useNextIntlTranslations('imageGeneration');
}

export function useImageEditingTranslations() {
  return useNextIntlTranslations('imageEditing');
}

export function useAiAssistantTranslations() {
  return useNextIntlTranslations('aiAssistant');
}

export function useSubscriptionTranslations() {
  return useNextIntlTranslations('subscription');
}

export function useVideoGenerationTranslations() {
  return useNextIntlTranslations('videoGeneration');
}

export function usePodcastGenerationTranslations() {
  return useNextIntlTranslations('podcastGeneration');
}

export function useMusicGenerationTranslations() {
  return useNextIntlTranslations('musicGeneration');
}

export function useSpeechToTextTranslations() {
  return useNextIntlTranslations('speechToText');
}

export function useExamplesTranslations() {
  return useNextIntlTranslations('examples');
}

export function useFooterTranslations() {
  try {
    return useNextIntlTranslations('footer');
  } catch (error) {
    console.warn('Translation context not available for footer');
    return (key: string) => key;
  }
}

export function useContactTranslations() {
  return useNextIntlTranslations('contact');
}

export function useAuthTranslations() {
  try {
    return useNextIntlTranslations('auth');
  } catch (error) {
    console.warn('Translation context not available for auth');
    return (key: string) => key;
  }
}
