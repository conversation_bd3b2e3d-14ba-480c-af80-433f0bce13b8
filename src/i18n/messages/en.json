{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "upload": "Upload", "download": "Download", "generate": "Generate", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "copy": "Copy", "copied": "Copied!", "share": "Share", "settings": "Settings", "help": "Help", "logout": "Logout", "login": "<PERSON><PERSON>", "signup": "Sign Up", "getStarted": "Get Started", "learnMore": "Learn More", "tryNow": "Try Now", "startCreating": "Start Creating", "exploreFeatures": "Explore Features"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "imageGeneration": "Image Generation", "imageEditing": "Image Editing", "videoGeneration": "Video Generation", "podcastGeneration": "Podcast Creation", "musicGeneration": "Music Composition", "speechToText": "Speech to Text", "aiAssistant": "AI Assistant", "subscription": "Subscription", "profile": "Profile", "pricing": "Pricing", "features": "Features", "about": "About"}, "hero": {"badge": "✨ AI-Powered Creation Platform", "title": "Create with", "titleHighlight": "AstroStudio AI", "description": "The ultimate AI creation platform. Generate stunning images, captivating videos, professional podcasts, and beautiful music with cutting-edge artificial intelligence.", "benefits": {"multiModel": "Multi-model platform", "privacyFirst": "Privacy-first approach", "professionalQuality": "Professional quality"}, "cta": {"primary": "Start Creating", "secondary": "Explore Features"}, "disclaimer": "No credit card required • Free tier available • Start in seconds"}, "dashboard": {"welcome": "Welcome to", "welcomeHighlight": "AstroStudio AI", "subtitle": "Your all-in-one AI creation platform. Generate images, videos, music, podcasts, and more with cutting-edge artificial intelligence.", "howItWorks": {"title": "How AstroStudio AI Works", "subtitle": "Get started with our AI creation tools in just a few simple steps. No technical expertise required.", "steps": {"choose": {"title": "Choose Your Tool", "description": "Select from our suite of AI creation tools based on what you want to create."}, "describe": {"title": "Describe Your Vision", "description": "Use natural language to describe what you want to create. Be as detailed as you like."}, "create": {"title": "AI Creates", "description": "Our advanced AI models process your request and generate high-quality content."}, "download": {"title": "Download & Use", "description": "Download your creations and use them for your projects, social media, or business."}}}, "features": {"multiModel": {"title": "Multi-Model Platform", "description": "Access multiple AI models for different creative tasks in one unified platform."}, "privacyFirst": {"title": "Privacy First", "description": "Your data stays private. We don't train models with your content or store data permanently."}, "sessionBased": {"title": "Session-Based", "description": "Your information is only stored during active sessions, never permanently on the internet."}}, "tools": {"title": "AI Creation Tools", "description": "Explore our comprehensive suite of AI-powered creation tools. Each tool is designed to help you bring your creative vision to life.", "descriptions": {"imageGeneration": "Create stunning images from text descriptions using advanced AI models", "imageEditing": "Edit and enhance your images with AI-powered tools", "videoGeneration": "Create captivating videos from text prompts", "podcastGeneration": "Generate professional podcast content with AI voices", "musicGeneration": "Compose beautiful music in various styles and genres", "aiAssistant": "Chat with your personal AI assistant for any task"}}, "privacy": {"title": "Your Privacy Matters", "description": "We're committed to protecting your privacy and ensuring your creative work remains yours.", "features": {"noDataTraining": {"title": "No Data Training", "description": "We never use your content to train our AI models. Your creative work stays private and secure."}, "sessionStorage": {"title": "Session-Only Storage", "description": "Your data is only stored during active sessions and automatically deleted when you're done."}, "fullOwnership": {"title": "Full Ownership", "description": "You own all the content you create. Download, modify, and use your creations however you like."}}, "payment": {"successMessage": "Payment successful! Loading your subscription details..."}}}, "pricing": {"title": "Pricing", "subtitle": "Choose the right plan for you", "description": "Start with a free account or upgrade to unlock more features and higher quality outputs.", "mostPopular": "Most popular", "month": "/month", "year": "/year", "processing": "Processing...", "noRegistration": "No registration required. Set up your account after payment.", "plans": {"free": {"name": "Free", "description": "Get a taste of AI creation with our free tier.", "cta": "Sign up for free", "features": {"aiAssistant": "Access to AI assistant", "fluxModel": "Flux Schell free model for image generations", "limitedGenerations": "Limit image generations per day", "basicModels": "Access to basic models", "standardQuality": "Standard output quality"}}, "standard": {"name": "Standard", "description": "Perfect for creative professionals who need reliable AI tools.", "cta": "Get started", "features": {"aiImages": "Generate stunning AI images", "musicTracks": "Create original music tracks", "speechToText": "Transcribe audio to text", "aiWriting": "Get AI writing assistance", "advancedModels": "Access to advanced models", "higherQuality": "Higher quality outputs", "priorityProcessing": "Priority processing"}}, "premium": {"name": "Premium", "description": "For power users who need the full creative suite with video capabilities.", "cta": "Choose Premium", "features": {"everythingStandard": "Everything in Standard plan", "videoGeneration": "AI video generation", "podcasts": "Produce professional podcasts", "higherQuality": "Higher quality outputs", "priorityProcessing": "Priority processing", "longerContent": "Longer content generation", "advancedCustomization": "Advanced customization options", "modelFineTuning": "Custom model fine-tuning"}}}}, "features": {"imageGeneration": {"title": "Image Generation", "description": "Create stunning and unique images from text descriptions with advanced AI models."}, "imageEditing": {"title": "Image Editing", "description": "Edit and enhance your photos with AI-powered tools that make complex edits simple."}, "videoGeneration": {"title": "Video Generation", "description": "Transform text prompts into captivating videos for social media, marketing, or personal projects."}, "podcastCreation": {"title": "Podcast Creation", "description": "Generate professional podcast content with realistic AI voices and audio editing capabilities."}, "musicComposition": {"title": "Music Composition", "description": "Compose beautiful music in various styles and genres with our AI music generation tools."}, "advancedModels": {"title": "Advanced AI Models", "description": "Powered by state-of-the-art AI models to deliver high-quality creative content."}}, "imageGeneration": {"title": "Image Generation", "subtitle": "Create stunning images with AI", "prompt": "Describe the image you want to create...", "negativePrompt": "Describe what you don't want in the image...", "generateButton": "Generate Image", "uploadImage": "Upload Image", "changeImage": "Change Image", "changeImages": "Change Images", "uploadImages": "Upload Images", "selectModel": "Select a model", "imageSize": "Image Size", "numberOfImages": "Number of Images", "advancedSettings": "Advanced Settings", "seed": "Seed", "steps": "Steps", "guidanceScale": "Guidance Scale", "generating": "Generating...", "downloadImage": "Download Image", "saveImage": "Save Image", "copyPrompt": "Copy Prompt", "regenerate": "Regenerate", "freeTierTitle": "Generate Image (Free Tier)", "mainTitle": "Generate Image", "aiImageGeneration": "AI Image Generation", "description": "Describe the image you want to create in detail, or upload an existing image to transform it.", "showDimensions": "Show Dimensions", "hideDimensions": "Hide Dimensions", "showAdvancedOptions": "Show Advanced Options", "hideAdvancedOptions": "Hide Advanced Options", "uploadInstructions": "Press Enter to send, Shift+Enter for new line. Upload an image for image-to-image generation.", "pasteImageSuccess": "Image pasted successfully", "pasteImageDescription": "I've pasted an image for editing:", "subscriptionRequired": "Subscription Required", "subscriptionRequiredDescription": "This feature requires a subscription", "modelDoesNotSupportImageInput": "This model does not support image input", "premiumModelSelected": "Premium Model Selected", "premiumModelDescription": "You'll need a subscription to generate with this model", "viewSubscriptionPlans": "View Subscription Plans", "searchModels": "Search models...", "freeTierLimited": "Free Tier - Limited Options", "freeModel": "Free Model", "premiumModel": "Premium", "fluxSchnellFree": "FLUX <PERSON> (Free)", "freeImageGeneration": "Free Image Generation", "placeholder": {"imageGeneration": "What image do you want to create?"}, "examples": {"photoRealistic": {"title": "Photorealistic portrait", "description": "Generate a detailed human portrait with realistic features"}, "fantasyLandscape": {"title": "Fantasy landscape", "description": "Create an immersive fantasy world"}}, "errors": {"promptRequired": "Please enter a prompt or upload an image", "modelNotSupported": "The selected model does not support image input. Please choose a model that supports image input or switch to text-only generation.", "fileSizeLimit": "Each file size should be less than 10MB", "imageFilesOnly": "Please upload image files only", "generationFailed": "Failed to generate image. Please try again.", "noImagesGenerated": "No images were generated. Please try again or choose a different model.", "failedToSaveImage": "Failed to save image", "failedToCopyText": "Failed to copy text to clipboard.", "failedToFetchImageData": "Failed to fetch image data", "failedToDownloadImage": "Failed to download image", "failedToReadFile": "Failed to read file", "failedToReadImageFile": "Failed to read the image file", "failedToReadImageFiles": "Failed to read the image files"}, "success": {"imageUploaded": "uploaded successfully. Now enter your prompt and send.", "imageGenerated": "Image generated successfully!", "imagesUploaded": "images uploaded successfully. Now enter your prompt and send.", "promptCopied": "Prompt copied to clipboard!"}, "loading": {"uploadingImage": "Uploading image...", "uploadingImages": "Uploading images...", "processingPastedImage": "Processing pasted image..."}}, "videoGeneration": {"title": "Video Generation", "subtitle": "Create amazing videos with AI", "prompt": "Describe the video you want to create...", "negativePrompt": "Describe what you don't want in the video...", "generateButton": "Generate Video", "uploadVideo": "Upload Video", "uploadImage": "Upload Image", "selectModel": "Select Model", "resolution": "Resolution", "duration": "Duration", "mode": "Mode", "modes": {"textToVideo": "Text to Video", "imageToVideo": "Image to Video", "upscaleVideo": "Upscale Video", "audioVideo": "Add Audio to Video"}, "generating": "Generating video...", "downloadVideo": "Download Video", "copyPrompt": "Copy Prompt", "mainTitle": "Generate Video", "aiVideoGeneration": "AI Video Generation", "description": "Describe the video you want to create, or upload an image/video to transform it.", "modeDescriptions": {"textToVideo": "Generate videos from text prompts", "imageToVideo": "Animate still images into videos", "upscaleVideo": "Enhance resolution and quality", "audioVideo": "Generate audio for videos"}, "placeholders": {"textToVideo": "Describe the video you want to create...", "imageToVideo": "Describe how the image should animate...", "upscaleVideo": "Upload a video to upscale...", "audioVideo": "Describe the audio you want to generate..."}, "examples": {"cinematicScene": {"title": "Cinematic scene", "description": "Generate a movie-like video sequence"}, "natureTimelapse": {"title": "Nature time-lapse", "description": "Create beautiful nature animations"}}, "errors": {"promptRequired": "Please enter a prompt", "videoRequired": "Please upload a video to upscale", "videoForAudioRequired": "Please upload a video to add audio to", "generationFailed": "Failed to generate video. Please try again.", "invalidImageFile": "Please select a valid image file", "invalidVideoFile": "Please select a valid video file", "fileSizeTooLarge": "File size must be less than 10MB", "failedToCopyText": "Failed to copy text to clipboard."}, "success": {"promptCopied": "Prompt copied to clipboard!"}}, "musicGeneration": {"title": "Music Generation", "subtitle": "Compose beautiful music with AI", "prompt": "Describe the music you want to create...", "tags": "Tags (optional)", "generateButton": "Generate Music", "uploadAudio": "Upload Audio", "selectModel": "Select Model", "duration": "Duration", "quality": "Quality", "mode": "Mode", "modes": {"textToMusic": "Text to Music", "audioToMusic": "Audio to Music", "extendMusic": "Extend Music", "remixMusic": "Remix Music"}, "generating": "Generating music...", "downloadMusic": "Download Music", "playMusic": "Play Music", "pauseMusic": "Pause Music", "aiMusicGeneration": "AI Music Generation", "description": "Describe the music you want to create, or upload audio to transform it.", "subscriptionRequired": "Subscription Required", "subscriptionRequiredDescription": "Upgrade your plan to access AI music generation features.", "upgradeSubscription": "Upgrade Subscription", "modeDescriptions": {"textToMusic": "Generate music from text descriptions", "audioToMusic": "Transform audio into music", "extendMusic": "Extend existing music", "remixMusic": "Remix and transform music"}, "placeholders": {"textToMusic": "Describe the music you want to generate... (e.g., \"Upbeat electronic dance music with heavy bass\")", "audioToMusic": "Describe how you want to transform the uploaded audio...", "extendMusic": "Describe how you want to continue the uploaded music...", "remixMusic": "Describe the style or changes you want to make...", "lyrics": "Write the lyrics for your song...", "tags": "Enter tags separated by commas (e.g., pop, upbeat, electronic)"}, "examples": {"electronicDance": {"title": "Electronic dance", "description": "Create energetic EDM with catchy beats"}, "acousticRelaxation": {"title": "Acoustic relaxation", "description": "Generate calming acoustic music"}}, "errors": {"promptRequired": "Please enter a prompt", "generationFailed": "Failed to generate music. Please try again.", "subscriptionRequired": "Please upgrade your subscription to generate music", "audioFileRequired": "Please select an audio file", "musicDescriptionRequired": "Please enter a description for your music"}, "success": {"musicGenerated": "Music generated successfully!"}, "loading": {"generatingMusic": "Generating music..."}}, "podcastGeneration": {"title": "Podcast Generation", "subtitle": "Create professional podcasts with AI voices", "prompt": "Enter your podcast content...", "generateButton": "Generate Podcast", "selectModel": "Select Model", "selectVoice": "Select Voice", "createVoice": "Create Voice", "cloneVoice": "<PERSON><PERSON> Voice", "voiceDescriptions": {"aurora": "<PERSON> is a young, friendly, and bubbly female voice. Perfect for children's content, commercials, and upbeat narration with an energetic and cheerful tone.", "blade": "<PERSON> is a strong, confident male voice with a modern edge. Ideal for action content, gaming, and dynamic presentations requiring authority and intensity.", "britney": "<PERSON><PERSON><PERSON> is a vibrant, youthful female voice with contemporary appeal. Great for lifestyle content, social media, and trendy commercial applications.", "carl": "<PERSON> is a warm, reliable male voice with a professional tone. Perfect for corporate content, educational material, and trustworthy narration.", "cliff": "<PERSON> is a mature, authoritative male voice with gravitas. Excellent for documentaries, serious content, and professional presentations requiring credibility.", "rico": "<PERSON> is a charismatic, smooth male voice with Latin flair. Ideal for entertainment content, storytelling, and engaging conversational formats.", "vicky": "<PERSON> is a sophisticated, articulate female voice with professional clarity. Perfect for business content, news, and formal presentations.", "richard": "<PERSON> is a distinguished, experienced male voice with refined delivery. Excellent for luxury brands, premium content, and sophisticated narration.", "aria": "<PERSON> is a husky, middle-aged female American voice with African-American accent. Perfect for soulful narration, storytelling, and content requiring warmth and authenticity.", "rachel": "<PERSON> is a calm, young female American voice with clear articulation. Ideal for narration, educational content, and professional presentations requiring clarity and trust.", "drew": "<PERSON> is a well-rounded, middle-aged male American voice with authoritative tone. Perfect for news, documentaries, and corporate content requiring credibility and professionalism.", "clyde": "<PERSON> is a distinctive male American voice with character depth and gravitas. Excellent for storytelling, dramatic content, and narratives requiring emotional resonance.", "paul": "<PERSON> is an authoritative, middle-aged male American voice with commanding presence. Ideal for news broadcasting, corporate presentations, and content requiring strong leadership tone.", "domi": "<PERSON><PERSON> is a strong, young female American voice with confident delivery. Perfect for dynamic narration, motivational content, and presentations requiring energy and conviction.", "sarah": "<PERSON> is a confident, warm female voice with mature quality and reassuring professional tone. Excellent for business content, training materials, and trustworthy communication.", "dave": "<PERSON> is a conversational, young male British voice with friendly charm. Ideal for character work, casual content, and engaging storytelling with approachable personality.", "jennifer": "<PERSON> is a young female American voice with clear, conversational tone. Perfect for dialog, customer service content, and friendly communication requiring approachability.", "furio": "<PERSON><PERSON> is a distinctive male Italian voice with character and warmth. Ideal for storytelling, character work, and content requiring Mediterranean charm and personality.", "dexter": "<PERSON> is a middle-aged male American voice with professional clarity. Excellent for corporate content, training materials, and presentations requiring reliability.", "charlotte": "<PERSON> is a professional female Canadian voice with advertising appeal. Perfect for commercials, marketing content, and promotional materials requiring polished delivery.", "ava": "<PERSON> is a vibrant female Australian voice with energetic personality. Ideal for lifestyle content, entertainment, and engaging presentations with international appeal.", "cecil": "<PERSON> is a distinguished male British voice with refined articulation. Excellent for formal content, documentaries, and presentations requiring sophistication and authority.", "cillian": "<PERSON><PERSON><PERSON> is a charming male Irish voice with warm, conversational tone. Perfect for storytelling, character work, and content requiring Celtic charm and authenticity.", "alessandro": "<PERSON> is a sophisticated male Italian voice with elegant delivery. Ideal for luxury content, cultural presentations, and narratives requiring European sophistication.", "carmenMexican": "<PERSON> is a warm female Mexican voice with engaging personality. Perfect for multicultural content, storytelling, and presentations requiring Latin American authenticity.", "inara": "<PERSON><PERSON> is a professional female voice with clear, articulate delivery. Excellent for business content, educational materials, and presentations requiring clarity and professionalism.", "kiriko": "<PERSON><PERSON><PERSON> is a gentle female Japanese voice with warm, conversational tone. Ideal for cultural content, storytelling, and presentations requiring Asian authenticity and grace.", "sterling": "<PERSON> is an elderly male British voice with distinguished narrative quality. Perfect for documentaries, historical content, and storytelling requiring gravitas and wisdom.", "dohee": "<PERSON><PERSON> is a professional female Korean voice with clear, engaging delivery. Excellent for educational content, presentations, and multicultural communication.", "carmenSpanish": "<PERSON> is an articulate female Spanish voice with professional clarity. Ideal for business content, educational materials, and formal presentations in Spanish markets.", "patricia": "<PERSON> is a mature female Spanish voice with warm, professional tone. Perfect for corporate content, training materials, and presentations requiring authority and trust.", "caroline": "<PERSON> is a friendly female Brazilian voice with warm, conversational delivery. Excellent for lifestyle content, customer service, and engaging communication.", "madison": "<PERSON> is a professional female Irish voice with clear, articulate tone. Ideal for business content, presentations, and formal communication requiring Celtic professionalism.", "baptiste": "<PERSON> is a sophisticated male French voice with refined delivery. Perfect for luxury content, cultural presentations, and narratives requiring European elegance.", "andrei": "<PERSON> is a strong male Russian voice with authoritative presence. Excellent for dramatic content, documentaries, and presentations requiring gravitas and intensity.", "ada": "<PERSON> is a distinctive female South African voice with warm, engaging personality. Ideal for multicultural content, storytelling, and presentations requiring African authenticity.", "charlotteMeditation": "<PERSON> is a calming female Canadian voice with soothing, meditative quality. Perfect for wellness content, guided meditations, and relaxation materials."}, "generating": "Generating podcast...", "downloadPodcast": "Download Podcast", "playPodcast": "Play Podcast", "pausePodcast": "Pause Podcast", "placeholders": {"dialog": "Enter dialog format with Voice1: and Voice2: prefixes. Example:\nVoice1: Hey, did you catch the game last night?\nVoice2: Of course! What a match—it had me on the edge of my seat.\nVoice1: Same here! That last-minute goal was unreal.", "narration": "Enter text for single-speaker narration. Example: Hello! This is a test of the text to speech system, powered by ElevenLabs. How does it sound?", "default": "Enter the content for your podcast..."}, "aiPodcastGeneration": "AI Podcast Generation", "description": "Describe the podcast you want to create. Choose your model and voices below.", "subscriptionRequired": "Subscription Required", "subscriptionRequiredDescription": "Upgrade your plan to access AI podcast generation features.", "upgradeSubscription": "Upgrade Subscription", "aiAssistant": "AI Assistant", "examples": {"techDebate": {"title": "Tech Debate", "description": "Create a discussion between experts about AI"}, "educationalContent": {"title": "Educational Content", "description": "Generate informative podcasts on any topic", "prompt": "Create a 5-minute educational podcast about space exploration with engaging narration"}, "conversationalDialog": {"title": "Conversational Dialog", "description": "Multi-speaker discussions", "prompt": "Voice1: Hey, did you catch the game last night? Voice2: Of course! What a match—it had me on the edge of my seat. Voice1: Same here! That last-minute goal was unreal. Who's your MVP? Voice2: <PERSON> be the goalie. Those saves were unbelievable. Voice1: Absolutely. Saved the day, literally! Are you planning to watch the next game? Voice2: Oh, you bet. I'm already stocked up on snacks! Voice1: Can't wait! Let's catch up after the game."}}, "errors": {"promptRequired": "Please enter podcast content", "generationFailed": "Failed to generate podcast. Please try again.", "requestRequired": "Please enter a request", "failedToGenerateContent": "Failed to generate content", "failedToPlayVoicePreview": "Failed to play voice preview", "noAudioUrlAvailable": "No audio URL available to download.", "failedToGeneratePodcast": "Failed to generate podcast:"}, "success": {"contentGenerated": "Content generated successfully!", "downloadStarted": "Download started!"}, "loading": {"generating": "Generating...", "generatingContent": "Generate Content"}, "buttons": {"download": "Download", "useThisContent": "Use This Content"}}, "imageEditing": {"editImage": "Edit Image", "upscaleImage": "Upscale Image", "aiImageEditing": "AI Image Editing", "description": "Upload an image and describe how you want to edit it.", "uploadImage": "Upload Image", "uploadInstructions": "Drag and drop an image here, or click to select", "changeImage": "Change Image", "subscriptionRequired": "Subscription Required", "subscriptionRequiredDescription": "Premium models require a subscription", "premiumModelSelected": "Premium Model Selected", "premiumModelDescription": "You'll need a subscription to use this model", "viewSubscriptionPlans": "View Subscription Plans", "showAdvancedOptions": "Show Advanced Options", "hideAdvancedOptions": "Hide Advanced Options", "negativePrompt": "Negative Prompt", "negativePromptPlaceholder": "What you don't want in the image...", "examples": {"backgroundRemoval": {"title": "Background removal", "description": "Remove backgrounds from images"}, "imageUpscaling": {"title": "Image upscaling", "description": "Enhance image resolution and quality"}}, "errors": {"uploadImageFirst": "Please upload an image first", "enterPromptForModel": "Please enter a prompt for this model", "imageFileOnly": "Please upload an image file", "fileSizeLimit": "File size should be less than 10MB", "failedToReadFile": "Failed to read the image file", "failedToProcessImage": "Failed to process image. Please try again.", "noImageInResponse": "No image URL in response", "failedToSaveImage": "Failed to save image", "failedToCopyText": "Failed to copy text to clipboard.", "failedToFetchImageData": "Failed to fetch image data", "failedToDownloadImage": "Failed to download image. Please try again."}, "success": {"imageUploaded": "Image uploaded successfully", "imagePasted": "Image pasted successfully", "promptCopied": "Prompt copied to clipboard!"}, "loading": {"uploadingImage": "Uploading image...", "processingPastedImage": "Processing pasted image..."}, "studio": {"title": "Image Editing Studio", "uploadInstructions": "Upload an image using the upload button in the message box below to start editing. You can remove backgrounds, change objects, add effects, and more.", "pasteInstructions": "You can also paste an image from clipboard (Ctrl+V / Cmd+V)", "uploadFirstMessage": "Please upload an image first before providing instructions.", "placeholderWithImage": "Describe how you want to edit this image...", "placeholderWithoutImage": "Upload an image first to start editing"}}, "speechToText": {"title": "Speech to Text", "subtitle": "Convert audio to text with AI", "uploadAudio": "Upload Audio", "recordAudio": "Record Audio", "startRecording": "Start Recording", "stopRecording": "Stop Recording", "transcribeButton": "Transcribe", "selectModel": "Select Model", "language": "Language", "format": "Output Format", "transcribing": "Transcribing...", "downloadTranscript": "Download Transcript", "copyTranscript": "Copy Transcript", "description": "Convert speech to text with advanced AI models. Upload audio files or record directly.", "subscriptionRequired": "Subscription Required", "subscriptionRequiredDescription": "Speech to Text requires a Standard or Premium subscription", "viewSubscriptionPlans": "View Subscription Plans", "goBack": "Go Back", "modelSelection": "Model Selection", "autoDetectLanguage": "Auto-detect language", "outputFormat": "Output Format", "uploadAudioFile": "Upload Audio File", "supportedFormats": "MP3, WAV, M4A, FLAC, up to 25MB", "transcribeAudio": "Transcribe Audio", "processing": "Processing...", "errors": {"audioRequired": "Please upload or record an audio file first", "transcriptionFailed": "Failed to transcribe audio. Please try again.", "failedToStartRecording": "Failed to start recording. Please check your microphone permissions.", "uploadOrRecordFirst": "Please upload or record an audio file first"}, "success": {"transcriptionComplete": "Transcription completed successfully!"}, "loading": {"loading": "Loading..."}}, "aiAssistant": {"title": "AI Assistant", "subtitle": "Chat with your personal AI assistant", "placeholder": "Type your message...", "sendButton": "Send", "clearChat": "Clear Chat", "selectModel": "Select Model", "thinking": "Thinking...", "modelCategories": {"availableModels": "Available Models"}, "modelSelector": {"modelSelection": "Model Selection", "selectAModel": "Select a model", "noProvider": "No provider", "selected": "Selected"}, "sidebar": {"hideSidebar": "Hide sidebar", "showSidebar": "Show sidebar", "conversations": "Conversations", "noConversations": "No conversations yet", "currentModel": "Current model:", "selectAModel": "Select a model"}, "options": {"hide": "<PERSON>de", "show": "Show", "options": "Options", "advancedOptions": "Advanced Options", "temperature": "Temperature", "maxTokens": "<PERSON>"}, "conversation": {"newConversation": "New Conversation", "deleteConversation": "Delete conversation", "downloadConversation": "Download conversation", "download": "Download"}, "welcome": {"subtitle": "Ask questions, get help with tasks, or engage in creative conversations with your AI assistant.", "examples": {"professionalWriting": "Professional Writing", "professionalWritingPrompt": "Help me write a professional email", "learningEducation": "Learning & Education", "learningEducationPrompt": "Explain quantum computing in simple terms", "creativeWriting": "Creative Writing", "creativeWritingPrompt": "Write a creative short story about space exploration", "codeAssistance": "Code Assistance", "codeAssistancePrompt": "Help me debug this Python code"}}, "input": {"instructions": "Press Enter to send, Shift+Enter for new line", "copyMessage": "Copy message", "sendMessage": "Send message", "messageInput": "Message input"}, "mock": {"banner": "Running in Mock AI mode. Set up API keys in your .env file to use real AI models.", "response": "<PERSON><PERSON>", "warning": "⚠️ Using Mock AI mode because API keys aren't configured. Please set up TOGETHER_API_KEY and REPLICATE_API_KEY in your .env file."}, "errors": {"messageRequired": "Please enter a message", "chatFailed": "Failed to send message. Please try again.", "copyFailed": "Failed to copy text to clipboard."}}, "subscription": {"title": "Subscription Management", "currentPlan": "Current Plan", "billingCycle": "Billing Cycle", "nextBilling": "Next Billing", "manageSubscription": "Manage Subscription", "cancelSubscription": "Cancel Subscription", "upgradeSubscription": "Upgrade Subscription", "customerPortal": "Customer Portal", "loading": "Loading subscription details...", "subtitle": "Choose a subscription plan that works for your creative needs", "subscriptionPlans": "Subscription Plans", "selectPlan": "Select a plan to access premium features", "currentSubscription": "Current Subscription", "manageCurrentPlan": "Manage your current plan", "status": "Status", "planType": "Plan Type", "subscriptionId": "Subscription ID", "opening": "Opening...", "refreshStatus": "Refresh Status", "manageSubscriptionInfo": "Click \"Manage Subscription\" to access the Stripe customer portal where you can update payment methods, download invoices, and cancel your subscription.", "subscriptionManagement": "Subscription Management", "accessBillingHistory": "Access your billing history and payment methods", "accessBillingPortal": "Access Billing Portal", "billingPortalInfo": "Access your billing history, download invoices, and manage payment methods even without an active subscription.", "planFeatures": "Plan Features", "standardPlan": "Standard Plan", "standardDescription": "Perfect for creative professionals who need reliable AI tools.", "premiumPlan": "Premium Plan", "premiumDescription": "For power users who need the full creative suite with video capabilities.", "loadingSubscription": "Loading subscription information...", "features": {"generateStunningImages": "Generate stunning AI images", "createOriginalMusic": "Create original music tracks", "transcribeAudio": "Transcribe audio to text", "aiWritingAssistance": "Get AI writing assistance", "everythingStandard": "Everything in Standard plan", "aiVideoGeneration": "AI video generation", "professionalPodcasts": "Produce professional podcasts", "higherQuality": "Higher quality outputs", "priorityProcessing": "Priority processing", "longerContent": "Longer content generation", "advancedCustomization": "Advanced customization options"}, "subscriptionManager": {"validatingSubscription": "Validating subscription...", "loadingOptions": "Loading subscription options...", "paymentConfigError": "Payment Configuration Error", "errorLoadingPrices": "There was an error loading the subscription prices. Please try refreshing the page or contact support.", "refreshPage": "Refresh Page", "recommended": "Recommended", "currentPlan": "Current Plan", "subscribe": "Subscribe", "processing": "Processing...", "subscriptionCanceled": "Your subscription has been canceled", "failedToSubscribe": "Failed to start subscription. Please try again.", "failedToCancel": "Failed to cancel subscription. Please try again.", "invalidPlan": "Invalid plan selected", "priceNotConfigured": "Price ID not configured for this plan", "noCheckoutUrl": "No checkout URL received", "failedToCreateCheckout": "Failed to create checkout session", "failedToCancelInternal": "Failed to cancel subscription"}, "errors": {"loadFailed": "Failed to load subscription details", "portalFailed": "Failed to open customer portal", "unableToAccess": "Unable to access subscription management. Please log in again.", "failedToCreate": "Failed to create portal session", "noPortalUrl": "No portal URL received", "failedToAccess": "Failed to access subscription management. Please try again."}}, "testimonials": {"title": "What Our Users Say", "subtitle": "Discover how AstroStudio AI is transforming creative workflows", "testimonial1": {"content": "AstroStudio AI has completely revolutionized my creative process! I was spending days trying to create professional visuals for my clients, but now I can generate stunning, publication-ready images in minutes. The AI understands my artistic vision perfectly.", "author": "<PERSON>", "role": "Digital Artist & Creative Director"}, "testimonial2": {"content": "The music generation capabilities of AstroStudio AI are simply mind-blowing. I've used the compositions for my YouTube channel background music, and make original songs for hobbie. The quality is indistinguishable.", "author": "<PERSON><PERSON>", "role": "Content Creator & Artist"}, "testimonial3": {"content": "As a small business owner, AstroStudio AI has been a game-changer for my marketing efforts. I can create professional-quality promotional videos and social media content without hiring expensive agencies. It's like having an entire creative team at my fingertips!", "author": "<PERSON>", "role": "Small Business Owner"}}, "auth": {"login": {"title": "Welcome back", "subtitle": "Sign in to your account to continue", "email": "Email address", "emailLabel": "Email address", "emailPlaceholder": "Email address", "password": "Password", "passwordLabel": "Password", "passwordPlaceholder": "Password", "forgotPassword": "Forgot your password?", "signInButton": "Sign in with <PERSON><PERSON>", "signInWithGoogle": "Sign in with Google", "signingIn": "Signing in...", "connecting": "Connecting...", "orContinueWith": "Or continue with", "noAccount": "Don't have an account?", "signUpInstead": "Create one now", "createAccount": "Create one now", "signInSuccess": "Sign in successful", "success": "Successfully signed in! Redirecting..."}, "signup": {"title": "Create an account", "subtitle": "Join AstroStudio AI to start creating amazing content", "signInInstead": "Sign in instead", "name": "Full name", "nameLabel": "Full name", "namePlaceholder": "Full name", "email": "Email address", "emailLabel": "Email address", "emailPlaceholder": "Email address", "password": "Password (min 8 characters)", "passwordLabel": "Password", "passwordPlaceholder": "Password (min 8 characters)", "confirmPassword": "Confirm password", "confirmPasswordLabel": "Confirm Password", "confirmPasswordPlaceholder": "Confirm password", "signUpButton": "Create Account", "createAccountButton": "Create Account", "signingUp": "Creating account...", "signUpWithGoogle": "Google", "processing": "Processing...", "orContinueWith": "Or continue with", "hasAccount": "Already have an account?", "accountCreated": "Account created successfully! Redirecting to login...", "success": "Account created successfully! Redirecting to login...", "passwordsNoMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters long"}, "forgotPassword": {"title": "Forgot your password?", "subtitle": "Enter your email address and we'll send you a link to reset your password", "email": "Email", "sendResetLink": "Send reset link", "sending": "Sending...", "backToLogin": "Back to login", "success": "Password reset link sent! Check your email for instructions.", "errors": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "userNotFound": "If an account with this email exists, you will receive a password reset link", "rateLimited": "Too many reset attempts. Please wait before trying again", "sendFailed": "Failed to send reset email. Please try again later"}}, "resetPassword": {"title": "Reset your password", "subtitle": "Enter your new password below", "validating": "Validating your reset token...", "newPassword": "New password", "confirmPassword": "Confirm new password", "resetButton": "Reset password", "resetting": "Resetting...", "backToLogin": "Back to login", "success": "Password reset successfully! You can now log in with your new password.", "errors": {"passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordMismatch": "Passwords do not match", "tokenInvalid": "Invalid or expired reset token", "tokenExpired": "Reset token has expired. Please request a new password reset", "resetFailed": "Failed to reset password. Please try again"}}, "errors": {"loginFailed": "Failed to login", "signupFailed": "Registration failed", "googleLoginFailed": "Failed to login with Google", "googleSignupFailed": "Failed to sign up with Google", "invalidCredentials": "Invalid credentials", "userExists": "User already exists", "networkError": "Network error", "unknownError": "Unknown error", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "nameRequired": "Name is required", "passwordTooShort": "Password must be at least 8 characters", "passwordMismatch": "Passwords do not match", "emailExists": "An account with this email already exists", "accountNotFound": "No account found with this email address", "tooManyAttempts": "Too many login attempts. Please try again later"}}, "footer": {"description": "Create amazing images, videos, podcasts, and music with the power of AI.", "location": "Our Location: 7901 4th St N STE 300, St. Petersburg, FL 33702", "sections": {"tools": "Tools", "resources": "Resources", "company": "Company"}, "links": {"features": "Features", "pricing": "Pricing", "signup": "Sign Up", "login": "<PERSON><PERSON>", "blog": "Blog", "documentation": "Documentation", "support": "Support", "aboutUs": "About Us", "contact": "Contact", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}, "copyright": "AstroStudio AI. All rights reserved."}, "contact": {"title": "Get in Touch", "subtitle": "Have questions? We'd love to hear from you.", "name": "Name", "email": "Email", "message": "Message", "sendMessage": "Send Message", "sending": "Sending...", "success": "Message sent successfully!", "error": "Failed to send message. Please try again."}, "examples": {"title": "Examples", "subtitle": "See our AI models in action", "description": "Experience the quality and capabilities of our AI models with these interactive examples.", "input": "Input", "output": "Output", "imageGeneration": {"title": "Image Generation", "description": "Create high-quality images with our free AI model"}, "videoGeneration": {"title": "Image to Video", "description": "Transform static images into dynamic videos"}, "musicGeneration": {"title": "Music Generation", "description": "Create original music with AI"}, "podcastGeneration": {"title": "Podcast Creation", "description": "Generate podcast content with AI voices"}, "imageUpscaling": {"title": "Image Upscaling", "description": "Enhance image quality and resolution"}, "videoUpscaling": {"title": "Video Upscaling", "description": "Improve video quality and resolution"}, "sampleTitles": {"sample1": "Sample 1", "sample2": "Sample 2", "example1": "Example 1", "example2": "Example 2", "podcast1": "Podcast 1", "podcast2": "Podcast 2"}}, "voiceModals": {"create": {"title": "Create AI Voice", "steps": {"design": "Design Voice", "preview": "Preview", "complete": "Complete"}, "voiceName": "Voice Name", "gender": "Gender", "accent": "Accent", "age": "Age", "clarity": "Clarity", "stability": "Stability", "presets": "Voice Presets", "generating": "Generating voice...", "success": "Voice created successfully!", "error": "Failed to create voice"}, "clone": {"title": "<PERSON><PERSON> Voice", "steps": {"upload": "Upload Audio", "review": "Review", "complete": "Complete"}, "voiceName": "Voice Name", "uploadAudio": "Upload Audio Sample", "processing": "Processing voice...", "success": "Voice cloned successfully!", "error": "Failed to clone voice"}}, "legal": {"terms": {"title": "Terms of Service", "effectiveDate": "Effective Date:", "sections": {"acceptance": {"title": "1. Acceptance of Terms", "content": "By accessing or using AstroStudio AI (\"Service\"), you agree to be bound by these Terms of Service (\"Terms\"). If you disagree with any part of these terms, you may not access the Service."}, "description": {"title": "2. Description of Service", "content": "AstroStudio AI provides artificial intelligence-powered content generation services, including:", "list": ["Image generation and editing", "Video creation and processing", "Music composition and audio generation", "Podcast creation and voice synthesis", "AI assistant and chat capabilities"]}, "userAccounts": {"title": "3. User Accounts", "content": "To access certain features of the Service, you must create an account. You are responsible for maintaining the confidentiality of your account and password, and for all activities that occur under your account."}, "payment": {"title": "4. Payment and Refund Policy", "importantTitle": "IMPORTANT: NO REFUNDS AVAILABLE", "importantContent": "All payments for subscriptions and services are final and non-refundable. This includes but is not limited to:", "list": ["Monthly and annual subscription fees", "One-time purchases and credits", "Unused portions of subscription periods", "Services not used or partially used"], "additional": "By making a payment, you acknowledge and agree to this no-refund policy."}, "acceptableUse": {"title": "5. Acceptable Use", "content": "You agree not to use the Service to:", "list": ["Generate illegal, harmful, or offensive content", "Violate any applicable laws or regulations", "Infringe on intellectual property rights", "Create content that impersonates others without consent", "Generate spam, malware, or malicious content", "Attempt to reverse engineer or exploit our systems"]}, "contentOwnership": {"title": "6. Content Ownership and Rights", "content": "Regarding content created using our Service:", "list": ["You retain ownership of content you create", "You grant us a license to process and store your content as necessary to provide the Service", "You are responsible for ensuring you have rights to any input content", "Generated content may not be unique and similar content may be created for other users"]}, "disclaimers": {"title": "7. <PERSON><PERSON><PERSON>", "content": "The Service is provided \"as is\" without warranties of any kind. We do not guarantee that the Service will be uninterrupted, error-free, or meet your specific requirements."}, "limitation": {"title": "8. Limitation of Liability", "content": "In no event shall we be liable for any indirect, incidental, special, consequential, or punitive damages, including but not limited to loss of profits, data, or use."}, "termination": {"title": "9. Termination", "content": "We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms."}, "changes": {"title": "10. Changes to Terms", "content": "We reserve the right to modify these Terms at any time. We will notify users of material changes by posting the updated Terms on our website. Your continued use of the Service after such changes constitutes acceptance of the new Terms."}, "governingLaw": {"title": "11. Governing Law", "content": "These Terms shall be governed by and construed in accordance with the laws of the State of Florida, United States, without regard to its conflict of law provisions."}, "contact": {"title": "12. Contact Information", "content": "If you have any questions about these Terms, please contact us at:", "email": "Email: <EMAIL>", "address": "Address: 7901 4th St N STE 300, St. Petersburg, FL 33702"}}}, "privacy": {"title": "Privacy Policy", "effectiveDate": "Effective Date:", "sections": {"informationCollection": {"title": "1. Information We Collect", "content": "We collect information you provide directly to us, such as when you:", "list": ["Create an account or subscribe to our services", "Use our AI generation tools (images, videos, music, podcasts)", "Contact us for support or inquiries", "Participate in surveys or promotional activities"], "additional": "This may include your name, email address, payment information, and content you create or upload."}, "informationUse": {"title": "2. How We Use Your Information", "content": "We use the information we collect to:", "list": ["Provide, maintain, and improve our AI generation services", "Process payments and manage subscriptions", "Send you technical notices and support messages", "Respond to your comments and questions", "Analyze usage patterns to enhance user experience"]}, "informationSharing": {"title": "3. Information Sharing", "content": "We do not sell, trade, or otherwise transfer your personal information to third parties, except:", "list": ["With service providers who assist us in operating our business", "When required by law or to protect our rights", "In the event of a merger, acquisition, or asset sale"]}, "dataRetention": {"title": "4. Data Retention", "content": "We retain your personal information only for as long as necessary for the purposes outlined in this policy. Generated content is stored temporarily during your session and automatically deleted."}, "dataSecurity": {"title": "5. Data Security", "content": "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure."}, "userRights": {"title": "6. Your Rights", "content": "You have the right to:", "list": ["Access and update your personal information", "Delete your account and associated data", "Opt out of marketing communications", "Request a copy of your data", "Lodge a complaint with relevant authorities"]}, "cookies": {"title": "7. Cookies and Tracking Technologies", "content": "We use cookies and similar technologies to enhance your experience, analyze site usage, and personalize content. You can control cookies through your browser settings."}, "childrensPrivacy": {"title": "8. Children's Privacy", "content": "Our services are not intended for children under 13. We do not knowingly collect personal information from children under 13. If you believe we have collected such information, please contact us immediately."}, "policyChanges": {"title": "9. Changes to This Policy", "content": "We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new policy on this page and updating the effective date."}, "contact": {"title": "10. Contact Us", "content": "If you have any questions about this Privacy Policy, please contact us at:", "email": "Email: <EMAIL>", "address": "Address: 7901 4th St N STE 300, St. Petersburg, FL 33702"}}}}}