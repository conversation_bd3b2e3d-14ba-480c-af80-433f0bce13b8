{"common": {"loading": "Cargando...", "error": "Error", "success": "Éxito", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "upload": "Subir", "download": "<PERSON><PERSON><PERSON>", "generate": "Generar", "back": "Atrás", "next": "Siguient<PERSON>", "previous": "Anterior", "close": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "copied": "¡Copiado!", "share": "Compartir", "settings": "Configuración", "help": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "login": "In<PERSON><PERSON>", "signup": "Registrarse", "getStarted": "Comenzar", "learnMore": "Saber más", "tryNow": "<PERSON><PERSON> ahora", "startCreating": "Comenzar a crear", "exploreFeatures": "Explorar funciones"}, "navigation": {"home": "<PERSON><PERSON>o", "dashboard": "Panel", "imageGeneration": "Generación de imágenes", "imageEditing": "Edición de imágenes", "videoGeneration": "Generación de videos", "podcastGeneration": "Creación de podcasts", "musicGeneration": "Composición musical", "speechToText": "Voz a texto", "aiAssistant": "Asistente IA", "subscription": "Suscripción", "profile": "Perfil", "pricing": "<PERSON><PERSON><PERSON>", "features": "Funciones", "about": "Acerca de"}, "hero": {"badge": "✨ Plataforma de creación con IA", "title": "<PERSON><PERSON> con", "titleHighlight": "AstroStudio AI", "description": "La plataforma definitiva de creación con IA. Genera imágenes impresionantes, videos cautivadores, podcasts profesionales y música hermosa con inteligencia artificial de vanguardia.", "benefits": {"multiModel": "Plataforma multi-modelo", "privacyFirst": "Enfoque de privacidad primero", "professionalQuality": "Calidad profesional"}, "cta": {"primary": "Comenzar a crear", "secondary": "Explorar funciones"}, "disclaimer": "No se requiere tarjeta de crédito • Nivel gratuito disponible • Comienza en segundos"}, "dashboard": {"welcome": "Bienvenido a", "welcomeHighlight": "AstroStudio AI", "subtitle": "Tu plataforma todo-en-uno de creación con IA. Genera imágenes, videos, música, podcasts y más con inteligencia artificial de vanguardia.", "howItWorks": {"title": "Cómo funciona AstroStudio AI", "subtitle": "Comienza con nuestras herramientas de creación con IA en solo unos simples pasos. No se requiere experiencia técnica.", "steps": {"choose": {"title": "Elige tu herramienta", "description": "Selecciona de nuestro conjunto de herramientas de creación con IA basado en lo que quieres crear."}, "describe": {"title": "Describe tu visión", "description": "Usa lenguaje natural para describir lo que quieres crear. Sé tan detallado como quieras."}, "create": {"title": "La IA crea", "description": "Nuestros modelos avanzados de IA procesan tu solicitud y generan contenido de alta calidad."}, "download": {"title": "Descarga y usa", "description": "Descarga tus creaciones y úsalas para tus proyectos, redes sociales o negocio."}}}, "features": {"multiModel": {"title": "Plataforma multi-modelo", "description": "Accede a múltiples modelos de IA para diferentes tareas creativas en una plataforma unificada."}, "privacyFirst": {"title": "Privacidad primero", "description": "Tus datos se mantienen privados. No entrenamos modelos con tu contenido ni almacenamos datos permanentemente."}, "sessionBased": {"title": "Basado en sesiones", "description": "Tu información solo se almacena durante sesiones activas, nunca permanentemente en internet."}}, "tools": {"title": "Herramientas de creación con IA", "description": "Explora nuestro conjunto completo de herramientas de creación impulsadas por IA. Cada herramienta está diseñada para ayudarte a dar vida a tu visión creativa.", "descriptions": {"imageGeneration": "Crea imágenes impresionantes a partir de descripciones de texto usando modelos de IA avanzados", "imageEditing": "Edita y mejora tus imágenes con herramientas impulsadas por IA", "videoGeneration": "Crea videos cautivadores a partir de prompts de texto", "podcastGeneration": "Genera contenido profesional de podcasts con voces de IA", "musicGeneration": "Compone música hermosa en varios estilos y géneros", "aiAssistant": "Chatea con tu asistente personal de IA para cualquier tarea"}}, "privacy": {"title": "Tu privacidad importa", "description": "Estamos comprometidos a proteger tu privacidad y asegurar que tu trabajo creativo siga siendo tuyo.", "features": {"noDataTraining": {"title": "Sin entrenamiento de datos", "description": "Nunca usamos tu contenido para entrenar nuestros modelos de IA. Tu trabajo creativo se mantiene privado y seguro."}, "sessionStorage": {"title": "Almacenamiento solo de sesión", "description": "Tus datos solo se almacenan durante sesiones activas y se eliminan automáticamente cuando terminas."}, "fullOwnership": {"title": "Propiedad completa", "description": "<PERSON>res dueño de todo el contenido que creas. Descarga, modifica y usa tus creaciones como quieras."}}}}, "pricing": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Elige el plan adecuado para ti", "description": "Comienza con una cuenta gratuita o actualiza para desbloquear más funciones y salidas de mayor calidad.", "mostPopular": "Más popular", "month": "/mes", "year": "/año", "processing": "Procesando...", "noRegistration": "No se requiere registro. Configura tu cuenta después del pago.", "plans": {"free": {"name": "<PERSON><PERSON><PERSON>", "description": "Prueba la creación con IA con nuestro nivel gratuito.", "cta": "Registrar<PERSON> gratis", "features": {"aiAssistant": "Acceso al asistente IA", "fluxModel": "Modelo gratuito Flux Schell para generación de imágenes", "limitedGenerations": "Generaciones de imágenes limitadas por día", "basicModels": "Acceso a modelos básicos", "standardQuality": "Calidad de salida estándar"}}, "standard": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Perfecto para profesionales creativos que necesitan herramientas de IA confiables.", "cta": "Comenzar", "features": {"aiImages": "Genera imágenes impresionantes con IA", "musicTracks": "Crea pistas musicales originales", "speechToText": "Transcribe audio a texto", "aiWriting": "Obtén asistencia de escritura con IA", "advancedModels": "Acceso a modelos avanzados", "higherQuality": "Salidas de mayor calidad", "priorityProcessing": "Procesamiento prioritario"}}, "premium": {"name": "Premium", "description": "Para usuarios avanzados que necesitan la suite creativa completa con capacidades de video.", "cta": "Elegir Premium", "features": {"everythingStandard": "Todo en el plan Estándar", "videoGeneration": "Generación de video con IA", "podcasts": "Produce podcasts profesionales", "higherQuality": "Salidas de mayor calidad", "priorityProcessing": "Procesamiento prioritario", "longerContent": "Generación de contenido más largo", "advancedCustomization": "Opciones de personalización avanzadas", "modelFineTuning": "Ajuste fino de modelos personalizados"}}}}, "features": {"imageGeneration": {"title": "Generación de imágenes", "description": "Crea imágenes impresionantes y únicas a partir de descripciones de texto con modelos avanzados de IA."}, "imageEditing": {"title": "Edición de imágenes", "description": "Edita y mejora tus fotos con herramientas impulsadas por IA que hacen simples las ediciones complejas."}, "videoGeneration": {"title": "Generación de videos", "description": "Transforma prompts de texto en videos cautivadores para redes sociales, marketing o proyectos personales."}, "podcastCreation": {"title": "Creación de podcasts", "description": "Genera contenido profesional de podcasts con voces realistas de IA y capacidades de edición de audio."}, "musicComposition": {"title": "Composición musical", "description": "Compone música hermosa en varios estilos y géneros con nuestras herramientas de generación musical con IA."}, "advancedModels": {"title": "Modelos avanzados de IA", "description": "Impulsado por modelos de IA de vanguardia para entregar contenido creativo de alta calidad."}}, "imageGeneration": {"title": "Generación de imágenes", "subtitle": "Crea imágenes impresionantes con IA", "prompt": "Describe la imagen que quieres crear...", "negativePrompt": "Describe lo que no quieres en la imagen...", "generateButton": "Generar imagen", "uploadImage": "Subir imagen", "changeImage": "Cambiar imagen", "changeImages": "Cambiar <PERSON>", "uploadImages": "Subir imágenes", "imageSize": "Tamaño de imagen", "numberOfImages": "Número de imágenes", "advancedSettings": "Configuración avanzada", "seed": "<PERSON><PERSON>", "steps": "Pasos", "guidanceScale": "Escala de guía", "generating": "Generando...", "downloadImage": "<PERSON><PERSON><PERSON> imagen", "saveImage": "Guardar imagen", "copyPrompt": "<PERSON><PERSON><PERSON> prompt", "regenerate": "<PERSON><PERSON><PERSON>", "freeTierTitle": "<PERSON><PERSON> imagen (<PERSON><PERSON> gratuito)", "mainTitle": "Generar imagen", "aiImageGeneration": "Generación de imágenes con IA", "description": "Describe la imagen que quieres crear en detalle, o sube una imagen existente para transformarla.", "showDimensions": "Mostrar dimensiones", "hideDimensions": "Ocultar dimensiones", "showAdvancedOptions": "Mostrar opciones avanzadas", "hideAdvancedOptions": "Ocultar opciones avanzadas", "uploadInstructions": "Presiona Enter para enviar, Shift+Enter para nueva línea. Sube una imagen para generación imagen-a-imagen.", "pasteImageSuccess": "Imagen pegada exitosamente", "pasteImageDescription": "He pegado una imagen para editar:", "subscriptionRequired": "Suscripción requerida", "subscriptionRequiredDescription": "Esta función requiere una suscripción", "modelDoesNotSupportImageInput": "Este modelo no admite entrada de imágenes", "premiumModelSelected": "Modelo premium seleccionado", "premiumModelDescription": "Necesitarás una suscripción para generar con este modelo", "viewSubscriptionPlans": "Ver planes de suscripción", "searchModels": "Buscar modelos...", "selectModel": "Seleccionar un modelo", "freeTierLimited": "Nivel gratuito - Opciones limitadas", "freeModel": "<PERSON><PERSON> grat<PERSON>", "premiumModel": "Premium", "fluxSchnellFree": "FLUX <PERSON> (Gratuito)", "freeImageGeneration": "Generación de imágenes gratuita", "placeholder": {"imageGeneration": "Qué imagen quieres crear?"}, "examples": {"photoRealistic": {"title": "Retrato fotorrealista", "description": "Genera un retrato humano detallado con características realistas"}, "fantasyLandscape": {"title": "<PERSON><PERSON><PERSON>", "description": "Crea un mundo inmersivo de fantasía"}}, "errors": {"promptRequired": "Por favor ingresa un prompt o sube una imagen", "modelNotSupported": "El modelo seleccionado no soporta entrada de imagen. Por favor elige un modelo que soporte entrada de imagen o cambia a generación solo de texto.", "fileSizeLimit": "El tamaño de cada archivo debe ser menor a 10MB", "imageFilesOnly": "Por favor sube solo archivos de imagen", "generationFailed": "Error al generar imagen. Por favor intenta de nuevo.", "noImagesGenerated": "No se generaron imágenes. Por favor intenta de nuevo o elige un modelo diferente.", "failedToSaveImage": "Error al guardar imagen", "failedToCopyText": "Error al copiar texto al portapapeles.", "failedToFetchImageData": "Error al obtener datos de imagen", "failedToDownloadImage": "Error al descargar imagen", "failedToReadFile": "Error al leer el archivo", "failedToReadImageFile": "Error al leer el archivo de imagen", "failedToReadImageFiles": "Error al leer los archivos de imagen"}, "success": {"imageUploaded": "subida exitosamente. Ahora ingresa tu prompt y envía.", "imageGenerated": "¡Imagen generada exitosamente!", "imagesUploaded": "imágenes subidas exitosamente. Ahora ingresa tu prompt y envía.", "promptCopied": "¡Prompt copiado al portapapeles!"}, "loading": {"uploadingImage": "Subiendo imagen...", "uploadingImages": "Subiendo imágenes...", "processingPastedImage": "Procesando imagen pegada..."}}, "videoGeneration": {"title": "Generación de videos", "subtitle": "Crea videos increíbles con IA", "prompt": "Describe el video que quieres crear...", "negativePrompt": "Describe lo que no quieres en el video...", "generateButton": "Generar video", "uploadVideo": "Subir video", "uploadImage": "Subir imagen", "selectModel": "Seleccionar modelo", "resolution": "Resolución", "duration": "Duración", "mode": "Modo", "modes": {"textToVideo": "Texto a video", "imageToVideo": "Imagen a video", "upscaleVideo": "Mejorar video", "audioVideo": "Agregar audio al video"}, "generating": "Generando video...", "downloadVideo": "Des<PERSON><PERSON> video", "copyPrompt": "<PERSON><PERSON><PERSON> prompt", "mainTitle": "Generar video", "aiVideoGeneration": "Generación de videos con IA", "description": "Describe el video que quieres crear, o sube una imagen/video para transformarlo.", "modeDescriptions": {"textToVideo": "Genera videos a partir de prompts de texto", "imageToVideo": "Anima imágenes estáticas en videos", "upscaleVideo": "Mejora la resolución y calidad", "audioVideo": "Genera audio para videos"}, "placeholders": {"textToVideo": "Describe el video que quieres crear...", "imageToVideo": "Describe c<PERSON>mo debe animarse la imagen...", "upscaleVideo": "Sube un video para mejorar...", "audioVideo": "Describe el audio que quieres generar..."}, "examples": {"cinematicScene": {"title": "Escena cinematográfica", "description": "Genera una secuencia de video similar a una película"}, "natureTimelapse": {"title": "Timelapse de naturaleza", "description": "Crea hermosas animaciones de naturaleza"}}, "errors": {"promptRequired": "Por favor ingresa un prompt", "videoRequired": "Por favor sube un video para mejorar", "videoForAudioRequired": "Por favor sube un video para agregar audio", "generationFailed": "Error al generar video. Por favor intenta de nuevo.", "invalidImageFile": "Por favor selecciona un archivo de imagen válido", "invalidVideoFile": "Por favor selecciona un archivo de video válido", "fileSizeTooLarge": "El tamaño del archivo debe ser menor a 10MB", "failedToCopyText": "Error al copiar texto al portapapeles."}, "success": {"promptCopied": "¡Prompt copiado al portapapeles!"}}, "musicGeneration": {"title": "Generación de música", "subtitle": "Compone música hermosa con IA", "prompt": "Describe la música que quieres crear...", "tags": "Etiquetas (opcional)", "generateButton": "Generar m<PERSON>", "uploadAudio": "Subir audio", "selectModel": "Seleccionar modelo", "duration": "Duración", "quality": "Calidad", "mode": "Modo", "modes": {"textToMusic": "Texto a música", "audioToMusic": "Audio a música", "extendMusic": "Extender <PERSON><PERSON>", "remixMusic": "Remezclar música"}, "generating": "Generando mús<PERSON>...", "downloadMusic": "<PERSON><PERSON><PERSON>", "playMusic": "Reproducir m<PERSON>", "pauseMusic": "Pausar mús<PERSON>", "aiMusicGeneration": "Generación de música con IA", "description": "Describe la música que quieres crear, o sube audio para transformarlo.", "subscriptionRequired": "Suscripción requerida", "subscriptionRequiredDescription": "Actualiza tu plan para acceder a las funciones de generación de música con IA.", "upgradeSubscription": "Actualizar suscripción", "modeDescriptions": {"textToMusic": "Genera música a partir de descripciones de texto", "audioToMusic": "Transforma audio en música", "extendMusic": "Extiende música existente", "remixMusic": "Remezcla y transforma música"}, "placeholders": {"textToMusic": "Describe la música que quieres generar... (ej., \"Música electrónica alegre con bajo pesado\")", "audioToMusic": "Describe cómo quieres transformar el audio subido...", "extendMusic": "Describe cómo quieres continuar la música subida...", "remixMusic": "Describe el estilo o cambios que quieres hacer...", "lyrics": "Escribe la letra de tu canción...", "tags": "Ingresa etiquetas separadas por comas (ej., pop, alegre, electrónico)"}, "examples": {"electronicDance": {"title": "Danza electrónica", "description": "Crea música EDM energética con ritmos pegadizos"}, "acousticRelaxation": {"title": "Relajación acústica", "description": "Genera música acústica calmante"}}, "errors": {"promptRequired": "Por favor ingresa un prompt", "generationFailed": "Error al generar música. Por favor intenta de nuevo.", "subscriptionRequired": "Por favor actualiza tu suscripción para generar música", "audioFileRequired": "Por favor selecciona un archivo de audio", "musicDescriptionRequired": "Por favor ingresa una descripción para tu música"}, "success": {"musicGenerated": "¡Música generada exitosamente!"}, "loading": {"generatingMusic": "Generando mús<PERSON>..."}}, "podcastGeneration": {"title": "Generación de podcasts", "subtitle": "Crea podcasts profesionales con voces de IA", "prompt": "Ingresa el contenido de tu podcast...", "generateButton": "Generar podcast", "selectModel": "Seleccionar modelo", "selectVoice": "Seleccionar voz", "createVoice": "<PERSON><PERSON><PERSON> voz", "cloneVoice": "Clonar voz", "voiceDescriptions": {"aurora": "Aurora es una voz femenina joven, amigable y burbujeante. Perfecta para contenido infantil, comerciales y narración alegre con un tono energético y alegre.", "blade": "Blade es una voz masculina fuerte y confiada con un toque moderno. Ideal para contenido de acción, videojuegos y presentaciones dinámicas que requieren autoridad e intensidad.", "britney": "Britney es una voz femenina vibrante y juvenil con atractivo contemporáneo. Excelente para contenido de estilo de vida, redes sociales y aplicaciones comerciales modernas.", "carl": "Carl es una voz masculina cálida y confiable con un tono profesional. Perfecta para contenido corporativo, material educativo y narración confiable.", "cliff": "Cliff es una voz masculina madura y autoritaria con gravedad. Excelente para documentales, contenido serio y presentaciones profesionales que requieren credibilidad.", "rico": "Rico es una voz masculina carismática y suave con estilo latino. Ideal para contenido de entretenimiento, narración de historias y formatos conversacionales atractivos.", "vicky": "Vicky es una voz femenina sofisticada y articulada con claridad profesional. Perfecta para contenido empresarial, noticias y presentaciones formales.", "richard": "Richard es una voz masculina distinguida y experimentada con entrega refinada. Excelente para marcas de lujo, contenido premium y narración sofisticada.", "aria": "Aria es una voz femenina americana de mediana edad con acento afroamericano y tono ronco. Perfecta para narración emotiva, cuentos y contenido que requiere calidez y autenticidad.", "rachel": "Rachel es una voz femenina americana joven y calmada con articulación clara. Ideal para narración, contenido educativo y presentaciones profesionales que requieren claridad y confianza.", "drew": "Drew es una voz masculina americana de mediana edad bien equilibrada con tono autoritario. Perfecta para noticias, documentales y contenido corporativo que requiere credibilidad y profesionalismo.", "clyde": "Clyde es una voz masculina americana distintiva con profundidad de carácter y gravedad. Excelente para narración, contenido dramático y narrativas que requieren resonancia emocional.", "paul": "Paul es una voz masculina americana autoritaria de mediana edad con presencia dominante. Ideal para transmisión de noticias, presentaciones corporativas y contenido que requiere tono de liderazgo fuerte.", "domi": "Domi es una voz femenina americana joven y fuerte con entrega confiada. Perfecta para narración dinámica, contenido motivacional y presentaciones que requieren energía y convicción.", "sarah": "Sarah es una voz femenina confiada y cálida con calidad madura y tono profesional tranquilizador. Excelente para contenido empresarial, materiales de capacitación y comunicación confiable.", "dave": "Dave es una voz masculina británica joven y conversacional con encanto amigable. Ideal para trabajo de personajes, contenido casual y narración atractiva con personalidad accesible.", "jennifer": "Jennifer es una voz femenina americana joven con tono conversacional claro. Perfecta para diálogos, contenido de atención al cliente y comunicación amigable que requiere accesibilidad.", "furio": "Furio es una voz masculina italiana distintiva con carácter y calidez. Ideal para narración, trabajo de personajes y contenido que requiere encanto mediterráneo y personalidad.", "dexter": "Dexter es una voz masculina americana de mediana edad con claridad profesional. Excelente para contenido corporativo, materiales de capacitación y presentaciones que requieren confiabilidad.", "charlotte": "Charlotte es una voz femenina canadiense profesional con atractivo publicitario. Perfecta para comerciales, contenido de marketing y materiales promocionales que requieren entrega pulida.", "ava": "Ava es una voz femenina australiana vibrante con personalidad energética. Ideal para contenido de estilo de vida, entretenimiento y presentaciones atractivas con atractivo internacional.", "cecil": "Cecil es una voz masculina británica distinguida con articulación refinada. Excelente para contenido formal, documentales y presentaciones que requieren sofisticación y autoridad.", "cillian": "Cillian es una voz masculina irlandesa encantadora con tono cálido y conversacional. Perfecta para narración, trabajo de personajes y contenido que requiere encanto celta y autenticidad.", "alessandro": "Alessandro es una voz masculina italiana sofisticada con entrega elegante. Ideal para contenido de lujo, presentaciones culturales y narrativas que requieren sofisticación europea.", "carmenMexican": "Carmen es una voz femenina mexicana cálida con personalidad atractiva. Perfecta para contenido multicultural, narración y presentaciones que requieren autenticidad latinoamericana.", "inara": "Inara es una voz femenina profesional con entrega clara y articulada. Excelente para contenido empresarial, materiales educativos y presentaciones que requieren claridad y profesionalismo.", "kiriko": "Kiriko es una voz femenina japonesa gentil con tono cálido y conversacional. Ideal para contenido cultural, narración y presentaciones que requieren autenticidad asiática y gracia.", "sterling": "Sterling es una voz masculina británica mayor con calidad narrativa distinguida. Perfecta para documentales, contenido histórico y narración que requiere gravedad y sabiduría.", "dohee": "Dohee es una voz femenina coreana profesional con entrega clara y atractiva. Excelente para contenido educativo, presentaciones y comunicación multicultural.", "carmenSpanish": "Carmen es una voz femenina española articulada con claridad profesional. Ideal para contenido empresarial, materiales educativos y presentaciones formales en mercados españoles.", "patricia": "Patricia es una voz femenina española madura con tono cálido y profesional. Perfecta para contenido corporativo, materiales de capacitación y presentaciones que requieren autoridad y confianza.", "caroline": "Caroline es una voz femenina brasileña amigable con entrega cálida y conversacional. Excelente para contenido de estilo de vida, atención al cliente y comunicación atractiva.", "madison": "Madison es una voz femenina irlandesa profesional con tono claro y articulado. Ideal para contenido empresarial, presentaciones y comunicación formal que requiere profesionalismo celta.", "baptiste": "Baptiste es una voz masculina francesa sofisticada con entrega refinada. Perfecta para contenido de lujo, presentaciones culturales y narrativas que requieren elegancia europea.", "andrei": "Andrei es una voz masculina rusa fuerte con presencia autoritaria. Excelente para contenido dramático, documentales y presentaciones que requieren gravedad e intensidad.", "ada": "Ada es una voz femenina sudafricana distintiva con personalidad cálida y atractiva. Ideal para contenido multicultural, narración y presentaciones que requieren autenticidad africana.", "charlotteMeditation": "Charlotte es una voz femenina canadiense calmante con calidad suave y meditativa. Perfecta para contenido de bienestar, meditaciones guiadas y materiales de relajación."}, "generating": "Generando podcast...", "downloadPodcast": "Descargar podcast", "playPodcast": "Reproducir podcast", "pausePodcast": "Pausar podcast", "placeholders": {"dialog": "Ingresa formato de diálogo con prefijos Voice1: y Voice2:. <PERSON><PERSON><PERSON><PERSON>:\nVoice1: <PERSON><PERSON>, ¿viste el partido anoche?\nVoice2: ¡Por supuesto! Qué partido—me tuvo al borde del asiento.\nVoice1: ¡A mí también! Ese gol de último minuto fue increíble.", "narration": "Ingresa texto para narración de un solo hablante. Ejemplo: ¡Hola! Esta es una prueba del sistema de texto a voz, impulsado por ElevenLabs. ¿Cómo suena?", "default": "Ingresa el contenido para tu podcast..."}, "aiPodcastGeneration": "Generación de podcast con IA", "description": "Describe el podcast que quieres crear. Elige tu modelo y voces a continuación.", "subscriptionRequired": "Suscripción requerida", "subscriptionRequiredDescription": "Actualiza tu plan para acceder a las funciones de generación de podcast con IA.", "upgradeSubscription": "Actualizar suscripción", "aiAssistant": "Asistente IA", "examples": {"techDebate": {"title": "Debate tecnológico", "description": "Crea una discusión entre expertos sobre IA"}, "educationalContent": {"title": "Contenido educativo", "description": "Genera podcasts informativos sobre cualquier tema", "prompt": "Crea un podcast educativo de 5 minutos sobre exploración espacial con narración atractiva"}, "conversationalDialog": {"title": "Diálogo conversacional", "description": "Discusiones con múl<PERSON>les hablantes", "prompt": "Voz1: <PERSON><PERSON>, ¿viste el partido anoche? Voz2: ¡Por supuesto! Qué partido—me tuvo al borde del asiento. Voz1: ¡A mí también! Ese gol de último minuto fue increíble. ¿Quién es tu MVP? Voz2: Tiene que ser el portero. Esas atajadas fueron increíbles. Voz1: Absolutamente. Salvó el día, ¡literalmente! ¿Planeas ver el próximo partido? Voz2: ¡Por supuesto! Ya tengo los snacks listos. Voz1: ¡No puedo esperar! Hablemos después del partido."}}, "errors": {"promptRequired": "Por favor ingresa contenido del podcast", "generationFailed": "Error al generar podcast. Por favor intenta de nuevo.", "requestRequired": "Por favor ingresa una solicitud", "failedToGenerateContent": "Error al generar contenido", "failedToPlayVoicePreview": "Error al reproducir vista previa de voz", "noAudioUrlAvailable": "No hay URL de audio disponible para descargar.", "failedToGeneratePodcast": "Error al generar podcast:"}, "success": {"contentGenerated": "¡Contenido generado exitosamente!", "downloadStarted": "¡Descarga iniciada!"}, "loading": {"generating": "Generando...", "generatingContent": "Generar contenido"}, "buttons": {"download": "<PERSON><PERSON><PERSON>", "useThisContent": "Usar este contenido"}}, "imageEditing": {"editImage": "<PERSON><PERSON> imagen", "upscaleImage": "<PERSON><PERSON><PERSON> imagen", "aiImageEditing": "Edición de imagen con IA", "description": "Sube una imagen y describe cómo quieres editarla.", "uploadImage": "Subir imagen", "uploadInstructions": "Arrastra y suelta una imagen aquí, o haz clic para seleccionar", "changeImage": "Cambiar imagen", "subscriptionRequired": "Suscripción requerida", "subscriptionRequiredDescription": "Los modelos premium requieren una suscripción", "premiumModelSelected": "Modelo premium seleccionado", "premiumModelDescription": "Necesitarás una suscripción para usar este modelo", "viewSubscriptionPlans": "Ver planes de suscripción", "showAdvancedOptions": "Mostrar opciones avanzadas", "hideAdvancedOptions": "Ocultar opciones avanzadas", "negativePrompt": "Prompt negativo", "negativePromptPlaceholder": "Lo que no quieres en la imagen...", "examples": {"backgroundRemoval": {"title": "Eliminación de fondo", "description": "Elimina fondos de imágenes"}, "imageUpscaling": {"title": "<PERSON><PERSON><PERSON> de <PERSON>n", "description": "Mejora la resolución y calidad de la imagen"}}, "errors": {"uploadImageFirst": "Por favor sube una imagen primero", "enterPromptForModel": "Por favor ingresa un prompt para este modelo", "imageFileOnly": "Por favor sube un archivo de imagen", "fileSizeLimit": "El tamaño del archivo debe ser menor a 10MB", "failedToReadFile": "Error al leer el archivo de imagen", "failedToProcessImage": "Error al procesar imagen. Por favor intenta de nuevo.", "noImageInResponse": "No hay URL de imagen en la respuesta", "failedToSaveImage": "Error al guardar imagen", "failedToCopyText": "Error al copiar texto al portapapeles.", "failedToFetchImageData": "Error al obtener datos de imagen", "failedToDownloadImage": "Error al descargar imagen. Por favor intenta de nuevo."}, "success": {"imageUploaded": "Imagen subida exitosamente", "imagePasted": "Imagen pegada exitosamente", "promptCopied": "¡Prompt copiado al portapapeles!"}, "loading": {"uploadingImage": "Subiendo imagen...", "processingPastedImage": "Procesando imagen pegada..."}, "studio": {"title": "Estudio de edición de imágenes", "uploadInstructions": "Sube una imagen usando el botón de subida en el cuadro de mensaje de abajo para comenzar a editar. Puedes eliminar fondos, cambiar objetos, agregar efectos y más.", "pasteInstructions": "También puedes pegar una imagen desde el portapapeles (Ctrl+V / Cmd+V)", "uploadFirstMessage": "Por favor sube una imagen primero antes de proporcionar instrucciones.", "placeholderWithImage": "Describe cómo quieres editar esta imagen...", "placeholderWithoutImage": "Sube una imagen primero para comenzar a editar"}}, "speechToText": {"title": "Voz a texto", "subtitle": "Convierte audio a texto con IA", "uploadAudio": "Subir audio", "recordAudio": "Grabar audio", "startRecording": "Iniciar grabac<PERSON>", "stopRecording": "Detener grabación", "transcribeButton": "Transcribir", "selectModel": "Seleccionar modelo", "language": "Idioma", "format": "Formato de salida", "transcribing": "Transcribiendo...", "downloadTranscript": "Descargar transcripción", "copyTranscript": "Copiar transcripción", "description": "Convierte voz a texto con modelos de IA avanzados. Sube archivos de audio o graba directamente.", "subscriptionRequired": "Suscripción requerida", "subscriptionRequiredDescription": "Voz a texto requiere una suscripción Estándar o Premium", "viewSubscriptionPlans": "Ver planes de suscripción", "goBack": "Volver", "modelSelection": "Selección de modelo", "autoDetectLanguage": "Detectar idioma automáticamente", "outputFormat": "Formato de salida", "uploadAudioFile": "Subir archivo de audio", "supportedFormats": "MP3, WAV, M4A, FLAC, hasta 25MB", "transcribeAudio": "Transcribir audio", "processing": "Procesando...", "errors": {"audioRequired": "Por favor sube o graba un archivo de audio primero", "transcriptionFailed": "Error al transcribir audio. Por favor intenta de nuevo.", "failedToStartRecording": "Error al iniciar grabación. Por favor verifica los permisos del micrófono.", "uploadOrRecordFirst": "Por favor sube o graba un archivo de audio primero"}, "success": {"transcriptionComplete": "¡Transcripción completada exitosamente!"}, "loading": {"loading": "Cargando..."}}, "aiAssistant": {"title": "Asistente IA", "subtitle": "Chatea con tu asistente personal de IA", "placeholder": "Escribe tu mensaje...", "sendButton": "Enviar", "clearChat": "Limpiar chat", "selectModel": "Seleccionar modelo", "thinking": "Pensando...", "modelCategories": {"availableModels": "Modelos disponibles"}, "modelSelector": {"modelSelection": "Selección de modelo", "selectAModel": "Seleccionar un modelo", "noProvider": "<PERSON> proveedor", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sidebar": {"hideSidebar": "Ocultar barra lateral", "showSidebar": "Mostrar barra lateral", "conversations": "Conversaciones", "noConversations": "Aún no hay conversaciones", "currentModel": "Modelo actual:", "selectAModel": "Seleccionar un modelo"}, "options": {"hide": "Ocultar", "show": "Mostrar", "options": "Opciones", "advancedOptions": "Opciones avanzadas", "temperature": "Temperatura", "maxTokens": "Tokens máximos"}, "conversation": {"newConversation": "Nueva conversación", "deleteConversation": "Eliminar conversación", "downloadConversation": "Descargar conversación", "download": "<PERSON><PERSON><PERSON>"}, "welcome": {"subtitle": "<PERSON><PERSON> pregun<PERSON>, obtén ayuda con tareas o mantén conversaciones creativas con tu asistente de IA.", "examples": {"professionalWriting": "Escritura profesional", "professionalWritingPrompt": "Ayúdame a escribir un correo profesional", "learningEducation": "Aprendizaje y educación", "learningEducationPrompt": "Explica la computación cuántica en términos simples", "creativeWriting": "Escritura creativa", "creativeWritingPrompt": "Escribe una historia corta creativa sobre exploración espacial", "codeAssistance": "Asistencia de código", "codeAssistancePrompt": "Ayúdame a depurar este código Python"}}, "input": {"instructions": "Presiona Enter para enviar, Shift+Enter para nueva línea", "copyMessage": "<PERSON><PERSON><PERSON>", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "messageInput": "Entrada de mensaje"}, "mock": {"banner": "Ejecutándose en modo IA simulada. Configura las claves API en tu archivo .env para usar modelos de IA reales.", "response": "Respuesta simulada", "warning": "⚠️ Usando modo IA simulada porque las claves API no están configuradas. Por favor configura TOGETHER_API_KEY y REPLICATE_API_KEY en tu archivo .env."}, "errors": {"messageRequired": "Por favor ingresa un mensaje", "chatFailed": "Error al enviar mensaje. Por favor intenta de nuevo.", "copyFailed": "Error al copiar texto al portapapeles."}}, "subscription": {"title": "Gestión de suscripción", "currentPlan": "Plan actual", "billingCycle": "Ciclo de facturación", "manageSubscription": "Gestionar suscripción", "cancelSubscription": "Cancelar suscripción", "upgradeSubscription": "Actualizar suscripción", "customerPortal": "Portal del cliente", "loading": "Cargando detalles de suscripción...", "subtitle": "Elige un plan de suscripción que funcione para tus necesidades creativas", "subscriptionPlans": "Planes de suscripción", "selectPlan": "Selecciona un plan para acceder a funciones premium", "currentSubscription": "Suscripción actual", "manageCurrentPlan": "Gestiona tu plan actual", "status": "Estado", "planType": "Tipo de plan", "nextBilling": "Próxima facturación", "subscriptionId": "ID de suscripción", "opening": "Abriendo...", "refreshStatus": "Actualizar estado", "manageSubscriptionInfo": "Haz clic en \"Gestionar suscripción\" para acceder al portal de cliente de Stripe donde puedes actualizar métodos de pago, descargar facturas y cancelar tu suscripción.", "subscriptionManagement": "Gestión de suscripción", "accessBillingHistory": "Accede a tu historial de facturación y métodos de pago", "accessBillingPortal": "Acceder al portal de facturación", "billingPortalInfo": "Accede a tu historial de facturación, descarga facturas y gestiona métodos de pago incluso sin una suscripción activa.", "planFeatures": "Características del plan", "standardPlan": "Plan Estándar", "standardDescription": "Perfecto para profesionales creativos que necesitan herramientas de IA confiables.", "premiumPlan": "Plan Premium", "premiumDescription": "Para usuarios avanzados que necesitan la suite creativa completa con capacidades de video.", "loadingSubscription": "Cargando información de suscripción...", "features": {"generateStunningImages": "Genera imágenes impresionantes con IA", "createOriginalMusic": "Crea pistas musicales originales", "transcribeAudio": "Transcribe audio a texto", "aiWritingAssistance": "Obtén asistencia de escritura con IA", "everythingStandard": "Todo en el plan Estándar", "aiVideoGeneration": "Generación de video con IA", "professionalPodcasts": "Produce podcasts profesionales", "higherQuality": "Salidas de mayor calidad", "priorityProcessing": "Procesamiento prioritario", "longerContent": "Generación de contenido más largo", "advancedCustomization": "Opciones de personalización avanzadas"}, "subscriptionManager": {"validatingSubscription": "Validando suscripción...", "loadingOptions": "Cargando opciones de suscripción...", "paymentConfigError": "Error de configuración de pago", "errorLoadingPrices": "Hubo un error al cargar los precios de suscripción. Por favor intenta actualizar la página o contacta soporte.", "refreshPage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "recommended": "Recomendado", "currentPlan": "Plan actual", "subscribe": "Suscribirse", "processing": "Procesando...", "subscriptionCanceled": "Tu suscripción ha sido cancelada", "failedToSubscribe": "Error al iniciar suscripción. Por favor intenta de nuevo.", "failedToCancel": "Error al cancelar suscripción. Por favor intenta de nuevo.", "invalidPlan": "Plan seleccionado inválido", "priceNotConfigured": "ID de precio no configurado para este plan", "noCheckoutUrl": "No se recibió URL de checkout", "failedToCreateCheckout": "Error al crear sesión de checkout", "failedToCancelInternal": "Error al cancelar suscripción"}, "errors": {"loadFailed": "Error al cargar detalles de suscripción", "portalFailed": "Error al abrir portal del cliente", "unableToAccess": "No se puede acceder a la gestión de suscripción. Por favor inicia sesión de nuevo.", "failedToCreate": "Error al crear sesión del portal", "noPortalUrl": "No se recibió URL del portal", "failedToAccess": "Error al acceder a la gestión de suscripción. Por favor intenta de nuevo."}}, "testimonials": {"title": "Lo que dicen nuestros usuarios", "subtitle": "Descubre cómo AstroStudio AI está transformando los flujos de trabajo creativos", "testimonial1": {"content": "¡AstroStudio AI ha revolucionado completamente mi proceso creativo! Pasaba días tratando de crear visuales profesionales para mis clientes, pero ahora puedo generar imágenes impresionantes listas para publicación en minutos. La IA entiende mi visión artística perfectamente.", "author": "<PERSON>", "role": "Artista digital y directora creativa"}, "testimonial2": {"content": "Las capacidades de generación de música de AstroStudio AI son simplemente alucinantes. He usado las composiciones para la música de fondo de mi canal de YouTube, y hago canciones originales como pasatiempo. La calidad es indistinguible.", "author": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON> de contenido y artista"}, "testimonial3": {"content": "Como propietario de una pequeña empresa, AstroStudio AI ha sido un cambio radical para mis esfuerzos de marketing. Puedo crear videos promocionales de calidad profesional y contenido para redes sociales sin contratar agencias costosas. ¡Es como tener un equipo creativo completo al alcance de mis dedos!", "author": "<PERSON>", "role": "Propietario de pequeña empresa"}}, "auth": {"login": {"title": "Bienvenido de vuelta", "subtitle": "Inicia sesión en tu cuenta para continuar", "email": "Dirección de correo electrónico", "emailLabel": "Dirección de correo electrónico", "emailPlaceholder": "Dirección de correo electrónico", "password": "Contraseña", "passwordLabel": "Contraseña", "passwordPlaceholder": "Contraseña", "forgotPassword": "¿Olvidaste tu contraseña?", "signInButton": "Iniciar se<PERSON><PERSON> con Email", "signInWithGoogle": "Iniciar se<PERSON><PERSON> con Google", "signingIn": "Iniciando se<PERSON>...", "connecting": "Conectando...", "orContinueWith": "O continúa con", "noAccount": "¿No tienes una cuenta?", "signUpInstead": "<PERSON><PERSON><PERSON> una ahora", "createAccount": "<PERSON><PERSON><PERSON> una ahora", "signInSuccess": "Inicio de sesión exitoso", "success": "¡Sesión iniciada exitosamente! Redirigiendo..."}, "signup": {"title": "<PERSON><PERSON><PERSON> una cuenta", "subtitle": "Únete a AstroStudio AI para comenzar a crear contenido increíble", "signInInstead": "Inicia sesión aquí", "name": "Nombre completo", "nameLabel": "Nombre completo", "namePlaceholder": "Nombre completo", "email": "Dirección de correo electrónico", "emailLabel": "Dirección de correo electrónico", "emailPlaceholder": "Dirección de correo electrónico", "password": "Contraseña (mín 8 caracteres)", "passwordLabel": "Contraseña", "passwordPlaceholder": "Contraseña (mín 8 caracteres)", "confirmPassword": "Confirmar con<PERSON>", "confirmPasswordLabel": "Confirmar con<PERSON>", "confirmPasswordPlaceholder": "Confirmar con<PERSON>", "signUpButton": "<PERSON><PERSON><PERSON>", "createAccountButton": "<PERSON><PERSON><PERSON>", "signingUp": "<PERSON><PERSON>ndo cuenta...", "signUpWithGoogle": "Google", "processing": "Procesando...", "orContinueWith": "O continúa con", "hasAccount": "¿Ya tienes una cuenta?", "accountCreated": "¡Cuenta creada exitosamente! Redirigiendo al inicio de sesión...", "success": "¡Cuenta creada exitosamente! Redirigiendo al inicio de sesión...", "passwordsNoMatch": "Las contraseñas no coinciden", "passwordTooShort": "La contraseña debe tener al menos 8 caracteres"}, "forgotPassword": {"title": "¿Olvidaste tu contraseña?", "subtitle": "Ingresa tu dirección de correo electrónico y te enviaremos un enlace para restablecer tu contraseña", "email": "Correo electrónico", "sendResetLink": "Enviar enlace de restablecimiento", "sending": "Enviando...", "backToLogin": "Volver al inicio de sesión", "success": "¡Enlace de restablecimiento enviado! Revisa tu correo electrónico para obtener instrucciones.", "errors": {"emailRequired": "El correo electrónico es requerido", "emailInvalid": "Por favor ingresa un correo electrónico válido", "userNotFound": "Si existe una cuenta con este correo electrónico, recibirás un enlace de restablecimiento de contraseña", "rateLimited": "Demasiados intentos de restablecimiento. Por favor espera antes de intentar de nuevo", "sendFailed": "Error al enviar el correo de restablecimiento. Por favor intenta más tarde"}}, "resetPassword": {"title": "Restablece tu contraseña", "subtitle": "Ingresa tu nueva contraseña a continuación", "validating": "Validando tu token de restablecimiento...", "newPassword": "Nueva contraseña", "confirmPassword": "Confirmar nueva contraseña", "resetButton": "Restablecer contraseña", "resetting": "Restableciendo...", "backToLogin": "Volver al inicio de sesión", "success": "¡Contraseña restablecida exitosamente! Ahora puedes iniciar sesión con tu nueva contraseña.", "errors": {"passwordRequired": "La contraseña es requerida", "passwordTooShort": "La contraseña debe tener al menos 8 caracteres", "passwordMismatch": "Las contraseñas no coinciden", "tokenInvalid": "Token de restablecimiento inválido o expirado", "tokenExpired": "El token de restablecimiento ha expirado. Por favor solicita un nuevo restablecimiento de contraseña", "resetFailed": "Error al restablecer la contraseña. Por favor intenta de nuevo"}}, "errors": {"loginFailed": "Error al iniciar sesión", "signupFailed": "Error al registrarse", "googleLoginFailed": "Error al iniciar sesión con Google", "googleSignupFailed": "Error al registrarse con Google", "invalidCredentials": "Credenciales inválidas", "userExists": "El usuario ya existe", "networkError": "Error de red", "unknownError": "Error descon<PERSON>", "emailRequired": "El correo electrónico es requerido", "emailInvalid": "Por favor ingresa un correo electrónico válido", "passwordRequired": "La contraseña es requerida", "nameRequired": "El nombre es requerido", "passwordTooShort": "La contraseña debe tener al menos 8 caracteres", "passwordMismatch": "Las contraseñas no coinciden", "emailExists": "Ya existe una cuenta con este correo electrónico", "accountNotFound": "No se encontró una cuenta con este correo electrónico", "tooManyAttempts": "Demasiados intentos de inicio de sesión. Por favor intenta más tarde"}}, "footer": {"description": "<PERSON>rea imágenes, videos, podcasts y música increíbles con el poder de la IA.", "location": "Nuestra ubicación: 7901 4th St N STE 300, St. Petersburg, FL 33702", "sections": {"tools": "Herramientas", "resources": "Recursos", "company": "Empresa"}, "links": {"features": "Funciones", "pricing": "<PERSON><PERSON><PERSON>", "signup": "Registrarse", "login": "In<PERSON><PERSON>", "blog": "Blog", "documentation": "Documentación", "support": "Soporte", "aboutUs": "Acerca de nosotros", "contact": "Contacto", "privacyPolicy": "Política de privacidad", "termsOfService": "Términos de servicio"}, "copyright": "AstroStudio AI. Todos los derechos reservados."}, "contact": {"title": "Ponte en contacto", "subtitle": "¿Tienes preguntas? Nos encantaría escucharte.", "name": "Nombre", "email": "Correo electrónico", "message": "Men<PERSON><PERSON>", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "sending": "Enviando...", "success": "¡Mensaje enviado exitosamente!", "error": "Error al enviar mensaje. Por favor intenta de nuevo."}, "examples": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Ve nuestros modelos de IA en acción", "description": "Experimenta la calidad y capacidades de nuestros modelos de IA con estos ejemplos interactivos.", "input": "Entrada", "output": "Salida", "imageGeneration": {"title": "Generación de imágenes", "description": "Crea imágenes de alta calidad con nuestro modelo de IA gratuito"}, "videoGeneration": {"title": "Imagen a video", "description": "Transforma imágenes estáticas en videos dinámicos"}, "musicGeneration": {"title": "Generación de música", "description": "Crea música original con IA"}, "podcastGeneration": {"title": "Creación de podcasts", "description": "Genera contenido de podcast con voces de IA"}, "imageUpscaling": {"title": "<PERSON><PERSON><PERSON> de imágenes", "description": "Mejora la calidad y resolución de imágenes"}, "videoUpscaling": {"title": "Mejora de videos", "description": "Mejora la calidad y resolución de videos"}, "sampleTitles": {"sample1": "Muestra 1", "sample2": "Muestra 2", "example1": "Ejemplo 1", "example2": "Ejemplo 2", "podcast1": "Podcast 1", "podcast2": "Podcast 2"}}, "voiceModals": {"create": {"title": "Crear voz IA", "steps": {"design": "Diseñar voz", "preview": "Vista previa", "complete": "Completar"}, "voiceName": "Nombre de voz", "gender": "<PERSON><PERSON><PERSON>", "accent": "Ace<PERSON>", "age": "Edad", "clarity": "Claridad", "stability": "Estabilidad", "presets": "Presets de voz", "generating": "Generando voz...", "success": "¡Voz creada exitosamente!", "error": "Error al crear voz"}, "clone": {"title": "Clonar voz", "steps": {"upload": "Subir audio", "review": "Rev<PERSON><PERSON>", "complete": "Completar"}, "voiceName": "Nombre de voz", "uploadAudio": "Subir muestra de audio", "processing": "Procesando voz...", "success": "¡Voz clonada exitosamente!", "error": "Error al clonar voz"}}, "legal": {"terms": {"title": "Términos de Servicio", "effectiveDate": "Fecha de vigencia:", "sections": {"acceptance": {"title": "1. Aceptación de los Términos", "content": "Al acceder o usar AstroStudio AI (\"Servicio\"), usted acepta estar sujeto a estos Términos de Servicio (\"Términos\"). Si no está de acuerdo con alguna parte de estos términos, no puede acceder al Servicio."}, "description": {"title": "2. Descripción del Servicio", "content": "AstroStudio AI proporciona servicios de generación de contenido impulsados por inteligencia artificial, incluyendo:", "list": ["Generación y edición de imágenes", "Creación y procesamiento de videos", "Composición musical y generación de audio", "Creación de podcasts y síntesis de voz", "Capacidades de asistente IA y chat"]}, "userAccounts": {"title": "3. Cuentas de Usuario", "content": "Para acceder a ciertas funciones del Servicio, debe crear una cuenta. Usted es responsable de mantener la confidencialidad de su cuenta y contraseña, y de todas las actividades que ocurran bajo su cuenta."}, "payment": {"title": "4. Política de Pago y Reembolso", "importantTitle": "IMPORTANTE: NO HAY REEMBOLSOS DISPONIBLES", "importantContent": "Todos los pagos por suscripciones y servicios son finales y no reembolsables. Esto incluye pero no se limita a:", "list": ["Tarifas de suscripción mensual y anual", "Compras únicas y créditos", "Porciones no utilizadas de períodos de suscripción", "Servicios no utilizados o parcialmente utilizados"], "additional": "Al realizar un pago, usted reconoce y acepta esta política de no reembolso."}, "acceptableUse": {"title": "5. <PERSON><PERSON>", "content": "Usted acepta no usar el Servicio para:", "list": ["Generar contenido ilegal, da<PERSON><PERSON> u ofensivo", "Violar cualquier ley o regulación aplicable", "Infringir derechos de propiedad intelectual", "<PERSON><PERSON><PERSON> contenido que suplante a otros sin consentimiento", "Generar spam, malware o contenido malicioso", "Intentar hacer ingeniería inversa o explotar nuestros sistemas"]}, "contentOwnership": {"title": "6. Propiedad del Contenido y Derechos", "content": "Con respecto al contenido creado usando nuestro Servicio:", "list": ["Usted conserva la propiedad del contenido que crea", "Nos otorga una licencia para procesar y almacenar su contenido según sea necesario para proporcionar el Servicio", "Usted es responsable de asegurar que tiene derechos sobre cualquier contenido de entrada", "El contenido generado puede no ser único y contenido similar puede ser creado para otros usuarios"]}, "disclaimers": {"title": "7. <PERSON><PERSON><PERSON> de Responsabilidad", "content": "El Servicio se proporciona \"tal como está\" sin garantías de ningún tipo. No garantizamos que el Servicio será ininterrumpido, libre de errores o que cumplirá con sus requisitos específicos."}, "limitation": {"title": "8. Limitación de Responsabilidad", "content": "En ningún caso seremos responsables por daños indirectos, incidentales, especiales, consecuentes o punitivos, incluyendo pero no limitado a pérdida de beneficios, datos o uso."}, "termination": {"title": "9. Terminación", "content": "Podemos terminar o suspender su cuenta inmediatamente, sin previo aviso o responsabilidad, por cualquier motivo, incluyendo sin limitación si usted incumple los Términos."}, "changes": {"title": "10. Cambios a los Términos", "content": "Nos reservamos el derecho de modificar estos Términos en cualquier momento. Notificaremos a los usuarios de cambios materiales publicando los Términos actualizados en nuestro sitio web. Su uso continuado del Servicio después de tales cambios constituye la aceptación de los nuevos Términos."}, "governingLaw": {"title": "11. <PERSON><PERSON> Aplicable", "content": "Estos Términos se regirán e interpretarán de acuerdo con las leyes del Estado de Florida, Estados Unidos, sin tener en cuenta sus disposiciones de conflicto de leyes."}, "contact": {"title": "12. Información de Contacto", "content": "Si tiene alguna pregunta sobre estos Términos, contáctenos en:", "email": "Correo electrónico: <EMAIL>", "address": "Dirección: 7901 4th St N STE 300, St. Petersburg, FL 33702"}}}, "privacy": {"title": "Política de Privacidad", "effectiveDate": "Fecha de vigencia:", "sections": {"informationCollection": {"title": "1. Información que Recopilamos", "content": "Recopilamos información que usted nos proporciona directamente, como cuando:", "list": ["Crea una cuenta o se suscribe a nuestros servicios", "Usa nuestras herramientas de generación de IA (imágenes, videos, música, podcasts)", "Se comunica con nosotros para soporte o consultas", "Participa en encuestas o actividades promocionales"], "additional": "Esto puede incluir su nombre, dirección de correo electrónico, información de pago y contenido que crea o sube."}, "informationUse": {"title": "2. <PERSON>ómo Usamos Su Información", "content": "Usamos la información que recopilamos para:", "list": ["Propor<PERSON>ar, mantener y mejorar nuestros servicios de generación de IA", "Procesar pagos y gestionar suscripciones", "Enviarle avisos técnicos y mensajes de soporte", "Responder a sus comentarios y preguntas", "<PERSON><PERSON><PERSON> patrones de uso para mejorar la experiencia del usuario"]}, "informationSharing": {"title": "3. Compartir Información", "content": "No vendemos, comercializamos o transferimos su información personal a terceros, excepto:", "list": ["Con proveedores de servicios que nos ayudan a operar nuestro negocio", "Cuando sea requerido por ley o para proteger nuestros derechos", "En caso de fusión, adquisición o venta de activos"]}, "dataRetention": {"title": "4. Retención de Datos", "content": "Mantenemos su información personal solo durante el tiempo necesario para los propósitos descritos en esta política. El contenido generado se almacena temporalmente durante su sesión y se elimina automáticamente."}, "dataSecurity": {"title": "5. <PERSON><PERSON><PERSON><PERSON>", "content": "Implementamos medidas de seguridad apropiadas para proteger su información personal contra acceso no autorizado, alteración, divulgación o destrucción. Sin embargo, ningún método de transmisión por internet es 100% seguro."}, "userRights": {"title": "6. <PERSON><PERSON>", "content": "Usted tiene derecho a:", "list": ["Acceder y actualizar su información personal", "Eliminar su cuenta y datos asociados", "Optar por no recibir comunicaciones de marketing", "Solicitar una copia de sus datos", "Presentar una queja ante las autoridades relevantes"]}, "cookies": {"title": "7. Cookies y Tecnologías de Seguimiento", "content": "Usamos cookies y tecnologías similares para mejorar su experiencia, analizar el uso del sitio y personalizar el contenido. Puede controlar las cookies a través de la configuración de su navegador."}, "childrensPrivacy": {"title": "8. <PERSON><PERSON><PERSON><PERSON><PERSON> Menores", "content": "Nuestros servicios no están destinados a menores de 13 años. No recopilamos conscientemente información personal de menores de 13 años. Si cree que hemos recopilado dicha información, contáctenos inmediatamente."}, "policyChanges": {"title": "9. Cambios a Esta Política", "content": "Podemos actualizar esta Política de Privacidad de vez en cuando. Le notificaremos de cualquier cambio material publicando la nueva política en esta página y actualizando la fecha de vigencia."}, "contact": {"title": "10. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content": "Si tiene alguna pregunta sobre esta Política de Privacidad, contáctenos en:", "email": "Co<PERSON>o electrónico: <EMAIL>", "address": "Dirección: 7901 4th St N STE 300, St. Petersburg, FL 33702"}}}}}