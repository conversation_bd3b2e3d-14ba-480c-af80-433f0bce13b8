import { getRequestConfig } from 'next-intl/server';
import { locales, type Locale } from './config';

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locale || !locales.includes(locale as Locale)) {
    locale = 'en'; // Default to English if invalid locale
  }

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default
  };
});
