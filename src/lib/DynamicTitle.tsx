'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

// Map of routes to their respective titles
const routeTitles: Record<string, string> = {
  '/dashboard': 'Dashboard',
  '/dashboard/image-generation': 'Image Generation',
  '/dashboard/speech-to-text': 'Speech to Text',
  '/dashboard/video-generation': 'Video Generation',
  '/dashboard/music-generation': 'Music Generation',
  '/dashboard/podcast-generation': 'Podcast Creation',
  '/dashboard/assistant': 'AI Assistant',
  '/dashboard/subscription': 'Subscription',
  '/dashboard/library': 'My Library',
  '/login': 'Login',
  '/signup': 'Sign Up',
  '/pricing': 'Pricing',
  '/payment': 'Payment',
  '/payment-success': 'Payment Successful',
};

export default function DynamicTitle() {
  const pathname = usePathname();

  useEffect(() => {
    if (pathname) {
      // Get the title for the current route, or use a default value
      const pageTitle = routeTitles[pathname] || 'AI-Powered Multimedia Generation';
      
      // Update the document title with the format "AstroStudio AI - [Page Title]"
      document.title = `AstroStudio AI - ${pageTitle}`;
    }
  }, [pathname]);

  // This is a client component that doesn't render anything
  return null;
} 