'use server';

import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { SubscriptionService } from '@/lib/services/subscription-service';
import Stripe from 'stripe';
import { redirect } from 'next/navigation';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2025-02-24.acacia',
});

// Define Price IDs for different plans
const STANDARD_PRICE_ID = process.env.STRIPE_STANDARD_PRICE_ID ?? '';
const PREMIUM_PRICE_ID = process.env.STRIPE_PREMIUM_PRICE_ID ?? '';

/**
 * Get the current user's subscription details
 * Now with improved fallback to Stripe for validation if Neo4j data is missing
 */
export async function getCurrentUserSubscription() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id || !session?.user?.email) {
    return null;
  }
  
  try {
    // First try Neo4j validation
    const subscription = await SubscriptionService.getUserSubscription(session.user.id);
    
    // If subscription is found and active, return it
    if (subscription && subscription.status === 'active') {
      return {
        id: subscription.id,
        type: subscription.type,
        status: subscription.status,
        startDate: subscription.startDate,
        currentPeriodEnd: subscription.endDate,
        stripeCustomerId: subscription.stripeCustomerId,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        stripePriceId: subscription.stripePriceId
      };
    }
    
    // If Neo4j validation fails, try synchronized validation from Stripe
    // This will update both User and Subscription records in Neo4j if needed
    console.log('No active subscription found in Neo4j, checking Stripe and synchronizing');
    const syncedSubscription = await SubscriptionService.synchronizeUserSubscriptionFromStripe(
      session.user.id,
      session.user.email
    );
    
    if (syncedSubscription) {
      console.log(`Found and synchronized subscription from Stripe for ${session.user.email}`);
      return {
        id: syncedSubscription.id,
        type: syncedSubscription.type,
        status: syncedSubscription.status,
        startDate: syncedSubscription.startDate,
        currentPeriodEnd: syncedSubscription.endDate,
        stripeCustomerId: syncedSubscription.stripeCustomerId,
        stripeSubscriptionId: syncedSubscription.stripeSubscriptionId,
        stripePriceId: syncedSubscription.stripePriceId
      };
    }
    
    // No valid subscription found in either Neo4j or Stripe
    return null;
  } catch (error) {
    console.error('Error fetching user subscription:', error);
    // Try Stripe as a last resort without synchronization
    try {
      if (session?.user?.email) {
        const stripeSubscription = await SubscriptionService.validateSubscriptionFromStripe(session.user.email);
        if (stripeSubscription) {
          return {
            id: stripeSubscription.id,
            type: stripeSubscription.type,
            status: stripeSubscription.status,
            startDate: stripeSubscription.startDate,
            currentPeriodEnd: stripeSubscription.endDate,
            stripeCustomerId: stripeSubscription.stripeCustomerId,
            stripeSubscriptionId: stripeSubscription.stripeSubscriptionId,
            stripePriceId: stripeSubscription.stripePriceId
          };
        }
      }
    } catch (stripeError) {
      console.error('Error in Stripe fallback validation:', stripeError);
    }
    return null;
  }
}

/**
 * Create a checkout session for subscription
 */
export async function createCheckoutSession(planType: 'Standard' | 'Premium') {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    throw new Error('You must be logged in to subscribe');
  }
  
  try {
    // Get the price ID based on the selected plan
    const priceId = planType === 'Standard' ? STANDARD_PRICE_ID : PREMIUM_PRICE_ID;
    
    if (!priceId) {
      throw new Error(`Price ID for ${planType} plan is not configured`);
    }
    
    // Check if the user already has a Stripe customer ID
    const existingSubscription = await SubscriptionService.getUserSubscription(session.user.id);
    let customerId = existingSubscription?.stripeCustomerId;
    
    // If not, create a new customer in Stripe
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: session.user.email || undefined,
        metadata: {
          userId: session.user.id
        }
      });
      
      customerId = customer.id;
    }
    
    // Create the checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: customerId,
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription' as const,
      allow_promotion_codes: true,
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/subscription?canceled=true`,
      metadata: {
        userId: session.user.id,
        planType: planType
      }
    });
    
    if (!checkoutSession.url) {
      throw new Error('Failed to create checkout session');
    }
    
    // Redirect to the checkout page
    redirect(checkoutSession.url);
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw new Error('Something went wrong. Please try again later.');
  }
}

/**
 * Create a checkout session for unregistered users
 * This allows users to subscribe without registering first
 */
export async function createGuestCheckoutSession(
  planType: 'Standard' | 'Premium',
  email?: string,
  customPriceId?: string
) {
  try {
    // Verify Stripe key is set
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not set');
      throw new Error('Payment service is not configured');
    }

    // Get the price ID based on the selected plan or use the custom price ID if provided
    const priceId = customPriceId || (planType === 'Standard' ? STANDARD_PRICE_ID : PREMIUM_PRICE_ID);
    
    if (!priceId) {
      console.error(`Price ID for ${planType} plan is not configured`);
      throw new Error('Payment plan configuration is missing');
    }
    
    console.log(`Creating guest checkout with price ID: ${priceId} for plan type: ${planType}`);
    
    // Prepare line items for checkout
    const lineItems = [
      {
        price: priceId,
        quantity: 1
      }
    ];
    
    // Make sure app URL is set
    const appUrl = process.env.NEXT_PUBLIC_APP_URL;
    if (!appUrl) {
      console.error('NEXT_PUBLIC_APP_URL is not set');
      throw new Error('Application URL configuration is missing');
    }

    // Create checkout session parameters - let Stripe collect user email
    const params = {
      line_items: lineItems,
      mode: 'subscription' as const,
      subscription_data: {
        trial_period_days: 3, // 3-day free trial
      },
      allow_promotion_codes: true, // Allow users to enter promo codes in Stripe checkout
      success_url: `${appUrl}/signup`,
      cancel_url: `${appUrl}/`, // Redirect to home page instead of pricing page
      metadata: {
        planType: planType,
        isGuestCheckout: 'true'
      },
      customer_email: email, // Optional - only used if provided
      payment_method_collection: 'always' as const,
      phone_number_collection: {
        enabled: true
      },
      custom_text: {
        submit: {
          message: "You'll create your account after payment. We'll save your email and payment details to set up your subscription."
        }
      }
    };
    
    // Create the checkout session with proper error handling
    let checkoutSession;
    try {
      checkoutSession = await stripe.checkout.sessions.create(params);
    } catch (stripeError: any) {
      console.error('Stripe checkout session creation error:', stripeError);
      throw new Error(stripeError.message || 'Failed to create checkout session');
    }
    
    if (!checkoutSession || !checkoutSession.url) {
      console.error('No URL in checkout session response');
      throw new Error('Failed to generate payment link');
    }
    
    // Return the URL for client-side redirect
    return { url: checkoutSession.url };
  } catch (error) {
    console.error('Error creating guest checkout session:', error);
    throw error; // Let the client handle the error
  }
}

/**
 * Handle webhook events from Stripe
 */
export async function handleStripeWebhook(event: Stripe.Event) {
  try {
    console.log(`Processing webhook event: ${event.type} with ID: ${event.id}`);
    
    // Handle subscription created event
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;
      console.log(`Checkout session completed: ${session.id}`);
      
      const userId = session.metadata?.userId;
      const planType = session.metadata?.planType as 'Standard' | 'Premium';
      const isGuestCheckout = session.metadata?.isGuestCheckout === 'true';
      
      console.log(`Session details: userId=${userId || 'none'}, planType=${planType || 'none'}, isGuestCheckout=${isGuestCheckout || false}`);
      
      // Handle the checkout session differently based on whether it's a guest checkout
      if (isGuestCheckout) {
        console.log(`Processing guest checkout for session: ${session.id}`);
        try {
          const result = await handleGuestCheckout(session, planType);
          console.log(`Guest checkout processing complete: ${JSON.stringify(result)}`);
        } catch (error) {
          console.error(`Error in guest checkout processing: ${error instanceof Error ? error.message : 'Unknown error'}`);
          throw error;
        }
      } else if (userId && planType) {
        // Existing flow for registered users
        console.log(`Processing registered user checkout for userId: ${userId}`);
        // Get subscription details from Stripe
        const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
        
        // Calculate end date
        const endDate = new Date(subscription.current_period_end * 1000).toISOString();
        
        // Create subscription in our database
        await SubscriptionService.createSubscription({
          userId,
          subscriptionType: planType,
          status: subscription.status as "active" | "canceled" | "incomplete" | "past_due" | "trialing",
          startDate: new Date(subscription.current_period_start * 1000).toISOString(),
          endDate,
          stripeSubscriptionId: subscription.id,
          stripePriceId: (subscription.items.data[0].price as Stripe.Price).id,
          stripeCustomerId: subscription.customer as string
        });
      } else {
        console.warn('Missing user ID or plan type in session metadata:', session.metadata);
      }

      // Sync Google users with Stripe subscriptions
      if (session.customer_email) {
        const stripeSyncService = (await import('@/lib/services/stripe-sync-service')).StripeSyncService;
        try {
          console.log(`Running validation for potential Google user with email: ${session.customer_email}`);
          await stripeSyncService.validateAndRegisterGoogleUser(session.customer_email);
        } catch (error) {
          console.error(`Error validating Google user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    }
    
    // Handle subscription updated event
    if (event.type === 'customer.subscription.updated') {
      const subscription = event.data.object as Stripe.Subscription;
      
      // Find the subscription in our database
      const stripeSubscriptionId = subscription.id;
      
      // Get all users with this subscription ID
      // Note: In a real app, you'd have a more efficient query for this
      const existingSubscriptions = await getSubscriptionByStripeId(stripeSubscriptionId);
      
      if (existingSubscriptions) {
        // Update subscription status
        await SubscriptionService.updateSubscriptionStatus(
          existingSubscriptions.id,
          subscription.status as "active" | "canceled" | "incomplete" | "past_due" | "trialing"
        );
        
        // Update end date
        await SubscriptionService.updateSubscriptionEndDate(
          existingSubscriptions.id,
          new Date(subscription.current_period_end * 1000).toISOString()
        );
      }
    }
    
    // Handle subscription canceled event
    if (event.type === 'customer.subscription.deleted') {
      const subscription = event.data.object as Stripe.Subscription;
      
      // Find the subscription in our database
      const stripeSubscriptionId = subscription.id;
      const existingSubscriptions = await getSubscriptionByStripeId(stripeSubscriptionId);
      
      if (existingSubscriptions) {
        // Cancel subscription
        await SubscriptionService.updateSubscriptionStatus(
          existingSubscriptions.id,
          'canceled'
        );
      }
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error handling webhook:', error);
    throw new Error('Failed to handle webhook event');
  }
}

/**
 * Handle checkout for guest users (who haven't registered yet)
 */
async function handleGuestCheckout(
  session: Stripe.Checkout.Session, 
  planType?: 'Standard' | 'Premium'
) {
  try {
    // When using guest checkout, we need the email from Stripe's checkout data
    const customerEmail = session.customer_email || session.customer_details?.email;
    
    if (!customerEmail) {
      console.error('No email found in session data for guest checkout', session.id);
      throw new Error('No email found for guest checkout');
    }
    
    const email = customerEmail;
    const customerId = session.customer as string;
    const subscriptionId = session.subscription as string;
    
    // Load required services
    const userService = (await import('@/lib/services/user-service')).UserService;
    
    // Check if user with this email already exists
    const existingUser = await userService.getUserByEmail(email);
    
    // If the user already exists and has a valid password (not a guest)
    if (existingUser && !existingUser.pendingPasswordSetup) {
      // Just link this subscription to their account
      await linkSubscriptionToExistingUser(existingUser.id, session, subscriptionId);
      return { success: true, userId: existingUser.id };
    }
    
    // Get subscription details from Stripe
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    // Set default plan type if not provided
    planType = planType || 'Standard';
    
    // 1. Create a temporary user in Neo4j if they don't exist yet
    let user = existingUser;
    
    if (!user) {
      // Get more details from customer data
      let customerName;
      try {
        // Attempt to get customer details from Stripe
        const customer = await stripe.customers.retrieve(customerId);
        if ('name' in customer && customer.name) {
          customerName = customer.name;
        }
      } catch (error) {
        console.error('Error fetching customer details:', error);
      }
      
      // Generate a secure random password
      const crypto = await import('crypto');
      const tempPassword = crypto.randomBytes(16).toString('hex');
      
      // Create the user with data from Stripe
      try {
        user = await userService.createUser({
          email,
          password: tempPassword, // This will be hashed
          name: customerName || email.split('@')[0], // Use customer name or first part of email
          stripeCustomerId: customerId,
          isGuest: true, // Explicitly set as guest user
          priceId: process.env.STRIPE_STANDARD_PRICE_ID ?? '',
        });
        
        console.log("Created user from Stripe checkout data:", user?.id || 'NO USER CREATED');
        
        if (!user) {
          console.error("Failed to create user from Stripe checkout data - null returned from createUser");
          throw new Error('Failed to create user from Stripe checkout data');
        }
      } catch (error) {
        console.error("Error creating user account:", error);
        throw new Error(`Failed to create user account: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    // 2. Create subscription record in Neo4j
    const endDate = new Date(subscription.current_period_end * 1000).toISOString();
    
    await SubscriptionService.createSubscription({
      userId: user.id,
      subscriptionType: planType,
      status: subscription.status as "active" | "canceled" | "incomplete" | "past_due" | "trialing",
      startDate: new Date(subscription.current_period_start * 1000).toISOString(),
      endDate,
      stripeSubscriptionId: subscription.id,
      stripePriceId: (subscription.items.data[0].price as Stripe.Price).id,
      stripeCustomerId: customerId
    });
    
    // 3. Send welcome email with instructions to create password
    const emailService = (await import('@/lib/email'));
    
    // Include additional data about customer if available
    const customerData = {
      name: user.name,
      email: user.email,
      checkoutSessionId: session.id
    };
    
    await emailService.sendWelcomeEmail(email, user.name, customerData);
    
    return { success: true, userId: user.id };
  } catch (error) {
    console.error('Error handling guest checkout:', error);
    throw error;
  }
}

/**
 * Link a new subscription to an existing user
 */
async function linkSubscriptionToExistingUser(
  userId: string,
  session: Stripe.Checkout.Session,
  subscriptionId: string
) {
  try {
    console.log(`Linking subscription ${subscriptionId} to existing user ${userId}`);
    
    // Get subscription details from Stripe
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const planType = session.metadata?.planType as 'Standard' | 'Premium' || 'Standard';
    
    // Check if user already has this subscription
    const subscriptionService = (await import('@/lib/services/subscription-service')).SubscriptionService;
    const existingSubscription = await subscriptionService.getSubscriptionByStripeId(subscriptionId);
    
    if (existingSubscription) {
      console.log(`Subscription ${subscriptionId} already exists in database, updating user link`);
      
      // If the subscription exists but is linked to a different user, update the link
      if (existingSubscription.userId !== userId) {
        await subscriptionService.updateSubscriptionUser(existingSubscription.id, userId);
        console.log(`Updated subscription ${existingSubscription.id} to link to user ${userId}`);
      } else {
        console.log(`Subscription ${existingSubscription.id} is already linked to user ${userId}`);
      }
      
      return { success: true };
    }
    
    // Create new subscription in our database
    const endDate = new Date(subscription.current_period_end * 1000).toISOString();
    
    console.log(`Creating new subscription record for user ${userId} with plan ${planType}`);
    
    await SubscriptionService.createSubscription({
      userId,
      subscriptionType: planType,
      status: subscription.status as "active" | "canceled" | "incomplete" | "past_due" | "trialing",
      startDate: new Date(subscription.current_period_start * 1000).toISOString(),
      endDate,
      stripeSubscriptionId: subscription.id,
      stripePriceId: (subscription.items.data[0].price as Stripe.Price).id,
      stripeCustomerId: subscription.customer as string
    });
    
    // Send notification email
    const emailService = (await import('@/lib/email'));
    const userService = (await import('@/lib/services/user-service')).UserService;
    const user = await userService.getUserById(userId);
    
    if (user) {
      await emailService.sendSubscriptionChangeEmail(
        user.email,
        planType,
        subscription.status,
        new Date().toISOString()
      );
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error linking subscription to existing user:', error);
    throw error;
  }
}

/**
 * Helper function to get a subscription by Stripe subscription ID
 */
async function getSubscriptionByStripeId(stripeSubscriptionId: string) {
  try {
    return await SubscriptionService.getSubscriptionByStripeId(stripeSubscriptionId);
  } catch (error) {
    console.error('Error finding subscription by Stripe ID:', error);
    return null;
  }
}

/**
 * Cancel a subscription
 */
export async function cancelUserSubscription() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    throw new Error('You must be logged in to cancel your subscription');
  }
  
  try {
    // Get the user's active subscription
    const subscription = await SubscriptionService.getUserSubscription(session.user.id);
    
    if (!subscription) {
      throw new Error('No active subscription found');
    }
    
    // Cancel the subscription in Stripe
    await stripe.subscriptions.cancel(subscription.stripeSubscriptionId);
    
    // Cancel the subscription in our database
    await SubscriptionService.cancelSubscription(subscription.id);
    
    return { success: true };
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw new Error('Failed to cancel subscription');
  }
} 