import { Analytics } from '@vercel/analytics/react';

/**
 * Simple analytics tracking module
 */
export const analytics = {
  /**
   * Track an event
   * @param event - The name of the event to track
   * @param properties - Optional properties to include with the event
   */
  track: (event: string, properties?: Record<string, any>) => {
    try {
      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.log('[Analytics]', event, properties);
      }
      
      // In production you might want to send to a real analytics service
      // This is a placeholder for any actual analytics implementation
    } catch (error) {
      console.error('Error tracking analytics event:', error);
    }
  }
};

// Export the Vercel Analytics component for use in _app.tsx
export { Analytics }; 