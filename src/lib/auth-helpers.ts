/**
 * Authentication helper functions for the client-side
 */

import { useSession } from 'next-auth/react';

/**
 * Fetches the current user data, trying different auth methods
 * @returns User data if authenticated, null otherwise
 */
export async function fetchUserData() {
  try {
    console.log('Fetching user data from API');
    
    // Attempt to fetch the user data with explicit credentials
    const response = await fetch('/api/auth/me', {
      // Include credentials to send cookies
      credentials: 'include',
      // Add cache control to prevent caching
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch user data: ${response.status} ${response.statusText}`);
      
      // If unauthorized, try refresh auth once
      if (response.status === 401) {
        console.log('Attempting to refresh authentication...');
        
        // Try to refresh the session
        const refreshResponse = await fetch('/api/auth/refresh', {
          method: 'POST',
          credentials: 'include',
          cache: 'no-store'
        });
        
        if (refreshResponse.ok) {
          // If refresh was successful, try the user data fetch again
          console.log('Authentication refreshed, trying user data fetch again');
          const retryResponse = await fetch('/api/auth/me', {
            credentials: 'include',
            cache: 'no-store'
          });
          
          if (retryResponse.ok) {
            const retryData = await retryResponse.json();
            if (retryData.success && retryData.user) {
              return retryData.user;
            }
          }
        }
      }
      
      // If we got here, all attempts failed
      console.error('All authentication attempts failed');
      return null;
    }
    
    const data = await response.json();
    
    if (data.success && data.user) {
      console.log('User data fetched successfully:', data.user.email);
      return data.user;
    }
    
    // If no user, return null
    console.warn('No user data found in successful response');
    return null;
  } catch (error) {
    console.error('Error fetching user data:', error);
    return null;
  }
}

/**
 * A hook that combines NextAuth session and custom JWT authentication
 * @returns Session data if authenticated, null otherwise
 */
export function useAuth() {
  const { data: session, status } = useSession();
  
  // If NextAuth session is available, use it
  if (session?.user) {
    return {
      user: session.user,
      isLoading: false,
      isAuthenticated: true
    };
  }
  
  // If NextAuth is still loading, return loading state
  if (status === 'loading') {
    return {
      user: null,
      isLoading: true,
      isAuthenticated: false
    };
  }
  
  // Otherwise, not authenticated with NextAuth
  return {
    user: null,
    isLoading: false,
    isAuthenticated: false
  };
}

/**
 * Checks if a user has a specific role
 * @param user The user to check
 * @param role The role to check for
 * @returns true if the user has the role, false otherwise
 */
export function hasRole(user: any, role: string) {
  return user?.role === role;
}

/**
 * Checks if a user is authenticated
 * @param user The user to check
 * @returns true if the user is authenticated, false otherwise
 */
export function isAuthenticated(user: any) {
  return !!user;
}

/**
 * Logs out the user by clearing cookies and redirecting to login page
 */
export async function logoutUser() {
  try {
    // Call the logout endpoint to clear cookies
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      credentials: 'include',
    });
    
    if (!response.ok) {
      console.error('Logout failed:', response.status, response.statusText);
    }
    
    // Redirect to login page
    window.location.href = '/login';
  } catch (error) {
    console.error('Error logging out:', error);
    // Attempt to redirect anyway
    window.location.href = '/login';
  }
}

/**
 * Check the current authentication status
 * @returns Promise<boolean> indicating if the user is currently authenticated
 */
export async function checkAuthStatus(): Promise<boolean> {
  try {
    const response = await fetch('/api/auth/me', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
    });
    
    return response.status === 200;
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
}

/**
 * Attempt to refresh the authentication session
 * @returns Promise<boolean> indicating if the refresh was successful
 */
export async function refreshAuthSession(): Promise<boolean> {
  try {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
    });
    
    if (response.status === 200) {
      const data = await response.json();
      return data.success === true;
    }
    
    return false;
  } catch (error) {
    console.error('Error refreshing authentication session:', error);
    return false;
  }
}

/**
 * Ensure the user is authenticated, refreshing the session if needed
 * @returns Promise<boolean> indicating if the user is authenticated after potential refresh
 */
export async function ensureAuthenticated(): Promise<boolean> {
  // First check current auth status
  const isAuthenticated = await checkAuthStatus();
  
  if (isAuthenticated) {
    return true;
  }
  
  // If not authenticated, try to refresh
  console.log('Session invalid, attempting refresh...');
  return await refreshAuthSession();
} 