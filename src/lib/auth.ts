import type { NextAuthOptions } from 'next-auth';
import { User } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';

// Extend the User interface to include custom properties
interface ExtendedUser extends User {
  id: string;
  role: string;
}

// Declare module for type augmentation
declare module 'next-auth' {
  interface User {
    id: string;
    role: string;
  }
  
  interface Session {
    user: {
      id: string;
      role: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      subscription?: {
        type: string;
        status: string;
        currentPeriodEnd: string;
      };
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: string;
    subscription?: {
      type: string;
      status: string;
      currentPeriodEnd: string;
      stripePriceId?: string;
      cached?: boolean;
      cachedAt?: number;
    };
  }
}

// Log the NextAuth configuration
console.log('[NextAuth] Configuration initialized');
console.log('[NextAuth] NEXTAUTH_URL:', process.env.NEXTAUTH_URL);
console.log('[NextAuth] NEXTAUTH_SECRET exists:', !!process.env.NEXTAUTH_SECRET);

// Define a base URL for API calls that works in all environments
const getBaseUrl = () => {
  // Check for NEXTAUTH_URL (preferred)
  if (process.env.NEXTAUTH_URL) {
    return process.env.NEXTAUTH_URL.replace(/\/$/, ''); // Remove trailing slash if present
  }
  
  // Return empty string to use relative URLs instead of hardcoded localhost
  return '';
};

// Create a function to make API requests with consistent error handling
async function makeApiRequest(endpoint: string, payload: any) {
  const baseUrl = getBaseUrl();
  const url = baseUrl ? `${baseUrl}${endpoint}` : endpoint;
  
  console.log(`[NextAuth] Making API request to: ${url}`);
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
      cache: 'no-store',
    });
    
    console.log(`[NextAuth] API response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      console.error(`[NextAuth] API error (${response.status}): ${errorText}`);
      throw new Error(`API error: ${response.status} ${errorText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('[NextAuth] API request failed:', error);
    throw error;
  }
}

// Cache for subscription data to avoid redundant DB calls
// TTL is 1 hour by default - this significantly reduces load on the database
const subscriptionCache = new Map<string, {
  data: any;
  timestamp: number;
}>();
const CACHE_TTL = 60 * 60 * 1000; // 1 hour in milliseconds

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID ?? '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? '',
      profile(profile) {
        console.log('[NextAuth] Google profile:', profile);
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
          role: 'user', // Default role for Google users
        };
      }
    }),
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        console.log('[NextAuth] Authorize function called');
        // Replace with your actual user validation logic
        if (!credentials?.email || !credentials?.password) {
          console.log('[NextAuth] Missing credentials');
          return null;
        }
        
        try {
          console.log('[NextAuth] Validating credentials for:', credentials.email);
          
          const data = await makeApiRequest('/api/auth/validate', {
            email: credentials.email,
            password: credentials.password,
          });
          
          console.log('[NextAuth] Validation response success:', data.success);
          
          if (data.success && data.user) {
            console.log('[NextAuth] User authenticated:', data.user.email);
            return data.user as ExtendedUser;
          }
          
          console.log('[NextAuth] Authentication failed: Invalid credentials');
          return null;
        } catch (error) {
          console.error('[NextAuth] Auth error:', error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      console.log('[NextAuth] Sign in callback called');
      
      if (account?.provider === 'google' && user?.email) {
        try {
          console.log('[NextAuth] Processing Google sign-in for email:', user.email);
          
          // Import the Stripe sync service
          const stripeSyncService = (await import('@/lib/services/stripe-sync-service')).StripeSyncService;
          const stripe = (await import('@/lib/stripe/stripe')).stripe;
          
          // First check if this email exists in Stripe customers
          const customers = await stripe.customers.list({ 
            email: user.email, 
            limit: 1 
          });
          
          if (customers.data.length > 0) {
            console.log('[NextAuth] Found matching Stripe customer for Google user:', user.email);
            const customer = customers.data[0];
            
            // Check for active subscriptions
            const subscriptions = await stripe.subscriptions.list({
              customer: customer.id,
              status: 'active',
              limit: 1
            });
            
            // Get subscription type if exists
            let subscriptionType = 'Free';
            let priceId = '';
            
            if (subscriptions.data.length > 0) {
              const subscription = subscriptions.data[0];
              priceId = (subscription.items.data[0].price as any).id;
              subscriptionType = priceId === process.env.STRIPE_PREMIUM_PRICE_ID ? 'Premium' : 'Standard';
              console.log('[NextAuth] Found active subscription for Google user:', subscriptionType);
            }
            
            // Register or update Google user with subscription info
            const result = await stripeSyncService.registerGoogleUserWithSubscription(
              user.email,
              user.id.toString(),
              user.name || '',
              customer.id,
              subscriptionType,
              priceId
            );
            
            if (result.success && result.userId) {
              console.log('[NextAuth] Updated Google user with Stripe data, Neo4j user ID:', result.userId);
              user.id = result.userId;
            }
          } else {
            console.log('[NextAuth] No Stripe customer found for Google user, checking Neo4j only');
            // Just register/link the Google account in Neo4j without Stripe data
            const result = await stripeSyncService.registerGoogleUserWithSubscription(
              user.email,
              user.id.toString(),
              user.name || ''
            );
            
            if (result.success && result.userId) {
              console.log('[NextAuth] Linked Google account with Neo4j user ID:', result.userId);
              user.id = result.userId;
            }
          }
        } catch (error) {
          console.error('[NextAuth] Error processing Google sign-in:', error);
          // Continue sign in even if registration fails
        }
      }
      
      return true;
    },
    jwt({ token, user }) {
      console.log('[NextAuth] JWT callback called');
      if (user) {
        console.log('[NextAuth] Adding user data to token:', user.email);
        token.id = user.id;
        token.role = user.role;
      }
      return token;
    },
    session: async ({ session, token }) => {
      console.log('[NextAuth] Session callback called');
      if (token) {
        console.log('[NextAuth] Adding user data to token:', token.email);
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        
        // Add subscription info if available from token
        if (token.subscription) {
          // Check if we need to refresh the cache
          const shouldRefresh = !token.subscription.cached || 
                               !token.subscription.cachedAt || 
                               Date.now() - token.subscription.cachedAt > CACHE_TTL;
          
          if (!shouldRefresh) {
            // Use cached subscription data from the token
            session.user.subscription = {
              type: token.subscription.type,
              status: token.subscription.status,
              currentPeriodEnd: token.subscription.currentPeriodEnd
            };
            return session;
          }
        }
        
        // Only fetch subscription if we have a user email and need to refresh
        if (session.user?.email) {
          try {
            // Check cache first
            const cacheKey = session.user.id;
            const cachedData = subscriptionCache.get(cacheKey);
            const now = Date.now();
            
            if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
              // Use cached data
              const subscription = cachedData.data;
              if (subscription) {
                session.user.subscription = {
                  type: subscription.type,
                  status: subscription.status,
                  currentPeriodEnd: subscription.endDate || subscription.currentPeriodEnd
                };
                
                // Also update token for future use
                token.subscription = {
                  type: subscription.type,
                  status: subscription.status,
                  currentPeriodEnd: subscription.endDate || subscription.currentPeriodEnd,
                  stripePriceId: subscription.stripePriceId,
                  cached: true,
                  cachedAt: now
                };
              }
            } else {
              // Fetch subscription data - prioritize Stripe over Neo4j
              const { SubscriptionService } = await import('@/lib/services/subscription-service');
              let subscription = null;
              
              // Try direct Stripe validation first (fastest source of truth)
              if (session.user.email) {
                subscription = await SubscriptionService.validateSubscriptionFromStripe(session.user.email);
              }
              
              // If no Stripe subscription, check Neo4j as fallback
              if (!subscription) {
                subscription = await SubscriptionService.getUserSubscription(session.user.id);
              }
              
              // If found, update session and cache
              if (subscription) {
                session.user.subscription = {
                  type: subscription.type,
                  status: subscription.status,
                  currentPeriodEnd: subscription.endDate
                };
                
                // Update token for future use
                token.subscription = {
                  type: subscription.type,
                  status: subscription.status,
                  currentPeriodEnd: subscription.endDate,
                  stripePriceId: subscription.stripePriceId,
                  cached: true,
                  cachedAt: now
                };
                
                // Update cache
                subscriptionCache.set(cacheKey, {
                  data: subscription,
                  timestamp: now
                });
              }
            }
          } catch (error) {
            console.error('[NextAuth] Error fetching subscription for session:', error);
          }
        }
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      console.log(`[NextAuth] Redirect callback called with URL: ${url}, baseUrl: ${baseUrl}`);
      
      // If the URL starts with the base URL, it's safe to redirect
      if (url.startsWith(baseUrl)) {
        console.log(`[NextAuth] Redirecting to: ${url}`);
        return url;
      } 
      // Handle relative URLs
      else if (url.startsWith('/')) {
        const redirectUrl = `${baseUrl}${url}`;
        console.log(`[NextAuth] Redirecting to: ${redirectUrl}`);
        return redirectUrl;
      }
      // If URL contains callbackUrl parameter, extract and use it
      else if (url.includes('callbackUrl=')) {
        try {
          const callbackUrl = new URL(url).searchParams.get('callbackUrl');
          if (callbackUrl) {
            console.log(`[NextAuth] Found callbackUrl parameter: ${callbackUrl}`);
            // If callback URL is relative, append base URL
            if (callbackUrl.startsWith('/')) {
              const finalUrl = `${baseUrl}${callbackUrl}`;
              console.log(`[NextAuth] Redirecting to relative callbackUrl: ${finalUrl}`);
              return finalUrl;
            }
            // Otherwise use the callback URL as is if it's on the same domain
            if (callbackUrl.startsWith(baseUrl)) {
              console.log(`[NextAuth] Redirecting to external callbackUrl: ${callbackUrl}`);
              return callbackUrl;
            }
          }
        } catch (err) {
          console.error(`[NextAuth] Error parsing callbackUrl:`, err);
        }
      }
      
      // Default redirect to dashboard
      console.log(`[NextAuth] Redirecting to default dashboard: ${baseUrl}/dashboard`);
      return `${baseUrl}/dashboard`;
    }
  },
  pages: {
    signIn: '/login',
    error: '/login',
  },
  // Use NEXTAUTH_SECRET instead of JWT_SECRET for consistency
  secret: process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET,
  session: {
    strategy: 'jwt',
    // Set the maximum age to 7 days
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  // Enable debug for detailed logs
  debug: process.env.NODE_ENV === 'development',
  logger: {
    error(code, metadata) {
      console.error(`[NextAuth] Error: ${code}`, metadata);
    },
    warn(code) {
      console.warn(`[NextAuth] Warning: ${code}`);
    },
    debug(code, metadata) {
      console.log(`[NextAuth] Debug: ${code}`, metadata);
    }
  }
};

export default authOptions; 