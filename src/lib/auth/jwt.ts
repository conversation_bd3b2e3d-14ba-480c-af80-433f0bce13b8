import { jwtVerify } from 'jose';

/**
 * Get the JWT secret key from environment variables
 */
export function getJwtSecretKey(): Uint8Array {
  const secret = process.env.JWT_SECRET_KEY;
  
  if (!secret) {
    throw new Error('JWT_SECRET_KEY is not set in environment variables');
  }
  
  return new TextEncoder().encode(secret);
}

/**
 * Verify and decode the JWT token
 */
export async function verifyJwtToken<T>(token: string): Promise<T> {
  try {
    const { payload } = await jwtVerify(token, getJwtSecretKey());
    return payload as unknown as T;
  } catch (error) {
    throw new Error('Invalid token');
  }
} 