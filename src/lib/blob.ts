'use server';

import { put, list, del, PutBlobResult } from '@vercel/blob';
import { executeQuery } from './neo4j';

/**
 * Upload an image to Vercel Blob
 */
export async function uploadImageToBlob(
  file: File,
  userId: string,
  metadata?: Record<string, string>
): Promise<PutBlobResult> {
  try {
    // Generate a unique filename
    const timestamp = Date.now();
    const uniqueFilename = `${userId}/${timestamp}-${file.name}`;
    
    // Upload the file to Vercel Blob
    const blob = await put(uniqueFilename, file, {
      access: 'public',
      contentType: file.type,
    });
    
    // Store the reference in our database
    await storeImageReference(
      userId, 
      blob.url, 
      file.name, 
      metadata?.prompt || '',
      timestamp
    );
    
    return blob;
  } catch (error) {
    console.error('Error uploading image to Blob:', error);
    throw new Error('Failed to upload image');
  }
}

/**
 * Store an image reference in our database
 */
async function storeImageReference(
  userId: string,
  url: string,
  filename: string,
  prompt: string,
  createdAt: number
): Promise<boolean> {
  try {
    const query = `
      MATCH (u:User {id: $userId})
      CREATE (i:Image {
        id: randomUUID(),
        url: $url,
        filename: $filename,
        prompt: $prompt,
        createdAt: datetime({epochMillis: toInteger($createdAt)})
      })
      CREATE (u)-[:CREATED]->(i)
      RETURN i.id
    `;
    
    const result = await executeQuery(query, { userId, url, filename, prompt, createdAt });
    return result.length > 0;
  } catch (error) {
    console.error('Error storing image reference:', error);
    return false;
  }
}

/**
 * Get a user's recent image generations
 */
export async function getRecentImages(
  userId: string,
  limit: number = 10
): Promise<Array<{
  id: string;
  url: string;
  filename: string;
  prompt: string;
  createdAt: string;
}>> {
  try {
    const query = `
      MATCH (u:User {id: $userId})-[:CREATED]->(i:Image)
      RETURN i.id as id, i.url as url, i.filename as filename, 
             i.prompt as prompt, toString(i.createdAt) as createdAt
      ORDER BY i.createdAt DESC
      LIMIT $limit
    `;
    
    const result = await executeQuery(query, { userId, limit });
    
    return result.map(record => {
      const obj = record.toObject();
      return {
        id: obj.id,
        url: obj.url,
        filename: obj.filename,
        prompt: obj.prompt,
        createdAt: obj.createdAt
      };
    });
  } catch (error) {
    console.error('Error fetching recent images:', error);
    return [];
  }
}

/**
 * Delete an image from Blob and the database
 */
export async function deleteImage(
  imageId: string,
  userId: string
): Promise<boolean> {
  try {
    // First, get the image URL from our database
    const query = `
      MATCH (u:User {id: $userId})-[:CREATED]->(i:Image {id: $imageId})
      RETURN i.url as url
    `;
    
    const result = await executeQuery(query, { userId, imageId });
    
    if (result.length === 0) {
      return false;
    }
    
    const url = result[0].toObject().url;
    
    // Delete from Vercel Blob
    await del(url);
    
    // Delete from our database
    const deleteQuery = `
      MATCH (u:User {id: $userId})-[:CREATED]->(i:Image {id: $imageId})
      DETACH DELETE i
      RETURN count(i) as deleted
    `;
    
    const deleteResult = await executeQuery(deleteQuery, { userId, imageId });
    const deleted = deleteResult[0].toObject().deleted;
    
    return deleted > 0;
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
} 