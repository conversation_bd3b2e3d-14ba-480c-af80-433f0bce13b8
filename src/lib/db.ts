import neo4j, { Driver } from 'neo4j-driver';

// Initialize Neo4j driver with environment variables
export function initNeo4j() {
  const uri = process.env.NEO4J_URI || 'bolt://localhost:7687';
  const user = process.env.NEO4J_USER || 'neo4j';
  const password = process.env.NEO4J_PASSWORD || 'password';

  const driver = neo4j.driver(uri, neo4j.auth.basic(user, password));

  return driver;
}

// Get a Neo4j session
export async function getSession() {
  const driver = initNeo4j();
  return driver.session();
}

// Close the Neo4j connection
export async function closeConnection(driver: Driver) {
  await driver.close();
} 