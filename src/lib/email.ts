import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";
import nodemailer from 'nodemailer';
import type { Transporter } from 'nodemailer';

// Validate AWS SES configuration
function validateAWSConfig(): boolean {
  const requiredVars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_REGION', 'EMAIL_SENDER'];
  const missing = requiredVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    console.error('Missing required AWS SES environment variables:', missing);
    return false;
  }

  return true;
}

// Initialize SES client with validation
let sesClient: SESClient | null = null;

try {
  if (validateAWSConfig()) {
    sesClient = new SESClient({
      region: process.env.AWS_REGION!,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
    });
    console.log('AWS SES client initialized successfully');
  } else {
    console.warn('AWS SES client not initialized due to missing configuration');
  }
} catch (error) {
  console.error('Failed to initialize AWS SES client:', error);
}

// Configure email transport (optional - only if SMTP is explicitly configured)
let transporter: Transporter | undefined;
if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
  try {
    transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
    console.log('Email transporter configured with SMTP');
  } catch (error) {
    console.error('Failed to initialize email transporter:', error);
  }
}
// Note: Contact forms use FormBold integration and don't require SMTP configuration

// Email types and templates
export type EmailType = 'password-reset' | 'welcome' | 'subscription-change';

interface EmailTemplate {
  subject: string;
  body: (params: Record<string, string>) => string;
}

// Simple templates for different email types
const EMAIL_TEMPLATES: Record<EmailType, EmailTemplate> = {
  'password-reset': {
    subject: 'Reset Your AstroStudio Password',
    body: (params) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #4F46E5;">Reset Your Password</h1>
        <p>Hello,</p>
        <p>You've requested to reset your password for your AstroStudio account. Please click the link below to reset it:</p>
        <p>
          <a 
            href="${params.resetLink}" 
            style="
              background-color: #4F46E5; 
              color: white; 
              padding: 10px 20px; 
              text-decoration: none;
              border-radius: 5px;
              display: inline-block;
              margin: 10px 0;
            "
          >
            Reset Password
          </a>
        </p>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request this password reset, please ignore this email or contact support if you have concerns.</p>
        <p>Thanks,<br>The AstroStudio Team</p>
      </div>
    `,
  },
  'welcome': {
    subject: 'Welcome to AstroStudio!',
    body: (params) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #4F46E5;">Welcome to AstroStudio!</h1>
        <p>Hello ${params.name || 'there'},</p>
        <p>Thank you for joining AstroStudio. We're excited to have you on board!</p>
        <p>With AstroStudio, you can create amazing AI-generated content including images, audio, videos, and more.</p>
        <p>Get started by exploring our tools:</p>
        <p>
          <a 
            href="${params.dashboardLink}" 
            style="
              background-color: #4F46E5; 
              color: white; 
              padding: 10px 20px; 
              text-decoration: none;
              border-radius: 5px;
              display: inline-block;
              margin: 10px 0;
            "
          >
            Go to Dashboard
          </a>
        </p>
        <p>If you have any questions, please don't hesitate to reach out to our support team.</p>
        <p>Best regards,<br>The AstroStudio Team</p>
      </div>
    `,
  },
  'subscription-change': {
    subject: 'Your AstroStudio Subscription Has Been Updated',
    body: (params) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #4F46E5;">Subscription Update</h1>
        <p>Hello,</p>
        <p>Your AstroStudio subscription has been updated. Here are the details:</p>
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
          <p><strong>Plan:</strong> ${params.plan}</p>
          <p><strong>Status:</strong> ${params.status}</p>
          <p><strong>Effective Date:</strong> ${params.date}</p>
        </div>
        <p>You can manage your subscription anytime from your account settings:</p>
        <p>
          <a 
            href="${params.accountLink}" 
            style="
              background-color: #4F46E5; 
              color: white; 
              padding: 10px 20px; 
              text-decoration: none;
              border-radius: 5px;
              display: inline-block;
              margin: 10px 0;
            "
          >
            Manage Subscription
          </a>
        </p>
        <p>If you have any questions about your subscription, please contact our support team.</p>
        <p>Thank you for choosing AstroStudio!</p>
        <p>Best regards,<br>The AstroStudio Team</p>
      </div>
    `,
  },
};

/**
 * Send an email using AWS SES
 */
export async function sendEmail(
  to: string,
  emailType: EmailType,
  params: Record<string, string>
): Promise<boolean> {
  try {
    // Check if SES client is available
    if (!sesClient) {
      console.error('AWS SES client not available. Please check your AWS configuration.');
      return false;
    }

    const template = EMAIL_TEMPLATES[emailType];
    if (!template) {
      throw new Error(`Email template not found for type: ${emailType}`);
    }

    const sender = process.env.EMAIL_SENDER || '<EMAIL>';

    const command = new SendEmailCommand({
      Source: sender,
      Destination: {
        ToAddresses: [to],
      },
      Message: {
        Subject: {
          Data: template.subject,
          Charset: 'UTF-8',
        },
        Body: {
          Html: {
            Data: template.body(params),
            Charset: 'UTF-8',
          },
        },
      },
    });

    const response = await sesClient.send(command);
    console.log('Email sent successfully:', response);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

/**
 * Get password reset email template with internationalization
 */
function getPasswordResetTemplate(locale: string = 'en') {
  const translations = {
    en: {
      subject: 'Reset your AstroStudio AI password',
      greeting: 'Hello',
      title: 'Password Reset Request',
      message: 'You requested to reset your password for your AstroStudio AI account. Click the button below to reset your password:',
      resetButton: 'Reset Password',
      expiry: 'This link will expire in 1 hour for security reasons.',
      noRequest: 'If you didn\'t request this password reset, you can safely ignore this email.',
      support: 'If you have any questions, please contact our support team.',
      footer: 'Best regards,<br>The AstroStudio AI Team'
    },
    es: {
      subject: 'Restablece tu contraseña de AstroStudio AI',
      greeting: 'Hola',
      title: 'Solicitud de Restablecimiento de Contraseña',
      message: 'Solicitaste restablecer tu contraseña para tu cuenta de AstroStudio AI. Haz clic en el botón de abajo para restablecer tu contraseña:',
      resetButton: 'Restablecer Contraseña',
      expiry: 'Este enlace expirará en 1 hora por razones de seguridad.',
      noRequest: 'Si no solicitaste este restablecimiento de contraseña, puedes ignorar este correo electrónico de forma segura.',
      support: 'Si tienes alguna pregunta, por favor contacta a nuestro equipo de soporte.',
      footer: 'Saludos cordiales,<br>El Equipo de AstroStudio AI'
    }
  };

  return translations[locale as keyof typeof translations] || translations.en;
}

/**
 * Generate professional HTML email template for password reset
 */
function generatePasswordResetHTML(resetLink: string, locale: string = 'en', userName?: string): string {
  const t = getPasswordResetTemplate(locale);

  return `
    <!DOCTYPE html>
    <html lang="${locale}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.subject}</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #000000; color: #ffffff;">
      <div style="max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #141493 0%, #417ef7 100%); padding: 40px 20px;">
        <!-- Header with Logo -->
        <div style="text-align: center; margin-bottom: 40px;">
          <div style="background-color: rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 20px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2);">
            <h1 style="color: #ffffff; font-size: 28px; font-weight: bold; margin: 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">
              AstroStudio AI
            </h1>
          </div>
        </div>

        <!-- Main Content -->
        <div style="background-color: rgba(255, 255, 255, 0.05); border-radius: 16px; padding: 30px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1);">
          <h2 style="color: #ffffff; font-size: 24px; font-weight: 600; margin: 0 0 20px 0; text-align: center;">
            ${t.title}
          </h2>

          <p style="color: #e5e7eb; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
            ${t.greeting}${userName ? ` ${userName}` : ''},
          </p>

          <p style="color: #e5e7eb; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
            ${t.message}
          </p>

          <!-- Reset Button -->
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetLink}"
               style="display: inline-block; background: linear-gradient(135deg, #417ef7 0%, #141493 100%); color: #ffffff; text-decoration: none; padding: 16px 32px; border-radius: 12px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 15px rgba(65, 126, 247, 0.3); transition: all 0.3s ease;">
              ${t.resetButton}
            </a>
          </div>

          <!-- Security Notice -->
          <div style="background-color: rgba(255, 255, 255, 0.05); border-radius: 8px; padding: 20px; margin: 30px 0; border-left: 4px solid #417ef7;">
            <p style="color: #fbbf24; font-size: 14px; line-height: 1.5; margin: 0 0 10px 0; font-weight: 500;">
              ⚠️ ${t.expiry}
            </p>
            <p style="color: #d1d5db; font-size: 14px; line-height: 1.5; margin: 0;">
              ${t.noRequest}
            </p>
          </div>

          <!-- Support -->
          <p style="color: #9ca3af; font-size: 14px; line-height: 1.5; margin: 20px 0 0 0; text-align: center;">
            ${t.support}
          </p>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255, 255, 255, 0.1);">
          <p style="color: #9ca3af; font-size: 14px; line-height: 1.5; margin: 0;">
            ${t.footer}
          </p>
          <p style="color: #6b7280; font-size: 12px; margin: 10px 0 0 0;">
            © ${new Date().getFullYear()} AstroStudio AI. All rights reserved.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Send a password reset email with internationalization support
 */
export async function sendPasswordResetEmail(
  email: string,
  resetToken: string,
  locale: string = 'en',
  userName?: string
): Promise<boolean> {
  try {
    // Check if SES client is available
    if (!sesClient) {
      console.error('AWS SES client not available. Please check your AWS configuration.');
      return false;
    }

    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const localePrefix = locale === 'es' ? '/es' : '';
    const resetLink = `${baseUrl}${localePrefix}/reset-password/${resetToken}`;

    const t = getPasswordResetTemplate(locale);
    const htmlContent = generatePasswordResetHTML(resetLink, locale, userName);

    const sender = process.env.EMAIL_SENDER || '<EMAIL>';

    console.log(`Attempting to send password reset email to ${email} from ${sender}`);

    const command = new SendEmailCommand({
      Source: sender,
      Destination: {
        ToAddresses: [email],
      },
      Message: {
        Subject: {
          Data: t.subject,
          Charset: 'UTF-8',
        },
        Body: {
          Html: {
            Data: htmlContent,
            Charset: 'UTF-8',
          },
        },
      },
    });

    const response = await sesClient.send(command);
    console.log('Password reset email sent successfully:', response);
    return true;
  } catch (error) {
    console.error('Error sending password reset email:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }
    return false;
  }
}

/**
 * Send a welcome email to a new user with instructions to create their password
 */
export async function sendWelcomeEmail(
  email: string, 
  name: string, 
  customerData?: Record<string, string>
): Promise<boolean> {
  try {
    const appUrl = process.env.NEXT_PUBLIC_APP_URL ?? 'http://localhost:3001';
    
    // Add checkout session ID to the URL if available
    let setupUrl = `${appUrl}/auth/set-password?email=${encodeURIComponent(email)}`;
    if (customerData?.checkoutSessionId) {
      setupUrl += `&session_id=${customerData.checkoutSessionId}`;
    }
    
    // Try to use AWS SES first
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
      return sendEmail(email, 'welcome', { 
        name, 
        dashboardLink: setupUrl
      });
    }
    
    // Fallback to nodemailer if AWS credentials are not set
    const mailOptions = {
      from: `"${process.env.SMTP_FROM_NAME || 'Astro Studio'}" <${process.env.SMTP_FROM_EMAIL || process.env.SMTP_USER}>`,
      to: email,
      subject: 'Welcome to Astro Studio - Complete Your Account Setup',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Welcome to Astro Studio, ${name}!</h2>
          <p>Thank you for your subscription! Your account has been created and your subscription is now active.</p>
          <p>To complete your account setup, please create a password by clicking the link below:</p>
          <p style="margin: 20px 0;">
            <a href="${setupUrl}" 
               style="background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Create Password
            </a>
          </p>
          <p>If you have any questions or need assistance, please contact our support team.</p>
          <p>Thank you,<br>The Astro Studio Team</p>
        </div>
      `,
    };
    
    // Only try to send mail if transporter exists
    if (transporter) {
      try {
        await transporter.sendMail(mailOptions);
        return true;
      } catch (error) {
        console.error('Failed to send welcome email via SMTP:', error);
        return false;
      }
    }

    // Email transport not configured - this is expected when using FormBold for contact forms
    return false;
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return false;
  }
}

/**
 * Send a subscription change notification email
 */
export async function sendSubscriptionChangeEmail(
  email: string,
  plan: string,
  status: string,
  date: string
): Promise<boolean> {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const accountLink = `${baseUrl}/settings/billing`;
  
  return sendEmail(email, 'subscription-change', { 
    plan, 
    status, 
    date, 
    accountLink 
  });
}

/**
 * Send a receipt email for a subscription
 */
export async function sendReceiptEmail(email: string, name: string, subscriptionType: string, amount: number): Promise<boolean> {
  try {
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    
    // Try to use AWS SES first
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
      // Use SES to send email
      return true;
    }
    
    // Fallback to nodemailer if AWS credentials are not set
    const mailOptions = {
      from: `"${process.env.SMTP_FROM_NAME || 'Astro Studio'}" <${process.env.SMTP_FROM_EMAIL || process.env.SMTP_USER}>`,
      to: email,
      subject: 'Astro Studio - Subscription Receipt',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Thank You for Your Subscription!</h2>
          <p>Hello ${name},</p>
          <p>We're writing to confirm your subscription to Astro Studio's ${subscriptionType} plan.</p>
          
          <div style="border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Receipt Details</h3>
            <p><strong>Plan:</strong> ${subscriptionType}</p>
            <p><strong>Amount:</strong> $${(amount / 100).toFixed(2)}</p>
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
          </div>
          
          <p>You can view your subscription details and manage your account by visiting:</p>
          <p style="margin: 20px 0;">
            <a href="${appUrl}/dashboard/settings/billing" 
               style="background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Manage Subscription
            </a>
          </p>
          
          <p>If you have any questions or need assistance, please contact our support team.</p>
          <p>Thank you,<br>The Astro Studio Team</p>
        </div>
      `,
    };
    
    // Only try to send mail if transporter exists
    if (transporter) {
      try {
        await transporter.sendMail(mailOptions);
        return true;
      } catch (error) {
        console.error('Failed to send receipt email via SMTP:', error);
        return false;
      }
    }

    // Email transport not configured - this is expected when using FormBold for contact forms
    return false;
  } catch (error) {
    console.error('Error sending receipt email:', error);
    return false;
  }
} 