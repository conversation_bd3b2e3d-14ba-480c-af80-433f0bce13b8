// Utility functions for image generation and editing

// Define interfaces for models
export interface AIModel {
  id: string;
  name: string;
  description: string;
  provider: string;
  capabilities?: string[];
  maxDuration?: number;
  [key: string]: any; // For additional tool-specific properties
}

// Define model categories interface
export interface ModelCategories {
  [category: string]: AIModel[];
}

// Define generated image interface
export interface GeneratedImage {
  id: number;
  url: string;
  prompt?: string;
  negativePrompt?: string;
  model?: string;
  width?: number;
  height?: number;
  timestamp?: Date;
  is_edited?: boolean;
  original_image?: string;
  provider?: string;
  raw_response?: any;
}

// Define upload status type
export type UploadStatus = "idle" | "uploading" | "success" | "error";

// Define download status type
export type DownloadStatus = "idle" | "converting" | "downloading" | "error";

// Dimension presets
export interface Dimension {
  name: string;
  width: number;
  height: number;
}

export const dimensions: Dimension[] = [
  { name: "Square", width: 1024, height: 1024 },
  { name: "Portrait", width: 768, height: 1024 },
  { name: "Landscape", width: 1024, height: 768 },
  { name: "Widescreen", width: 1280, height: 720 },
  { name: "Mobile", width: 512, height: 1024 },
  { name: "Banner", width: 1280, height: 640 },
];

// Download format options interface
export interface DownloadFormat {
  id: string;
  label: string;
  extension: string;
  mimeType: string;
}

// Download formats
export const downloadFormats: DownloadFormat[] = [
  { id: "webp", label: "WebP", extension: "webp", mimeType: "image/webp" },
  { id: "jpg", label: "JPG", extension: "jpg", mimeType: "image/jpeg" },
  { id: "png", label: "PNG", extension: "png", mimeType: "image/png" },
  { id: "svg", label: "SVG", extension: "svg", mimeType: "image/svg+xml" },
];

// Function to download an image given its URL
export const downloadImageDirectly = async (
  url: string,
  filename: string
): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      console.log(
        `Attempting to download ${filename} from ${url.substring(0, 30)}...`,
      );

      // Create a download link
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      a.target = "_blank"; // Ensures it doesn't replace current page
      a.style.display = "none";
      document.body.appendChild(a);

      // Click the link to start download
      a.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        resolve();
      }, 1000);
    } catch (err) {
      console.error("Error in downloadImageDirectly:", err);
      reject(err);
    }
  });
};

// Helper function to check image format by examining headers
export const checkImageFormat = async (url: string): Promise<string> => {
  try {
    // Check URL patterns first (most reliable way without causing CORS issues)
    // Check for extensions in the URL
    if (url.match(/\.webp($|\?)/i)) return "webp";
    if (url.match(/\.jpe?g($|\?)/i)) return "jpg";
    if (url.match(/\.png($|\?)/i)) return "png";
    if (url.match(/\.svg($|\?)/i)) return "svg";

    // Check for format hints in query parameters
    if (url.includes("format=webp") || url.includes("fmt=webp"))
      return "webp";
    if (url.includes("format=jpg") || url.includes("fmt=jpg")) return "jpg";
    if (url.includes("format=png") || url.includes("fmt=png")) return "png";

    // For data URLs, extract the MIME type
    if (url.startsWith("data:")) {
      const mimeMatch = url.match(/^data:(image\/[\w+]+);/);
      if (mimeMatch && mimeMatch[1]) {
        const mimeType = mimeMatch[1];
        if (mimeType === "image/webp") return "webp";
        if (mimeType === "image/jpeg") return "jpg";
        if (mimeType === "image/png") return "png";
        if (mimeType === "image/svg+xml") return "svg";
      }
    }

    // For AI services, most commonly use WebP by default
    if (
      url.includes("replicate.delivery") ||
      url.includes("together.ai") ||
      url.includes("cloudflare")
    ) {
      return "webp";
    }

    // Default to webp as that's what most AI services use
    return "webp";
  } catch (error) {
    console.error("Error checking image format:", error);
    return "webp"; // Default to webp as a fallback
  }
}; 