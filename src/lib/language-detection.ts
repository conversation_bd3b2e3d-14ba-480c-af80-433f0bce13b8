// Spanish-speaking countries for IP-based detection
const SPANISH_SPEAKING_COUNTRIES = [
  'ES', // Spain
  'MX', // Mexico
  'AR', // Argentina
  'CO', // Colombia
  'PE', // Peru
  'VE', // Venezuela
  'CL', // Chile
  'EC', // Ecuador
  'GT', // Guatemala
  'CU', // Cuba
  'BO', // Bolivia
  'DO', // Dominican Republic
  'HN', // Honduras
  'PY', // Paraguay
  'SV', // El Salvador
  'NI', // Nicaragua
  'CR', // Costa Rica
  'PA', // Panama
  'UY', // Uruguay
  'PR', // Puerto Rico
  'GQ', // Equatorial Guinea
];

// Browser language codes that indicate Spanish preference
const SPANISH_LANGUAGE_CODES = [
  'es',
  'es-ES', // Spain
  'es-MX', // Mexico
  'es-AR', // Argentina
  'es-CO', // Colombia
  'es-PE', // Peru
  'es-VE', // Venezuela
  'es-CL', // Chile
  'es-EC', // Ecuador
  'es-GT', // Guatemala
  'es-CU', // Cuba
  'es-BO', // Bolivia
  'es-DO', // Dominican Republic
  'es-HN', // Honduras
  'es-PY', // Paraguay
  'es-SV', // El Salvador
  'es-NI', // Nicaragua
  'es-CR', // Costa Rica
  'es-PA', // Panama
  'es-UY', // Uruguay
  'es-PR', // Puerto Rico
];

/**
 * Detects user's preferred language based on browser settings
 */
export function detectBrowserLanguage(): 'en' | 'es' | null {
  if (typeof window === 'undefined') return null;

  // Check navigator.language first
  const primaryLanguage = navigator.language?.toLowerCase();
  if (primaryLanguage && SPANISH_LANGUAGE_CODES.includes(primaryLanguage)) {
    return 'es';
  }

  // Check navigator.languages array
  const languages = navigator.languages || [];
  for (const lang of languages) {
    const normalizedLang = lang.toLowerCase();
    if (SPANISH_LANGUAGE_CODES.includes(normalizedLang)) {
      return 'es';
    }
    // If any language starts with 'es', consider it Spanish
    if (normalizedLang.startsWith('es')) {
      return 'es';
    }
  }

  // If we found English-like languages, return English
  if (primaryLanguage?.startsWith('en') || languages.some(lang => lang.toLowerCase().startsWith('en'))) {
    return 'en';
  }

  return null;
}

/**
 * Detects user's country based on IP geolocation
 * This is a fallback method when browser language detection fails
 */
export async function detectCountryByIP(): Promise<'en' | 'es' | null> {
  try {
    // Use a free IP geolocation service
    const response = await fetch('https://ipapi.co/json/', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      console.warn('IP geolocation service unavailable');
      return null;
    }

    const data = await response.json();
    const countryCode = data.country_code;

    if (countryCode && SPANISH_SPEAKING_COUNTRIES.includes(countryCode)) {
      return 'es';
    }

    // Default to English for other countries
    return 'en';
  } catch (error) {
    console.warn('Failed to detect country by IP:', error);
    return null;
  }
}

/**
 * Comprehensive language detection with fallback chain
 * 1. Browser language settings
 * 2. IP-based geolocation
 * 3. Default to English
 */
export async function detectUserLanguage(): Promise<'en' | 'es'> {
  // First try browser language detection
  const browserLang = detectBrowserLanguage();
  if (browserLang) {
    console.log('Language detected from browser:', browserLang);
    return browserLang;
  }

  // Fallback to IP-based detection
  try {
    const ipLang = await detectCountryByIP();
    if (ipLang) {
      console.log('Language detected from IP geolocation:', ipLang);
      return ipLang;
    }
  } catch (error) {
    console.warn('IP-based language detection failed:', error);
  }

  // Final fallback to English
  console.log('Language detection failed, defaulting to English');
  return 'en';
}

/**
 * Gets the user's preferred language from localStorage or detects it
 */
export async function getUserPreferredLanguage(): Promise<'en' | 'es'> {
  // Check if user has manually set a language preference
  if (typeof window !== 'undefined') {
    const savedLanguage = localStorage.getItem('preferred-language');
    if (savedLanguage === 'en' || savedLanguage === 'es') {
      console.log('Using saved language preference:', savedLanguage);
      return savedLanguage;
    }
  }

  // Otherwise, detect automatically
  return await detectUserLanguage();
}

/**
 * Saves the user's language preference
 */
export function saveLanguagePreference(language: 'en' | 'es'): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('preferred-language', language);
    console.log('Saved language preference:', language);
  }
}
