import { locales, defaultLocale, type Locale } from '@/i18n/config';

export function getLocaleFromPathname(pathname: string): Locale | null {
  const segments = pathname.split('/');
  const potentialLocale = segments[1];
  
  if (locales.includes(potentialLocale as Locale)) {
    return potentialLocale as Locale;
  }
  
  return null;
}

export function removeLocaleFromPathname(pathname: string): string {
  const locale = getLocaleFromPathname(pathname);
  if (locale) {
    return pathname.replace(`/${locale}`, '') || '/';
  }
  return pathname;
}

export function addLocaleToPathname(pathname: string, locale: Locale): string {
  if (locale === defaultLocale) {
    return pathname;
  }
  
  const cleanPathname = removeLocaleFromPathname(pathname);
  return `/${locale}${cleanPathname}`;
}

export function detectLocaleFromHeaders(acceptLanguage: string): Locale {
  if (!acceptLanguage) return defaultLocale;
  
  const languages = acceptLanguage
    .split(',')
    .map(lang => lang.split(';')[0].trim().toLowerCase());
  
  // Check for Spanish variants
  const spanishVariants = ['es', 'es-es', 'es-mx', 'es-ar', 'es-co', 'es-cl', 'es-pe', 'es-ve'];
  
  for (const lang of languages) {
    if (spanishVariants.includes(lang)) {
      return 'es';
    }
  }
  
  return defaultLocale;
}
