import React from 'react';

// Define message types
export interface Message {
  id: string;
  content: React.ReactNode;
  isUser: boolean;
  timestamp: Date;
  provider?: string; // 'Together AI', 'Replicate', etc.
  model?: string;
  metadata?: any; // Additional data like generated images
}

/**
 * Creates a new message object
 * @param content The content of the message
 * @param isUser Whether the message is from the user
 * @param provider Optional provider information
 * @param model Optional model information
 * @param metadata Optional additional data
 * @returns A new Message object
 */
export const createMessage = (
  content: React.ReactNode, 
  isUser: boolean, 
  provider?: string,
  model?: string,
  metadata?: any
): Message => ({
  id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  content,
  isUser,
  timestamp: new Date(),
  provider,
  model,
  metadata
}); 