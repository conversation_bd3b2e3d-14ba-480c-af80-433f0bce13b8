import React from 'react';

// Define serializable message data types
export interface MessageData {
  type: 'text' | 'image-grid' | 'image-upload';
  text?: string;
  images?: Array<{
    id: number;
    url: string;
    prompt?: string;
    negativePrompt?: string;
    model?: string;
    width?: number;
    height?: number;
    timestamp?: Date;
    provider?: string;
  }>;
  uploadedImage?: string;
  uploadedImages?: string[];
  provider?: string;
  modelProvider?: string;
  selectedModel?: string;
  numImages?: number;
}

// Define message types
export interface Message {
  id: string;
  content: React.ReactNode;
  isUser: boolean;
  timestamp: Date;
  provider?: string; // 'Together AI', 'Replicate', etc.
  model?: string;
  metadata?: any; // Additional data like generated images
  // Add serializable data for localStorage
  data?: MessageData;
}

// Define serializable message for localStorage
export interface SerializableMessage {
  id: string;
  data: MessageData;
  isUser: boolean;
  timestamp: string; // ISO string for serialization
  provider?: string;
  model?: string;
  metadata?: any;
}

/**
 * Creates a new message object
 * @param content The content of the message
 * @param isUser Whether the message is from the user
 * @param provider Optional provider information
 * @param model Optional model information
 * @param metadata Optional additional data
 * @returns A new Message object
 */
export const createMessage = (
  content: React.ReactNode,
  isUser: boolean,
  provider?: string,
  model?: string,
  metadata?: any,
  data?: MessageData
): Message => ({
  id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
  content,
  isUser,
  timestamp: new Date(),
  provider,
  model,
  metadata,
  data
});

// Utility functions for localStorage serialization
export const serializeMessage = (message: Message): SerializableMessage => ({
  id: message.id,
  data: message.data || { type: 'text', text: 'Message content not serializable' },
  isUser: message.isUser,
  timestamp: message.timestamp.toISOString(),
  provider: message.provider,
  model: message.model,
  metadata: message.metadata
});

export const deserializeMessage = (serialized: SerializableMessage): Partial<Message> => ({
  id: serialized.id,
  data: serialized.data,
  isUser: serialized.isUser,
  timestamp: new Date(serialized.timestamp),
  provider: serialized.provider,
  model: serialized.model,
  metadata: serialized.metadata
});

export const serializeMessages = (messages: Message[]): string => {
  try {
    const serializedMessages = messages.map(serializeMessage);
    return JSON.stringify(serializedMessages);
  } catch (error) {
    console.error('Error serializing messages:', error);
    return JSON.stringify([]);
  }
};

export const deserializeMessages = (serializedData: string): SerializableMessage[] => {
  try {
    return JSON.parse(serializedData) ?? [];
  } catch (error) {
    console.error('Error deserializing messages:', error);
    return [];
  }
};

// Helper functions to create specific message types
export const createTextMessage = (
  text: string,
  isUser: boolean,
  provider?: string,
  model?: string,
  uploadedImage?: string,
  uploadedImages?: string[]
): Message => {
  const data: MessageData = {
    type: 'text',
    text,
    uploadedImage,
    uploadedImages
  };

  const content = (
    <div>
      {uploadedImages && uploadedImages.length > 0 ? (
        <div className="flex flex-wrap gap-2 mb-2">
          {uploadedImages.map((img, index) => (
            <img key={index} src={img} alt={`Uploaded ${index + 1}`} className="max-h-32 rounded" />
          ))}
        </div>
      ) : uploadedImage && (
        <img src={uploadedImage} alt="Uploaded" className="max-h-32 mb-2 rounded" />
      )}
      <span>{text}</span>
      </div>
  );

  return createMessage(content, isUser, provider, model, undefined, data);
};

export const createImageGridMessage = (
  images: any[],
  provider: string,
  selectedModel: string,
  numImages: number,
  isUser: boolean = false
): Message => {
  const data: MessageData = {
    type: 'image-grid',
    images: images.map(img => ({
      id: img.id,
      url: img.url,
      prompt: img.prompt,
      negativePrompt: img.negativePrompt,
      model: img.model,
      width: img.width,
      height: img.height,
      timestamp: img.timestamp,
      provider: img.provider
    })),
    provider,
    selectedModel,
    numImages,
    modelProvider: provider
  };

  // The actual React component will be reconstructed when rendering
  // For now, store a placeholder that will be replaced during rendering
  const content = <div>Image grid placeholder - will be reconstructed</div>;

  return createMessage(content, isUser, provider, selectedModel, undefined, data);
};

export const createImageUploadMessage = (
  description: string,
  imageUrl: string,
  isUser: boolean = true
): Message => {
  const data: MessageData = {
    type: 'image-upload',
    text: description,
    uploadedImage: imageUrl
  };

  const content = (
    <div className="flex flex-col gap-2">
      <p>{description}</p>
      <div className="relative rounded-lg overflow-hidden bg-checkerboard">
        <img
          src={imageUrl}
          alt="Pasted image"
          className="max-h-[300px] w-full object-contain"
        />
      </div>
    </div>
  );

  return createMessage(content, isUser, undefined, undefined, undefined, data);
};

// Helper function to check if a message needs to be reconstructed
export const shouldReconstructMessage = (message: Message): boolean => {
  return message.data !== undefined && (
    message.data.type === 'image-grid' ||
    message.data.type === 'text' ||
    message.data.type === 'image-upload'
  );
};