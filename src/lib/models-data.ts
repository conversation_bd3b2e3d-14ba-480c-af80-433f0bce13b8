import { ModelCategories, AIModel } from "./image-utils";

// Models that support image input
export const IMAGE_INPUT_SUPPORTED_MODELS = [
  'black-forest-labs/flux-kontext-pro',
  'black-forest-labs/flux-kontext-max',
  'openai/gpt-image-1',
  'black-forest-labs/flux-pro',
  'black-forest-labs/flux-1.1-pro',
  'black-forest-labs/flux-1.1-pro-ultra',
  'black-forest-labs/flux-dev',
  'black-forest-labs/flux-dev-lora',
  'recraft-ai/recraft-v3',
  'ideogram-ai/ideogram-v2',
  'ideogram-ai/ideogram-v2-turbo',
  'ideogram-ai/ideogram-v3-quality',
  'ideogram-ai/ideogram-v3-turbo',
  'ideogram-ai/ideogram-v3-balanced'
];

// Models that require OpenAI API key
export const OPENAI_API_MODELS = ['openai/gpt-image-1'];

// Models that support multiple image inputs
export const MULTIPLE_IMAGE_SUPPORTED_MODELS = ['openai/gpt-image-1'];

// Helper function to check if a model supports image input
export const supportsImageInput = (modelId: string): boolean => {
  return IMAGE_INPUT_SUPPORTED_MODELS.includes(modelId);
};

// Helper function to check if a model requires OpenAI API key
export const requiresOpenAIKey = (modelId: string): boolean => {
  return OPENAI_API_MODELS.includes(modelId);
};

// Helper function to check if a model supports multiple images
export const supportsMultipleImages = (modelId: string): boolean => {
  return MULTIPLE_IMAGE_SUPPORTED_MODELS.includes(modelId);
};

// Model categories and models - organized by model type, not provider
export const modelsByCategory: ModelCategories = {
  "Quick Generation": [
    {
      id: "black-forest-labs/FLUX.1-schnell-Free",
      name: "Flux 1 schnell Basic",
      description:
        "Perfect for beginners or those on a budget. Good for casual image creation without cost.",
      provider: "Together AI",
    },
    {
      id: "bytedance/seedream-3",
      name: "Seedream 3",
      description:
        "Great for quick image generation with good quality. Ideal for rapid prototyping and testing ideas.",
      provider: "Replicate",
    },
    {
      id: "black-forest-labs/FLUX.1-schnell",
      name: "Flux 1 schnell",
      description:
        "Great for quick image generation with good quality. Ideal for rapid prototyping and testing ideas.",
      provider: "Together AI",
    },
    {
      id: "black-forest-labs/flux-schnell",
      name: "Flux Schnell",
      description:
        "Fast generation for quick iterations. Great when you need multiple options in a short time.",
      provider: "Replicate",
    },
    {
      id: "black-forest-labs/flux-kontext-max",
      name: "Flux Kontext Max",
      description:
        "Fast generation for quick iterations. Great when you need multiple options in a short time.",
      provider: "Replicate",
    },
    {
      id: "black-forest-labs/flux-kontext-pro",
      name: "Flux Kontext Pro",
      description:
        "Fast generation for quick iterations. Great when you need multiple options in a short time.",
      provider: "Replicate",
    },
    {
      id: "openai/dall-e-3",
      name: "Dall-E 3",
      description:
        "Fast generation for quick iterations. Great when you need multiple options in a short time.",
      provider: "Replicate",
    },
    {
      id: "openai/gpt-image-1",
      name: "GPT image 1",
      description:
        "Fast generation for quick iterations. Great when you need multiple options in a short time.",
      provider: "Replicate",
    },
  ],
  "Professional Quality": [
    {
      id: "black-forest-labs/FLUX.1-pro",
      name: "Flux 1",
      description:
        "Excellent for professional-grade images. Ideal for commercial projects requiring high quality output.",
      provider: "Together AI",
    },
    {
      id: "black-forest-labs/FLUX.1.1-pro",
      name: "Flux 1.1",
      description:
        "Enhanced professional model with improved details. Perfect for high-end commercial work and detailed illustrations.",
      provider: "Together AI",
    },
    {
      id: "black-forest-labs/flux-pro",
      name: "Flux Pro",
      description:
        "High-quality output for professional use. Excellent for client presentations and finished artwork.",
      provider: "Replicate",
    },
    {
      id: "black-forest-labs/flux-1.1-pro",
      name: "Flux 1.1 Pro",
      description:
        "Enhanced detail and quality for professional work. Ideal for print-ready illustrations and detailed digital art.",
      provider: "Replicate",
    },
  ],
  "Ultra Detail": [
    {
      id: "black-forest-labs/flux-1.1-pro-ultra",
      name: "Flux 1.1 Pro Ultra",
      description:
        "Maximum quality with exceptional detail. Perfect for large format prints and showcase pieces requiring the highest fidelity.",
      provider: "Replicate",
    },
    {
      id: "together-ai/stabilityai/stable-diffusion-xl-base-1.0",
      name: "Stable Diffusion XL 1.0",
      description:
        "Versatile base model with good general performance. Great for a wide range of image styles and subjects.",
      provider: "Together AI",
    },
  ],
  "Development Models": [
    {
      id: "black-forest-labs/FLUX.1-dev",
      name: "Flux 1 dev",
      description:
        "Suitable for developers testing new features. Good balance between speed and quality for development work.",
      provider: "Together AI",
    },
    {
      id: "black-forest-labs/flux-dev",
      name: "Flux Dev",
      description:
        "Balanced model for development purposes. Good for testing prompts and concepts before final rendering.",
      provider: "Replicate",
    },
    {
      id: "together-ai/flux-redux-dev",
      name: "Flux 1 Redux dev",
      description:
        "Refined development model with improved consistency. Good for projects requiring reliable output quality.",
      provider: "Together AI",
    },
  ],
  "Specialized Tools": [
    {
      id: "together-ai/flux-canny-dev",
      name: "Flux 1 Canny dev",
      description:
        "Excellent for creating images based on edge detection. Perfect for line art conversion and sketch-based generation.",
      provider: "Together AI",
    },
    {
      id: "together-ai/flux-depth-dev",
      name: "Flux 1 Depth dev",
      description:
        "Specialized for depth-aware image generation. Great for 3D-like effects and realistic spatial relationships.",
      provider: "Together AI",
    },
    {
      id: "black-forest-labs/flux-canny-dev",
      name: "Flux Canny Dev",
      description:
        "Edge detection model for development. Useful for converting sketches to detailed images or working with line art.",
      provider: "Replicate",
    },
  ],
  "LoRA Models": [
    {
      id: "together-ai/flux-dev-lora",
      name: "Flux 1 Dev LoRA",
      description:
        "Supports fine-tuning with LoRA adapters. Perfect for customized styles and specialized domain adaptation.",
      provider: "Together AI",
    },
    {
      id: "black-forest-labs/flux-dev-lora",
      name: "Flux Dev LoRA",
      description:
        "Development model with LoRA support. Great for testing custom styles and concept adaptation.",
      provider: "Replicate",
    },
    {
      id: "black-forest-labs/flux-schnell-lora",
      name: "Flux Schnell LoRA",
      description:
        "Fast generation with LoRA support. Ideal for quick testing of custom styles and concepts.",
      provider: "Replicate",
    },
  ],
  "Alternative Models": [
    {
      id: "google/imagen-3",
      name: "Google Imagen 3",
      description:
        "Google's powerful image generation model. Excellent for photorealistic images and complex scenes.",
      provider: "Replicate",
    },
    {
      id: "google/imagen-4",
      name: "Google Imagen 4",
      description:
        "Google's powerful image generation model. Excellent for photorealistic images and complex scenes.",
      provider: "Replicate",
    },
    {
      id: "google/imagen-4-ultra",
      name: "Google Imagen 4 Ultra",
      description:
        "Faster version of Google's model. Great for quick photorealistic results with slightly lower detail.",
      provider: "Replicate",
    },
    {
      id: "google/imagen-4-fast",
      name: "Google Imagen 4 Fast",
      description:
        "Faster version of Google's model. Great for quick photorealistic results with slightly lower detail.",
      provider: "Replicate",
    },
    {
      id: "google/imagen-3-fast",
      name: "Google Imagen 3 Fast",
      description:
        "Faster version of Google's model. Great for quick photorealistic results with slightly lower detail.",
      provider: "Replicate",
    },
  ],
  "Specialized Providers": [
    {
      id: "stability-ai/stable-diffusion-3",
      name: "Stable Diffusion 3",
      description:
        "Latest generation with improved capabilities. Excellent all-around model for various image types.",
      provider: "Replicate",
    },
    {
      id: "stability-ai/stable-diffusion-3.5-large",
      name: "Stable Diffusion 3.5 Large",
      description:
        "Large model with exceptional detail. Perfect for complex scenes and highly detailed artwork.",
      provider: "Replicate",
    },
    {
      id: "stability-ai/stable-diffusion-3.5-large-turbo",
      name: "Stable Diffusion 3.5 Large Turbo",
      description:
        "Faster version of the large model. Great for detailed images with reduced generation time.",
      provider: "Replicate",
    },
    {
      id: "stability-ai/stable-diffusion-3.5-medium",
      name: "Stable Diffusion 3.5 Medium",
      description:
        "Balanced size and performance. Good general-purpose model for most image generation needs.",
      provider: "Replicate",
    },
  ],
  "Creative ideas": [
    {
      id: "ideogram-ai/ideogram-v2",
      name: "Ideogram v2",
      description:
        "Excellent for creative and artistic imagery. Great for illustrations and stylized content.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v2a",
      name: "Ideogram v2a",
      description:
        "Refined artistic generation with improved details. Perfect for high-quality illustrations and creative work.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v2a-turbo",
      name: "Ideogram v2a Turbo",
      description:
        "Fast artistic generation with good quality. Ideal for quick creative iterations and concept art.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v2-turbo",
      name: "Ideogram v2 Turbo",
      description:
        "Accelerated creative image generation. Great for rapid artistic exploration and concept development.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v3-quality",
      name: "Ideogram v3 Quality",
      description:
        "Accelerated creative image generation. Great for rapid artistic exploration and concept development.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v3-turbo",
      name: "Ideogram v3 Turbo",
      description:
        "Accelerated creative image generation. Great for rapid artistic exploration and concept development.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v3-balanced",
      name: "Ideogram v3 Balanced",
      description:
        "Accelerated creative image generation. Great for rapid artistic exploration and concept development.",
      provider: "Replicate",
    },
    {
      id: "luma/photon",
      name: "Luma Photon",
      description:
        "Specialized for high-quality photorealistic images. Excellent for product visualization and realistic scenes.",
      provider: "Replicate",
    },
    {
      id: "luma/photon-flash",
      name: "Luma Photon Flash",
      description:
        "Quick photorealistic generation. Perfect for rapid prototyping of realistic product images and scenes.",
      provider: "Replicate",
    },
  ],
  "Artistic Providers": [
    {
      id: "recraft-ai/recraft-20b",
      name: "Recraft 20B",
      description:
        "Large model for detailed illustrations. Great for complex designs and intricate artwork.",
      provider: "Replicate",
    },
    {
      id: "recraft-ai/recraft-20b-svg",
      name: "Recraft 20B SVG",
      description:
        "Generates vector graphics in SVG format. Perfect for scalable illustrations and design assets.",
      provider: "Replicate",
    },
    {
      id: "recraft-ai/recraft-v3",
      name: "Recraft v3",
      description:
        "Versatile illustration generation. Great for digital art, icons, and graphic design elements.",
      provider: "Replicate",
    },
    {
      id: "recraft-ai/recraft-v3-svg",
      name: "Recraft v3 SVG",
      description:
        "Vector-based illustration generation. Ideal for logos, icons, and other scalable design assets.",
      provider: "Replicate",
    },
  ],
  "Image Editing": [
    {
      id: "openai/gpt-image-1",
      name: "GPT image 1",
      description:
        "Fast generation for quick iterations. Great when you need multiple options in a short time.",
      provider: "Replicate",
    },
    {
      id: "black-forest-labs/flux-canny-pro",
      name: "Flux Canny Pro",
      description:
        "Professional edge detection for high-quality results. Ideal for artists working from sketches or line drawings.",
      provider: "Replicate",
    },
    {
        id: "cjwbw/rembg:fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003",
        name: "Remove Background",
        description:
          "Clean background removal for any image. Creates transparent backgrounds with high precision.",
        provider: "Replicate",
      },
    {
      id: "tencentarc/gfpgan",
      name: "GFPGAN Face Restoration",
      description: "Specialized in restoring and enhancing faces in photos.",
      provider: "Replicate",
    },
    {
      id: "lucataco/sod",
      name: "Subject Object Detection",
      description:
        "Identifies and isolates subjects in images for easy editing.",
      provider: "Replicate",
    },
    {
      id: "nightmareai/real-esrgan",
      name: "Real-ESRGAN",
      description: "General image restoration and enhancement with AI.",
      provider: "Replicate",
    },
    {
      id: "black-forest-labs/flux-kontext-max",
      name: "Flux Kontext Max",
      description:
        "Fast generation for quick iterations. Great when you need multiple options in a short time.",
      provider: "Replicate",
    },
    {
      id: "black-forest-labs/flux-kontext-pro",
      name: "Flux Kontext Pro",
      description:
        "Fast generation for quick iterations. Great when you need multiple options in a short time.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v2",
      name: "Ideogram v2",
      description:
        "Excellent for creative and artistic imagery. Great for illustrations and stylized content.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v2-turbo",
      name: "Ideogram v2 Turbo",
      description:
        "Accelerated creative image generation. Great for rapid artistic exploration and concept development.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v3-quality",
      name: "Ideogram v3 Quality",
      description:
        "Accelerated creative image generation. Great for rapid artistic exploration and concept development.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v3-turbo",
      name: "Ideogram v3 Turbo",
      description:
        "Accelerated creative image generation. Great for rapid artistic exploration and concept development.",
      provider: "Replicate",
    },
    {
      id: "ideogram-ai/ideogram-v3-balanced",
      name: "Ideogram v3 Balanced",
      description:
        "Accelerated creative image generation. Great for rapid artistic exploration and concept development.",
      provider: "Replicate",
    },
  ],
  "Upscaling Models": [
    {
      id: "google/upscaler",
      name: "Google Upscaler",
      description:
        "Google's AI-powered image upscaler with excellent detail retention.",
      provider: "Replicate",
    },
    {
      id: "recraft-ai/recraft-creative-upscale",
      name: "Recraft Creative Upscale",
      description:
        "Enhances images with creative details. Ideal for adding artistic flair while increasing resolution.",
      provider: "Replicate",
    },
    {
      id: "recraft-ai/recraft-crisp-upscale",
      name: "Recraft Crisp Upscale",
      description:
        "Focuses on sharp, clean upscaling. Perfect for text-heavy images and technical illustrations.",
      provider: "Replicate",
    },
    {
      id: "nightmareai/real-esrgan",
      name: "Real-ESRGAN",
      description: "High-quality upscaling with additional detail enhancement.",
      provider: "Replicate",
    },
    {
      id: "recraft-ai/recraft-creative-upscale",
      name: "Recraft Creative Upscale",
      description: "Upscales while adding creative artistic details to images.",
      provider: "Replicate",
    },
    {
      id: "recraft-ai/recraft-crisp-upscale",
      name: "Recraft Crisp Upscale",
      description: "Focus on text clarity and sharpness when upscaling.",
      provider: "Replicate",
    },
    {
      id: "sczhou/codeformer",
      name: "CodeFormer",
      description:
        "Specialized in photo restoration and face enhancement while upscaling.",
      provider: "Replicate",
    },
  ],
};

// Get all models as a flat array
export const getAllModels = (): AIModel[] => {
  return Object.values(modelsByCategory).flat();
};

// Get a specific model by ID
export const getModelById = (id: string): AIModel | undefined => {
  return getAllModels().find(model => model.id === id);
};