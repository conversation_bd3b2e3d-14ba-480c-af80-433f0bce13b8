'use server';

import neo4j, { Driver, Session, ManagedTransaction } from 'neo4j-driver';
import { checkImageGenerationAccessQuery } from './neo4j/subscription-queries';

// Neo4j connection details - check for both naming conventions for backward compatibility
const URI = process.env.NEO4J_URI ?? '';
const USER = process.env.NEO4J_USERNAME ?? process.env.NEO4J_USER ?? '';
const PASSWORD = process.env.NEO4J_PASSWORD ?? '';

// Create a driver instance
let driver: Driver | null = null;

// Initialize driver with detailed logging
if (!global.neo4jDriver) {
  if (!URI || !USER || !PASSWORD) {
    console.error('Missing Neo4j connection details:');
    console.error('- NEO4J_URI:', !!URI);
    console.error('- NEO4J_USERNAME/NEO4J_USER:', !!USER);
    console.error('- NEO4J_PASSWORD:', !!PASSWORD);
  } else {
    try {
      console.log('Initializing Neo4j driver with URI:', URI);
      driver = neo4j.driver(
        URI,
        neo4j.auth.basic(USER, PASSWORD),
        {
          maxConnectionLifetime: 3 * 60 * 60 * 1000, // 3 hours
          maxConnectionPoolSize: 50,
          connectionAcquisitionTimeout: 2 * 60 * 1000, // 2 minutes
        }
      );
      
      // Test the connection
      driver.verifyConnectivity().then(() => {
        console.log('Neo4j connection verified successfully');
        global.neo4jDriver = driver as Driver;
      }).catch(error => {
        console.error('Neo4j connection test failed:', error);
      });
      
      console.log('Neo4j driver initialized');
    } catch (error) {
      console.error('Failed to initialize Neo4j driver:', error);
    }
  }
} else {
  driver = global.neo4jDriver;
  console.log('Using existing Neo4j driver');
}

// Function to get a new session - converted to async
export async function getSession(): Promise<Session> {
  if (!driver) {
    console.error('Trying to get Neo4j session but driver is not initialized');
    throw new Error('Neo4j driver not initialized');
  }
  return driver.session();
}

// Execute a Cypher query and return results - already async
export async function executeQuery(cypher: string, params = {}) {
  console.log('Executing Neo4j query:', cypher.substring(0, 50) + '...');
  console.log('With params:', JSON.stringify(params));
  
  const session = await getSession();
  try {
    const result = await session.executeRead((tx: ManagedTransaction) => 
      tx.run(cypher, params)
    );
    console.log(`Query returned ${result.records.length} records`);
    return result.records;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  } finally {
    await session.close();
  }
}

// Execute a Cypher write query - already async
export async function executeWrite(cypher: string, params = {}) {
  console.log('Executing Neo4j write query:', cypher.substring(0, 50) + '...');
  console.log('With params:', JSON.stringify(params));
  
  const session = await getSession();
  try {
    const result = await session.executeWrite((tx: ManagedTransaction) => 
      tx.run(cypher, params)
    );
    console.log(`Write query returned ${result.records.length} records`);
    return result.records;
  } catch (error) {
    console.error('Database write error:', error);
    throw error;
  } finally {
    await session.close();
  }
}

// Close the driver when the app is shut down - already async
export async function closeDriver() {
  if (driver) {
    await driver.close();
  }
}

// Initialize the database schema (run once) - already async
export async function initializeSchema() {
  const session = await getSession();
  try {
    // Create constraints and indexes
    await session.executeWrite((tx: ManagedTransaction) => 
      tx.run(`CREATE CONSTRAINT user_email IF NOT EXISTS FOR (u:User) REQUIRE u.email IS UNIQUE`)
    );
    console.log('Database schema initialized');
  } catch (error) {
    console.error('Failed to initialize schema:', error);
  } finally {
    await session.close();
  }
}

// Add TypeScript declarations for global variable
declare global {
  var neo4jDriver: Driver | undefined;
}

// Export a consistent init function - convert to async
export const initNeo4j = async (): Promise<Driver> => {
  if (driver !== null) {
    return driver;
  }

  // Use the same variables as above for consistency
  if (!URI || !USER || !PASSWORD) {
    console.error('Missing Neo4j connection details in initNeo4j');
    throw new Error('Missing Neo4j connection details');
  }

  console.log('Initializing new Neo4j driver in initNeo4j');
  driver = neo4j.driver(URI, neo4j.auth.basic(USER, PASSWORD));
  global.neo4jDriver = driver as Driver;
  return driver;
};

export const closeNeo4j = async (): Promise<void> => {
  if (driver !== null) {
    await driver.close();
    driver = null;
  }
};

// User subscription functions
export const getUserSubscription = async (userId: string) => {
  console.log('Getting subscription for user:', userId);
  const session = await getSession();

  try {
    const result = await session.run(
      `
      MATCH (u:User {id: $userId})-[:HAS_SUBSCRIPTION]->(s:Subscription)
      WHERE s.status = 'active' OR s.status = 'trialing'
      RETURN s
      ORDER BY s.createdAt DESC
      LIMIT 1
      `,
      { userId }
    );

    if (result.records.length === 0) {
      console.log('No active subscription found for user:', userId);
      return null;
    }

    const subscription = result.records[0].get('s').properties;
    console.log('Found active subscription for user:', userId, subscription.type);
    return subscription;
  } catch (error) {
    console.error('Error getting user subscription:', error);
    throw error;
  } finally {
    await session.close();
  }
};

export const createOrUpdateSubscription = async (
  userId: string, 
  subscriptionData: {
    type: string;
    status: string;
    startDate: string;
    endDate?: string;
    stripeCustomerId?: string;
    stripeSubscriptionId?: string;
  }
) => {
  const driver = await initNeo4j();
  const session = driver.session();

  try {
    const result = await session.run(
      `
      MATCH (u:User {id: $userId})
      MERGE (u)-[:HAS_SUBSCRIPTION]->(s:Subscription)
      SET s = $subscriptionData
      RETURN s
      `,
      { 
        userId,
        subscriptionData: {
          ...subscriptionData,
          updatedAt: new Date().toISOString()
        }
      }
    );

    return result.records[0].get('s').properties;
  } finally {
    await session.close();
  }
};

export const cancelSubscription = async (userId: string) => {
  const driver = await initNeo4j();
  const session = driver.session();

  try {
    await session.run(
      `
      MATCH (u:User {id: $userId})-[:HAS_SUBSCRIPTION]->(s:Subscription)
      SET s.status = 'cancelled', s.updatedAt = $updatedAt
      `,
      { 
        userId,
        updatedAt: new Date().toISOString()
      }
    );

    return true;
  } finally {
    await session.close();
  }
};

/**
 * Check if a user has access to image generation models based on subscription
 * @param userId - The user ID to check
 * @returns Object containing subscription type and whether the user has full access
 */
export const checkImageGenerationAccess = async (userId: string) => {
  console.log('Checking image generation access for user:', userId);
  const session = await getSession();

  try {
    const result = await session.run(checkImageGenerationAccessQuery, { userId });

    if (result.records.length === 0) {
      console.log('No active subscription found, user has limited access');
      return { subscriptionType: 'free', hasFullAccess: false };
    }

    const record = result.records[0];
    const subscriptionType = record.get('subscriptionType');
    const hasFullAccess = record.get('hasFullAccess');

    console.log(`User ${userId} has subscription type: ${subscriptionType}, full access: ${hasFullAccess}`);
    
    return { 
      subscriptionType, 
      hasFullAccess 
    };
  } catch (error) {
    console.error('Error checking image generation access:', error);
    // Default to limited access on error
    return { subscriptionType: 'free', hasFullAccess: false };
  } finally {
    await session.close();
  }
};

// User lookup and validation functions

/**
 * Check if a user exists by email or Stripe customer ID
 * This helps prevent duplicate user creation during sync
 */
export const findUserByEmailOrStripeId = async (email: string, stripeCustomerId?: string) => {
  if (!email && !stripeCustomerId) {
    console.error('Must provide either email or stripeCustomerId to find user');
    return null;
  }
  
  const session = await getSession();
  try {
    let query = '';
    let params: any = {};
    
    if (email && stripeCustomerId) {
      // Search by both email and Stripe ID
      query = `
        MATCH (u:User)
        WHERE u.email = $email OR u.stripeCustomerId = $stripeCustomerId
        RETURN u
        LIMIT 1
      `;
      params = { email, stripeCustomerId };
    } else if (email) {
      // Search by email only
      query = `
        MATCH (u:User)
        WHERE u.email = $email
        RETURN u
        LIMIT 1
      `;
      params = { email };
    } else {
      // Search by Stripe customer ID only
      query = `
        MATCH (u:User)
        WHERE u.stripeCustomerId = $stripeCustomerId
        RETURN u
        LIMIT 1
      `;
      params = { stripeCustomerId };
    }
    
    const result = await session.run(query, params);
    
    if (result.records.length === 0) {
      return null;
    }
    
    return result.records[0].get('u').properties;
  } catch (error) {
    console.error('Error finding user by email or Stripe ID:', error);
    return null;
  } finally {
    await session.close();
  }
};

/**
 * Get users by Stripe customer ID
 * This helps identify duplicate users with the same Stripe account
 */
export const getUsersByStripeCustomerId = async (stripeCustomerId: string) => {
  if (!stripeCustomerId) {
    console.error('Missing Stripe customer ID');
    return [];
  }
  
  const session = await getSession();
  try {
    const result = await session.run(
      `
      MATCH (u:User {stripeCustomerId: $stripeCustomerId})
      RETURN u
      `,
      { stripeCustomerId }
    );
    
    return result.records.map(record => record.get('u').properties);
  } catch (error) {
    console.error('Error getting users by Stripe customer ID:', error);
    return [];
  } finally {
    await session.close();
  }
}; 