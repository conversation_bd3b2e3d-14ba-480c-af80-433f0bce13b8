import neo4j, { Driver } from 'neo4j-driver';

let driver: Driver | null = null;

/**
 * Initialize the Neo4j driver
 */
export function initDriver() {
  // Check if driver already exists
  if (driver) {
    return driver;
  }

  // Get connection details from environment variables
  const uri = process.env.NEO4J_URI || '';
  const username = process.env.NEO4J_USERNAME || 'neo4j';
  const password = process.env.NEO4J_PASSWORD || 'password';

  // Create a new driver instance
  driver = neo4j.driver(
    uri,
    neo4j.auth.basic(username, password),
    {
      maxConnectionPoolSize: 50,
      connectionAcquisitionTimeout: 30000,
      // Logging can be turned on for debugging
      logging: neo4j.logging.console('error')
    }
  );

  // Register a shutdown hook for the driver
  process.on('exit', () => {
    if (driver) {
      driver.close();
    }
  });

  return driver;
}

/**
 * Get the Neo4j driver instance
 */
export function getDriver(): Driver {
  if (!driver) {
    return initDriver();
  }
  return driver;
}

/**
 * Close the Neo4j driver
 */
export async function closeDriver() {
  if (driver) {
    await driver.close();
    driver = null;
  }
} 