/**
 * <PERSON>4j Cypher queries for subscription management
 */

/**
 * Create a new subscription for a user
 * @param userId - The ID of the user
 * @param subscriptionType - The type of subscription (e.g., "Standard", "Premium")
 * @param status - The status of the subscription (e.g., "active", "canceled", "pending")
 * @param startDate - The start date of the subscription
 * @param endDate - The end date of the subscription
 * @param stripeSubscriptionId - The Stripe subscription ID
 * @param stripePriceId - The Stripe price ID
 * @param stripeCustomerId - The Stripe customer ID
 */
export const createSubscriptionQuery = `
  MATCH (u:User {id: $userId})
  
  // Create the subscription node
  CREATE (s:Subscription {
    id: randomUUID(),
    type: $subscriptionType,
    status: $status,
    startDate: $startDate,
    endDate: $endDate,
    createdAt: datetime(),
    updatedAt: datetime(),
    stripeSubscriptionId: $stripeSubscriptionId,
    stripePriceId: $stripePriceId,
    stripeCustomerId: $stripeCustomerId
  })
  
  // Create relationship between user and subscription
  CREATE (u)-[:HAS_SUBSCRIPTION]->(s)
  
  RETURN s
`;

/**
 * Get a user's current active subscription
 * @param userId - The ID of the user
 */
export const getUserSubscriptionQuery = `
  MATCH (u:User {id: $userId})-[:HAS_SUBSCRIPTION]->(s:Subscription)
  WHERE s.status = 'active' OR s.status = 'trialing'
  RETURN s
  ORDER BY s.createdAt DESC
  LIMIT 1
`;

/**
 * Update a subscription's status
 * @param subscriptionId - The ID of the subscription
 * @param status - The new status of the subscription
 */
export const updateSubscriptionStatusQuery = `
  MATCH (s:Subscription {id: $subscriptionId})
  SET s.status = $status,
      s.updatedAt = datetime()
  RETURN s
`;

/**
 * Update a subscription's end date
 * @param subscriptionId - The ID of the subscription
 * @param endDate - The new end date of the subscription
 */
export const updateSubscriptionEndDateQuery = `
  MATCH (s:Subscription {id: $subscriptionId})
  SET s.endDate = $endDate,
      s.updatedAt = datetime()
  RETURN s
`;

/**
 * Cancel a subscription
 * @param subscriptionId - The ID of the subscription
 */
export const cancelSubscriptionQuery = `
  MATCH (s:Subscription {id: $subscriptionId})
  SET s.status = 'canceled',
      s.updatedAt = datetime()
  RETURN s
`;

/**
 * Get all subscriptions for a user
 * @param userId - The ID of the user
 */
export const getAllUserSubscriptionsQuery = `
  MATCH (u:User {id: $userId})-[:HAS_SUBSCRIPTION]->(s:Subscription)
  RETURN s
  ORDER BY s.createdAt DESC
`;

/**
 * Get subscription counts by plan type
 */
export const getSubscriptionCountsByTypeQuery = `
  MATCH (s:Subscription)
  WHERE s.status = 'active'
  RETURN s.type AS type, count(s) AS count
`;

/**
 * Delete a subscription (admin only)
 * @param subscriptionId - The ID of the subscription
 */
export const deleteSubscriptionQuery = `
  MATCH (s:Subscription {id: $subscriptionId})
  DETACH DELETE s
`;

/**
 * Check if user has access to image generation models
 * @param userId - The ID of the user
 */
export const checkImageGenerationAccessQuery = `
  MATCH (u:User {id: $userId})-[:HAS_SUBSCRIPTION]->(s:Subscription)
  WHERE s.status = 'active' OR s.status = 'trialing'
  WITH s.type AS subscriptionType
  RETURN 
      subscriptionType,
      CASE 
          WHEN toLower(subscriptionType) IN ['standard', 'premium'] THEN true
          ELSE false
      END AS hasFullAccess
  LIMIT 1
`; 