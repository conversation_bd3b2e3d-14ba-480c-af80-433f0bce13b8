import { SUBSCRIPTION_TYPES } from './stripe-client';

// Feature access types
export enum FeatureAccess {
  // Content Generation Features
  IMAGE_GENERATION = 'image_generation',
  VIDEO_GENERATION = 'video_generation',
  PODCAST_GENERATION = 'podcast_generation',
  TEXT_GENERATION = 'text_generation',
  MUSIC_GENERATION = 'music_generation',
  SPEECH_TO_TEXT = 'speech_to_text',
  AI_ASSISTANT = 'ai_assistant',
  
  // Model Access
  BASIC_MODELS = 'basic_models',
  ADVANCED_MODELS = 'advanced_models',
  PREMIUM_MODELS = 'premium_models',
  
  // Quality/Quantity Settings
  HIGH_RESOLUTION = 'high_resolution',
  PRIORITY_PROCESSING = 'priority_processing',
  BATCH_PROCESSING = 'batch_processing',
  
  // Utilities
  DOWNLOADS = 'downloads',
  API_ACCESS = 'api_access',
}

// Define what features are available for each subscription level
const featureAccessByPlan = {
  // No subscription / free tier
  'free': [
    FeatureAccess.TEXT_GENERATION,
    FeatureAccess.BASIC_MODELS,
    FeatureAccess.DOWNLOADS,
  ],
  
  // Standard subscription
  [SUBSCRIPTION_TYPES.STANDARD]: [
    FeatureAccess.IMAGE_GENERATION,
    FeatureAccess.TEXT_GENERATION,
    FeatureAccess.MUSIC_GENERATION,
    FeatureAccess.SPEECH_TO_TEXT,
    FeatureAccess.AI_ASSISTANT,
    FeatureAccess.BASIC_MODELS,
    FeatureAccess.ADVANCED_MODELS,
    FeatureAccess.DOWNLOADS,
    FeatureAccess.BATCH_PROCESSING,
  ],
  
  // Premium subscription
  [SUBSCRIPTION_TYPES.PREMIUM]: [
    FeatureAccess.IMAGE_GENERATION,
    FeatureAccess.VIDEO_GENERATION,
    FeatureAccess.PODCAST_GENERATION,
    FeatureAccess.TEXT_GENERATION,
    FeatureAccess.MUSIC_GENERATION,
    FeatureAccess.SPEECH_TO_TEXT,
    FeatureAccess.AI_ASSISTANT,
    FeatureAccess.BASIC_MODELS,
    FeatureAccess.ADVANCED_MODELS,
    FeatureAccess.PREMIUM_MODELS,
    FeatureAccess.HIGH_RESOLUTION,
    FeatureAccess.PRIORITY_PROCESSING,
    FeatureAccess.BATCH_PROCESSING,
    FeatureAccess.DOWNLOADS,
    FeatureAccess.API_ACCESS,
  ],
};

// Model access by subscription levels
export const MODEL_ACCESS = {
  // Basic models - available on all plans
  BASIC: [
    'black-forest-labs/FLUX.1-schnell-Free',
    'google/upscaler',
  ],
  
  // Advanced models - Standard plan and above
  ADVANCED: [
    // Image generation models
    'black-forest-labs/FLUX.1-schnell',
    'black-forest-labs/FLUX.1-pro',
    'black-forest-labs/flux-dev',
    'black-forest-labs/FLUX.1-dev',
    'black-forest-labs/flux-schnell',
    'stabilityai/stable-diffusion-xl-base-1.0',

  ],
  
  // Premium models - Premium plan only
  PREMIUM: [
    // Image generation models
    'black-forest-labs/FLUX.1-schnell',
    'black-forest-labs/FLUX.1-pro',
    'black-forest-labs/flux-dev',
    'black-forest-labs/FLUX.1-dev',
    'black-forest-labs/flux-schnell',
    'stabilityai/stable-diffusion-xl-base-1.0',
    
    // Podcast generation models - for both Standard and Premium
    'eleven_multilingual_v2',
    'eleven_flash_v2_5',
    'eleven_flash_v2',
    'eleven_turbo_v2',
    'playht/play-dialog',
    
    // Add any missing podcast models from your app
    'eleven_monolingual_v1',
    'play-dialog-v1',
    'eleven_englishtts_v1'
    
    // Add any premium-exclusive podcast models here if needed
  ],
};

// Usage limits based on subscription plan
export const USAGE_LIMITS = {
  'free': {
    TEXT_GENERATION: { daily: 10 },
    IMAGE_GENERATION: { daily: 0 },
    VIDEO_GENERATION: { daily: 0 },
    PODCAST_GENERATION: { daily: 0 },
    MUSIC_GENERATION: { daily: 0 },
    SPEECH_TO_TEXT: { daily: 0 },
    AI_ASSISTANT: { daily: 0 },
  },
};

/**
 * Check if a user has access to a specific feature
 * @param userPlan - The user's subscription plan
 * @param feature - The feature to check access for
 * @returns boolean indicating if the user has access
 */
export function hasFeatureAccess(userPlan: string | undefined, feature: FeatureAccess): boolean {
  // Default to free plan if no subscription
  const plan = userPlan || 'free';
  
  // Get the features for this plan
  const availableFeatures = featureAccessByPlan[plan as keyof typeof featureAccessByPlan] || featureAccessByPlan.free;
  
  return availableFeatures.includes(feature);
}

/**
 * Check if a user has access to a specific AI model
 * @param userPlan - The user's subscription plan
 * @param modelId - The ID of the model to check
 * @returns boolean indicating if the user has access
 */
export function hasModelAccess(userPlan: string | undefined, modelId: string): boolean {
  // Default to free plan if no subscription
  const plan = userPlan || 'free';
  
  // For the free FLUX model, always allow access regardless of subscription
  if (modelId === 'black-forest-labs/FLUX.1-schnell-Free' || modelId === 'google/upscaler') {
    return true;
  }
  
  // Always allow access to basic models
  if (MODEL_ACCESS.BASIC.includes(modelId)) {
    return true;
  }
  
  // Check for advanced models
  if (MODEL_ACCESS.ADVANCED.includes(modelId)) {
    return plan === SUBSCRIPTION_TYPES.STANDARD || plan === SUBSCRIPTION_TYPES.PREMIUM;
  }
  
  // Check for premium models
  if (MODEL_ACCESS.PREMIUM.includes(modelId)) {
    return plan === SUBSCRIPTION_TYPES.PREMIUM;
  }
  
  // If the model isn't explicitly categorized, default to premium-only
  return plan === SUBSCRIPTION_TYPES.PREMIUM;
}

/**
 * Check if a user is within their usage limits
 * @param userPlan - The user's subscription plan
 * @param feature - The feature to check limits for
 * @param currentUsage - The user's current usage count for today
 * @returns boolean indicating if the user is within limits
 */
export function isWithinUsageLimits(
  userPlan: string | undefined, 
  feature: FeatureAccess,
  currentUsage: number
): boolean {
  // Default to free plan if no subscription
  const plan = userPlan || 'free';
  
  const planLimits = USAGE_LIMITS[plan as keyof typeof USAGE_LIMITS] || USAGE_LIMITS.free;
  
  // Convert feature to the corresponding usage key
  const usageKey = feature.toLowerCase() as keyof typeof planLimits;
  
  // If there's no limit defined for this feature, default to no access
  if (!planLimits[usageKey]) {
    return false;
  }
  
  const { daily } = planLimits[usageKey];
  
  // If daily limit is 0, no access is allowed
  if (daily === 0) {
    return false;
  }
  
  // Check if the user is within their daily limit
  return currentUsage < daily;
}

/**
 * Check if a user has access to image generation models based on subscription plan
 * @param userPlan - The user's subscription plan
 * @param modelId - The ID of the image generation model to check
 * @returns boolean indicating if the user has access
 */
export function hasImageGenerationModelAccess(userPlan: string | undefined, modelId: string): boolean {
  // Default to free plan if no subscription
  const plan = userPlan || 'free';
  
  // For free-tier specific models, always allow access regardless of subscription
  if (modelId === 'black-forest-labs/FLUX.1-schnell-Free' || modelId === 'google/upscaler') {
    return true;
  }
  
  // Standard and Premium plans have full unrestricted access to all image generation models
  if (plan === SUBSCRIPTION_TYPES.STANDARD || plan === SUBSCRIPTION_TYPES.PREMIUM) {
    return true;
  }
  
  // Free plan or no subscription users only have access to basic models
  if (plan === 'free') {
    return MODEL_ACCESS.BASIC.includes(modelId);
  }
  
  // Default to no access for undefined plans
  return false;
} 