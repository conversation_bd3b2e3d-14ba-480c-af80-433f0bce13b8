import { Redis } from '@upstash/redis';

// Initialize Redis client
let redisClient: Redis | null = null;

export function getRedisClient() {
  if (!redisClient) {
    // Check for required env variables
    if (!process.env.REDIS_URL || !process.env.REDIS_PASSWORD) {
      console.error('Missing Redis credentials in environment variables');
      throw new Error('Redis configuration is not properly set up');
    }
    
    redisClient = new Redis({
      url: `https://${process.env.REDIS_URL}`,
      token: process.env.REDIS_PASSWORD
    });
  }
  
  return redisClient;
}

// TTL in seconds (15 days)
const CHAT_TTL = parseInt(process.env.REDIS_TTL_DAYS ?? '15') * 24 * 60 * 60;

// Key patterns
const getUserConversationsKey = (userId: string) => `user:${userId}:conversations`;
const getConversationKey = (conversationId: string) => `conversation:${conversationId}`;
const getConversationMessagesKey = (conversationId: string) => `conversation:${conversationId}:messages`;

/**
 * Save a conversation with TTL
 */
export async function saveConversation(userId: string, conversation: any) {
  try {
    const redis = getRedisClient();
    
    // Save conversation data
    const conversationKey = getConversationKey(conversation.id);
    await redis.set(conversationKey, JSON.stringify(conversation));
    await redis.expire(conversationKey, CHAT_TTL);
    
    // Add to user's conversation list if not already there
    const userConversationsKey = getUserConversationsKey(userId);
    await redis.sadd(userConversationsKey, conversation.id);
    await redis.expire(userConversationsKey, CHAT_TTL);
    
    return true;
  } catch (error) {
    console.error('Error saving conversation to Redis:', error);
    return false;
  }
}

/**
 * Save messages for a conversation
 */
export async function saveMessages(conversationId: string, messages: any[]) {
  try {
    const redis = getRedisClient();
    const messagesKey = getConversationMessagesKey(conversationId);
    
    // Save messages
    await redis.set(messagesKey, JSON.stringify(messages));
    await redis.expire(messagesKey, CHAT_TTL);
    
    return true;
  } catch (error) {
    console.error('Error saving messages to Redis:', error);
    return false;
  }
}

/**
 * Get all conversations for a user
 */
export async function getUserConversations(userId: string) {
  try {
    const redis = getRedisClient();
    const userConversationsKey = getUserConversationsKey(userId);
    
    // Get conversation IDs
    const conversationIds = await redis.smembers<string[]>(userConversationsKey);
    
    // Get each conversation
    const conversations = [];
    for (const id of conversationIds) {
      const conversationKey = getConversationKey(id);
      const conversationData = await redis.get<string>(conversationKey);
      
      if (conversationData) {
        conversations.push(JSON.parse(conversationData));
      }
    }
    
    return conversations;
  } catch (error) {
    console.error('Error getting user conversations from Redis:', error);
    return [];
  }
}

/**
 * Get a single conversation by ID
 */
export async function getConversation(conversationId: string) {
  try {
    const redis = getRedisClient();
    const conversationKey = getConversationKey(conversationId);
    
    const conversation = await redis.get<string>(conversationKey);
    return conversation ? JSON.parse(conversation) : null;
  } catch (error) {
    console.error('Error getting conversation from Redis:', error);
    return null;
  }
}

/**
 * Get messages for a conversation
 */
export async function getConversationMessages(conversationId: string) {
  try {
    const redis = getRedisClient();
    const messagesKey = getConversationMessagesKey(conversationId);
    
    const messages = await redis.get<string>(messagesKey);
    return messages ? JSON.parse(messages) : [];
  } catch (error) {
    console.error('Error getting conversation messages from Redis:', error);
    return [];
  }
}

/**
 * Delete a conversation and its messages
 */
export async function deleteConversation(userId: string, conversationId: string) {
  try {
    const redis = getRedisClient();
    
    // Delete conversation and messages
    const conversationKey = getConversationKey(conversationId);
    const messagesKey = getConversationMessagesKey(conversationId);
    
    await redis.del(conversationKey);
    await redis.del(messagesKey);
    
    // Remove from user's conversation list
    const userConversationsKey = getUserConversationsKey(userId);
    await redis.srem(userConversationsKey, conversationId);
    
    return true;
  } catch (error) {
    console.error('Error deleting conversation from Redis:', error);
    return false;
  }
} 