import Replicate from 'replicate';

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY || '',
});

/**
 * Send a message to Claude 3.7 Sonnet on Replicate
 */
export async function sendMessageToClaude(
  message: string,
  options: {
    temperature?: number;
    max_tokens?: number;
  } = {}
) {
  try {
    // Default options
    const temperature = options.temperature ?? 0.7;
    const max_tokens = options.max_tokens ?? 4000;
    
    // Format the prompt for Claude
    const prompt = `Human: ${message}\n\nAssistant:`;
    
    // Define model ID for Claude 3.7 Sonnet
    const modelId = 'anthropic/claude-3.7-sonnet';
    
    // Prepare input for Replicate
    const input = {
      prompt: prompt,
      temperature: temperature,
      max_new_tokens: max_tokens,
      system_prompt: "You are <PERSON>, a helpful AI assistant by <PERSON><PERSON><PERSON>.",
    };
    
    // Make direct API call to Replicate
    const response = await fetch("https://api.replicate.com/v1/predictions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Token ${process.env.REPLICATE_API_KEY}`,
      },
      body: JSON.stringify({
        version: modelId,
        input: input,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Replicate API error: ${response.status}`);
    }
    
    const prediction = await response.json();
    
    // Poll for completion
    const predictId = prediction.id;
    let completed = false;
    let result = null;
    
    while (!completed) {
      // Wait 1 second between polls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check status
      const statusResponse = await fetch(`https://api.replicate.com/v1/predictions/${predictId}`, {
        headers: {
          "Authorization": `Token ${process.env.REPLICATE_API_KEY}`,
        },
      });
      
      if (!statusResponse.ok) {
        throw new Error(`Failed to check prediction status: ${statusResponse.status}`);
      }
      
      const statusData = await statusResponse.json();
      
      if (statusData.status === "succeeded") {
        completed = true;
        result = statusData.output;
      } else if (statusData.status === "failed") {
        throw new Error(`Prediction failed: ${statusData.error}`);
      }
      // Continue polling if still processing
    }
    
    // Format the response
    return {
      message: result,
      model: modelId,
      provider: 'Replicate'
    };
  } catch (error) {
    console.error('Error in Replicate completion:', error);
    throw error;
  }
}

export default {
  sendMessageToClaude
}; 