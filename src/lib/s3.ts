import { 
  S3Client, 
  PutObjectCommand, 
  GetObjectCommand, 
  DeleteObjectCommand, 
  ListObjectsCommand 
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Initialize S3 client with Supabase S3 configuration
const s3Client = new S3Client({
  region: process.env.NEXT_PUBLIC_S3_REGION || 'us-east-1',
  endpoint: 'https://gntleknxbxbmzwizidgv.supabase.co/storage/v1',
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY_ID || 'af73fb512d54487ad6cbefc17df357b7',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_ACCESS_KEY || '0a0de5e4f9fc5752870e965e3d5c18f9c369988f6b50561bc8bf009daaceb51c',
  },
  forcePathStyle: true, // Required for Supabase S3 compatibility
});

// AWS S3 bucket names
const BUCKET_NAMES = {
  audio: process.env.S3_AUDIO_BUCKET || 'audio-files',
  image: process.env.S3_IMAGE_BUCKET || 'images',
  video: process.env.S3_VIDEO_BUCKET || 'videos',
  podcast: process.env.S3_PODCAST_BUCKET || 'podcasts',
  voice: process.env.S3_VOICE_BUCKET || 'voice-samples',
};

export type FileType = 'audio' | 'image' | 'video' | 'podcast' | 'voice';

/**
 * Get S3 bucket name for a specific file type
 */
export const getBucketName = (type: FileType): string => {
  return BUCKET_NAMES[type];
};

/**
 * Generate a unique file path for S3
 */
export const generateFilePath = (
  userId: string,
  fileName: string,
  folder?: string
): string => {
  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
  const timestamp = Date.now();
  const uniqueId = Math.random().toString(36).substring(2, 8);
  
  const basePath = folder ? `${userId}/${folder}` : userId;
  return `${basePath}/${timestamp}-${uniqueId}-${sanitizedFileName}`;
};

/**
 * Upload a file to S3
 */
export const uploadToS3 = async (
  file: Buffer | Blob | ReadableStream | string,
  fileName: string,
  fileType: string,
  userId: string,
  type: FileType,
  folder?: string
): Promise<{ url: string; key: string }> => {
  try {
    console.log(`Starting server-side S3 upload process for ${fileName}...`);
    
    // We'll first ensure we have our file as a data URL
    let fileDataUrl = '';
    
    if (typeof file === 'string') {
      if (file.startsWith('data:')) {
        // Already a data URL, use directly
        console.log('Input is already a data URL, using directly');
        fileDataUrl = file;
      } else {
        // String URL, fetch and convert to data URL
        console.log(`Converting URL to data URL: ${file.substring(0, 50)}...`);
        
        try {
          // First try direct fetch and convert
          const response = await fetch(file);
          
          if (!response.ok) {
            throw new Error(`Failed to fetch URL: ${response.status} ${response.statusText}`);
          }
          
          const blob = await response.blob();
          
          // Convert to data URL
          fileDataUrl = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
          
          console.log('Successfully converted URL to data URL');
        } catch (fetchError) {
          console.error('Error fetching URL:', fetchError);
          
          // Try server-side download as fallback
          console.log('Falling back to server-side download');
          
          try {
            const downloadResponse = await fetch('/api/download-image', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ imageUrl: file })
            });
            
            if (!downloadResponse.ok) {
              throw new Error(`Server download failed: ${downloadResponse.status}`);
            }
            
            const downloadResult = await downloadResponse.json();
            
            if (downloadResult.success && downloadResult.dataUrl) {
              fileDataUrl = downloadResult.dataUrl;
              console.log('Successfully downloaded via server API');
            } else {
              throw new Error('Failed to get data URL from server download');
            }
          } catch (serverDownloadError) {
            console.error('Server download also failed:', serverDownloadError);
            throw new Error('Could not process image URL for upload');
          }
        }
      }
    } else if (file instanceof Blob) {
      // Convert Blob to data URL
      console.log(`Converting Blob to data URL: ${file.type}, ${file.size} bytes`);
      fileDataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    } else {
      // Handle other types by first converting to string, then server will handle
      console.error('Unsupported file type for upload:', typeof file);
      throw new Error('Unsupported file type. Please provide a URL or Blob.');
    }
    
    // Now upload via our server API endpoint
    console.log('Uploading via server-side API...');
    
    const formData = new FormData();
    formData.append('fileData', fileDataUrl);
    formData.append('fileName', fileName);
    formData.append('fileType', fileType);
    formData.append('userId', userId);
    formData.append('type', type);
    if (folder) {
      formData.append('folder', folder);
    }
    
    // Add timeout to the fetch request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
    
    try {
      const uploadResponse = await fetch('/api/s3-upload', {
        method: 'POST',
        body: formData,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        throw new Error(`Server S3 upload failed: ${uploadResponse.status} - ${errorText}`);
      }
      
      const result = await uploadResponse.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to upload file via server');
      }
      
      // If we got a local storage fallback response (data URL)
      if (result.storage === 'local') {
        console.log('Server used local storage fallback - file saved as data URL');
      } else {
        console.log(`Successfully uploaded ${fileName} via server API to S3`);
      }
      
      return {
        url: result.url,
        key: result.key
      };
    } catch (fetchError: any) {
      if (fetchError.name === 'AbortError') {
        console.error('Upload request timed out after 30 seconds');
        throw new Error('Upload request timed out. Please try again.');
      }
      throw fetchError;
    }
  } catch (error: any) {
    console.error('Error in uploadToS3:', error);
    throw new Error(`Failed to upload file: ${error.message}`);
  }
};

/**
 * Helper function that tries to upload an image URL by first converting it to a canvas
 * This is useful for handling CORS issues or opaque responses
 */
async function uploadWithCanvas(
  imageUrl: string,
  bucketName: string,
  key: string,
  contentType: string
): Promise<{ url: string; key: string }> {
  console.log('Attempting to upload image using canvas approach');
  
  // For client-side only
  if (typeof window === 'undefined') {
    throw new Error('Canvas approach only works in browser environment');
  }
  
  // Try multiple approaches to load the image
  async function tryLoadImage(): Promise<HTMLImageElement> {
    // First try: Direct loading with crossOrigin attribute
    try {
      console.log('Attempting to load image directly with crossOrigin="anonymous"');
      const img = await new Promise<HTMLImageElement>((resolve, reject) => {
        const image = new Image();
        image.crossOrigin = 'anonymous';
        
        const timeout = setTimeout(() => {
          reject(new Error('Image loading timed out'));
        }, 10000);
        
        image.onload = () => {
          clearTimeout(timeout);
          resolve(image);
        };
        
        image.onerror = (err) => {
          clearTimeout(timeout);
          console.error('Direct image loading failed:', err);
          reject(new Error('Failed to load image with crossOrigin'));
        };
        
        image.src = imageUrl;
      });
      
      console.log('Image loaded successfully with direct approach');
      return img;
    } catch (directError) {
      console.log('Direct loading failed, trying proxy approach');
      
      // Second try: Use a proxy if available
      try {
        // Check if we have the proxy endpoint
        const proxyResponse = await fetch('/api/proxy-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ imageUrl })
        });
        
        if (!proxyResponse.ok) {
          throw new Error(`Proxy fetch failed: ${proxyResponse.status}`);
        }
        
        // Get blob from proxy
        const blob = await proxyResponse.blob();
        const blobUrl = URL.createObjectURL(blob);
        
        console.log('Successfully fetched image through proxy, loading from blob URL');
        
        // Load image from blob URL
        const img = await new Promise<HTMLImageElement>((resolve, reject) => {
          const image = new Image();
          
          const timeout = setTimeout(() => {
            URL.revokeObjectURL(blobUrl);
            reject(new Error('Image loading from blob URL timed out'));
          }, 10000);
          
          image.onload = () => {
            clearTimeout(timeout);
            resolve(image);
          };
          
          image.onerror = (err) => {
            clearTimeout(timeout);
            URL.revokeObjectURL(blobUrl);
            reject(new Error('Failed to load image from blob URL'));
          };
          
          image.src = blobUrl;
        });
        
        console.log('Successfully loaded image from proxy blob');
        return img;
      } catch (proxyError) {
        console.error('Proxy approach failed:', proxyError);
        
        // Third try: Try with a data URL if it is one
        if (imageUrl.startsWith('data:')) {
          try {
            console.log('Image appears to be a data URL, loading directly');
            const image = new Image();
            
            await new Promise<void>((resolve, reject) => {
              const timeout = setTimeout(() => {
                reject(new Error('Data URL image loading timed out'));
              }, 5000);
              
              image.onload = () => {
                clearTimeout(timeout);
                resolve();
              };
              
              image.onerror = () => {
                clearTimeout(timeout);
                reject(new Error('Failed to load data URL'));
              };
              
              image.src = imageUrl;
            });
            
            return image;
          } catch (dataUrlError) {
            console.error('Data URL loading failed:', dataUrlError);
          }
        }
        
        // Final fallback - create a placeholder
        throw new Error('All image loading approaches failed');
      }
    }
  }
  
  try {
    // Try to load the image using one of our approaches
    const img = await tryLoadImage();
    console.log(`Image loaded successfully: ${img.width}x${img.height}`);
    
    // Create a canvas and draw the image to it
    const canvas = document.createElement('canvas');
    canvas.width = img.width || 800;
    canvas.height = img.height || 800;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    // Draw white background first (for transparent images)
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw the image
    ctx.drawImage(img, 0, 0);
    
    // Get as data URL
    const dataUrl = canvas.toDataURL('image/png', 0.95);
    console.log(`Converted image to data URL: ${dataUrl.substring(0, 50)}...`);
    
    // Upload the data URL
    const result = await uploadToDataUrl(dataUrl, bucketName, key, contentType || 'image/png');
    return result;
  } catch (error: any) {
    console.error('Canvas approach failed with error:', error);
    
    // If we couldn't load the image at all, try a last resort placeholder
    try {
      // Create a placeholder image with error text
      console.log('Creating placeholder error image');
      const canvas = document.createElement('canvas');
      canvas.width = 400;
      canvas.height = 300;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get canvas context for placeholder');
      }
      
      // Draw error background
      ctx.fillStyle = '#333333';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw error text
      ctx.fillStyle = '#ffffff';
      ctx.font = '20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Image could not be loaded', canvas.width / 2, canvas.height / 2 - 20);
      ctx.font = '14px Arial';
      ctx.fillText('(CORS or network error)', canvas.width / 2, canvas.height / 2 + 20);
      
      // Get as data URL
      const dataUrl = canvas.toDataURL('image/png', 0.95);
      
      // Upload the placeholder
      const result = await uploadToDataUrl(dataUrl, bucketName, key, 'image/png');
      return result;
    } catch (placeholderError) {
      console.error('Even placeholder creation failed:', placeholderError);
      throw new Error(`Canvas upload failed completely: ${error.message}`);
    }
  }
}

/**
 * Helper function to upload a data URL to S3
 */
async function uploadToDataUrl(
  dataUrl: string,
  bucketName: string,
  key: string,
  contentType: string
): Promise<{ url: string; key: string }> {
  console.log('Attempting to upload data URL to S3');
  
  try {
    // Verify it's a valid data URL
    if (!dataUrl.startsWith('data:')) {
      throw new Error('Invalid data URL format');
    }
    
    // Extract base64 data
    const matches = dataUrl.match(/^data:([^;]+);base64,(.+)$/);
    if (!matches || matches.length !== 3) {
      throw new Error('Invalid data URL format - could not extract base64 data');
    }
    
    const actualFileType = matches[1];
    const base64Data = matches[2];
    console.log(`Parsed data URL with type: ${actualFileType}`);
    
    // Convert base64 to binary
    const binaryData = Buffer.from(base64Data, 'base64');
    console.log(`Converted ${base64Data.length} chars of base64 to ${binaryData.length} bytes binary`);
    
    // Upload to S3
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: binaryData,
      ContentType: contentType || actualFileType,
    });
    
    const response = await s3Client.send(command);
    console.log(`Successfully uploaded data URL to S3: ${response.ETag}`);
    
    const signedUrl = await getSignedUrl(s3Client, new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    }), { expiresIn: 3600 });
    
    return {
      url: signedUrl,
      key,
    };
  } catch (error: any) {
    console.error('Error uploading data URL to S3:', error);
    throw new Error(`Failed to upload data URL: ${error.message}`);
  }
}

/**
 * Get a signed URL for a file in S3
 */
export const getSignedS3Url = async (
  key: string,
  type: FileType,
  expiresIn: number = 3600
): Promise<string> => {
  try {
    const bucketName = getBucketName(type);
    
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });
    
    return await getSignedUrl(s3Client, command, { expiresIn });
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw new Error('Failed to generate signed URL');
  }
};

/**
 * Delete a file from S3
 */
export const deleteFromS3 = async (
  key: string,
  type: FileType
): Promise<boolean> => {
  try {
    const bucketName = getBucketName(type);
    
    const command = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: key,
    });
    
    await s3Client.send(command);
    return true;
  } catch (error) {
    console.error('Error deleting from S3:', error);
    throw new Error('Failed to delete file from S3');
  }
};

/**
 * List files in a folder in S3
 */
export const listS3Files = async (
  userId: string,
  type: FileType,
  folder?: string,
  limit: number = 1000
): Promise<Array<{ key: string; size: number; lastModified: Date }>> => {
  try {
    const bucketName = getBucketName(type);
    const prefix = folder ? `${userId}/${folder}/` : `${userId}/`;
    
    const command = new ListObjectsCommand({
      Bucket: bucketName,
      Prefix: prefix,
      MaxKeys: limit,
    });
    
    const response = await s3Client.send(command);
    
    if (!response.Contents || response.Contents.length === 0) {
      return [];
    }
    
    return response.Contents.map(item => ({
      key: item.Key || '',
      size: item.Size || 0,
      lastModified: item.LastModified || new Date(),
    }));
  } catch (error) {
    console.error('Error listing S3 files:', error);
    throw new Error('Failed to list files from S3');
  }
}; 