import { stripe } from '@/lib/stripe/stripe';
import { UserService } from './user-service';
import { SubscriptionService } from './subscription-service';
import type <PERSON><PERSON> from 'stripe';
import crypto from 'crypto';
import { getDriver } from '@/lib/neo4j/neo4j-client';
import neo4j, { ManagedTransaction, Record } from 'neo4j-driver';

/**
 * Service to synchronize Stripe data with Neo4j
 */
export class StripeSyncService {
  /**
   * Sync all Stripe customers with Neo4j
   * This ensures all paying customers have accounts in our system without duplicates
   */
  static async syncAllCustomers() {
    console.log('Starting Stripe customer sync with Neo4j');
    let syncCount = 0;
    let errorCount = 0;
    let skippedCount = 0;
    
    try {
      // Get all customers from Stripe
      let hasMore = true;
      let startingAfter: string | undefined = undefined;
      
      while (hasMore) {
        const customers: Stripe.ApiList<Stripe.Customer> = await stripe.customers.list({
          limit: 100,
          starting_after: startingAfter,
          expand: ['data.subscriptions']
        });
        
        // Process each customer
        for (const customer of customers.data) {
          try {
            if (!customer.email) {
              console.warn(`Customer ${customer.id} has no email - skipping sync`);
              skippedCount++;
              continue;
            }

            // Check if user exists in Neo4j by email
            const existingUser = await UserService.getUserByEmail(customer.email);
            
            if (existingUser) {
              console.log(`User already exists for ${customer.email} - updating Stripe ID`);
              
              // Update Stripe customer ID if needed
              if (!existingUser.stripeCustomerId || existingUser.stripeCustomerId !== customer.id) {
                await UserService.updateUserStripeCustomerId(existingUser.id, customer.id);
              }
              
              // Sync subscriptions to the existing user
              if (customer.subscriptions && customer.subscriptions.data) {
                for (const subscription of customer.subscriptions.data) {
                  await this.syncSubscription(subscription, existingUser.id);
                }
              }
              
              syncCount++;
              continue;
            }
            
            // Check if there are any active subscriptions
            let subscriptionType: 'standard' | 'premium' | undefined = undefined; // Default to undefined (free)
            let priceId = '';
            let stripeSubscriptionId = '';
            let stripeSubscriptionStatus = '';
            
            if (customer.subscriptions && customer.subscriptions.data.length > 0) {
              // Find active subscriptions
              const activeSubscription = customer.subscriptions.data.find(
                sub => sub.status === 'active' || sub.status === 'trialing'
              );
              
              if (activeSubscription) {
                priceId = (activeSubscription.items.data[0].price as Stripe.Price).id;
                stripeSubscriptionId = activeSubscription.id;
                stripeSubscriptionStatus = activeSubscription.status;
                subscriptionType = priceId === process.env.STRIPE_PREMIUM_PRICE_ID ? 'premium' : 'standard';
              }
            }
            
            // Create new user with subscription data
            console.log(`Creating new user for ${customer.email} with subscription type ${subscriptionType || 'free'}`);
            const tempPassword = crypto.randomBytes(16).toString('hex');
            const name = customer.name || customer.email.split('@')[0];
            
            const newUser = await UserService.createUser({
              email: customer.email,
              password: tempPassword,
              name,
              priceId,
              stripeCustomerId: customer.id,
              stripeSubscriptionId,
              stripeSubscriptionStatus,
              subscriptionType, // Pass as is - undefined means free
              isGuest: true
            });
            
            if (!newUser) {
              console.error(`Failed to create user for ${customer.email}`);
              errorCount++;
              continue;
            }
            
            syncCount++;
          } catch (error) {
            console.error(`Error syncing customer ${customer.id}:`, error);
            errorCount++;
          }
        }
        
        // Prepare for next batch
        hasMore = customers.has_more;
        if (hasMore && customers.data.length > 0) {
          startingAfter = customers.data[customers.data.length - 1].id;
        }
      }
      
      console.log(`Stripe sync completed: ${syncCount} customers processed, ${skippedCount} skipped, ${errorCount} errors`);
      return { success: true, syncCount, skippedCount, errorCount };
    } catch (error) {
      console.error('Error syncing Stripe customers:', error);
      return { success: false, error, syncCount, errorCount };
    }
  }
  
  /**
   * Sync a specific Stripe customer with Neo4j
   */
  static async syncCustomer(customer: Stripe.Customer) {
    const email = customer.email;
    if (!email) {
      console.warn(`Customer ${customer.id} has no email - skipping sync`);
      return null;
    }
    
    // Check if user exists in Neo4j by email
    let user = await UserService.getUserByEmail(email);
    
    // If user doesn't exist, create a new one with temporary password
    if (!user) {
      console.log(`Creating new Neo4j user for Stripe customer ${customer.id} with email ${email}`);
      const tempPassword = crypto.randomBytes(16).toString('hex');
      
      // Get customer name if available
      const name = customer.name || (email.split('@')[0]);
      
      // Create user with Stripe metadata
      user = await UserService.createUser({
        email,
        password: tempPassword,
        name,
        priceId: process.env.STRIPE_STANDARD_PRICE_ID ?? process.env.STRIPE_PREMIUM_PRICE_ID ?? '',
        stripePriceId: process.env.STRIPE_STANDARD_PRICE_ID ?? process.env.STRIPE_PREMIUM_PRICE_ID ?? '',
        stripeCustomerId: customer.id,
        isGuest: true,
      });
      
      if (!user) {
        throw new Error(`Failed to create user for Stripe customer ${customer.id}`);
      }
    } else {
      console.log(`Found existing Neo4j user for Stripe customer ${customer.id}`);
      
      // Update Stripe customer ID if needed
      if (!user.stripeCustomerId || user.stripeCustomerId !== customer.id) {
        await UserService.updateUserStripeCustomerId(user.id, customer.id);
      }
    }
    
    // Sync subscriptions if the customer has any
    if (customer.subscriptions && customer.subscriptions.data) {
      for (const subscription of customer.subscriptions.data) {
        await this.syncSubscription(subscription, user.id);
      }
    } else {
      // If expanded subscriptions not available, fetch them separately
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all'
      });
      
      for (const subscription of subscriptions.data) {
        await this.syncSubscription(subscription, user.id);
      }
    }
    
    return user;
  }
  
  /**
   * Sync a Stripe subscription with Neo4j
   * Updated to avoid creating subscription nodes - only update user properties
   */
  static async syncSubscription(subscription: Stripe.Subscription, userId: string, options: { skipIfUserHasData?: boolean } = {}) {
    try {
      // Skip non-active subscriptions
      if (subscription.status !== 'active' && subscription.status !== 'trialing') {
        console.log(`Skipping non-active subscription ${subscription.id} with status ${subscription.status}`);
        return null;
      }
      
      console.log(`Syncing subscription data to user ${userId} without creating subscription node`);
      
      // Determine subscription type from price ID
      const priceId = (subscription.items.data[0].price as Stripe.Price).id;
      const subscriptionType = priceId === process.env.STRIPE_PREMIUM_PRICE_ID ? 'Premium' : 'Standard';
      
      // Get or create user in database
      const user = await UserService.getUserById(userId);
      
      if (!user) {
        console.error(`User ${userId} not found, cannot sync subscription data`);
        return null;
      }
      
      // Check if existing subscription exists in Neo4j (for backward compatibility)
      const existingSubscription = await SubscriptionService.getSubscriptionByStripeId(subscription.id);
      
      // If an existing subscription node is found, update it but don't create new ones
      if (existingSubscription) {
        console.log(`Found existing Neo4j subscription ${existingSubscription.id}, updating it`);
        
        // If subscription exists but linked to different user, update the link
        if (existingSubscription.userId !== userId) {
          console.log(`Linking subscription ${existingSubscription.id} to user ${userId} (was ${existingSubscription.userId})`);
          await SubscriptionService.updateSubscriptionUser(existingSubscription.id, userId);
        }
        
        // Update subscription status and end date if needed
        if (existingSubscription.status !== subscription.status) {
          await SubscriptionService.updateSubscriptionStatus(
            existingSubscription.id, 
            subscription.status as any
          );
        }
        
        const endDate = new Date(subscription.current_period_end * 1000).toISOString();
        await SubscriptionService.updateSubscriptionEndDate(existingSubscription.id, endDate);
      }
      
      // Always update user properties with subscription data
      // This will be the primary place subscription data is stored
      await UserService.updateUserSubscriptionDetails(userId, {
        stripeCustomerId: subscription.customer as string,
        stripeSubscriptionId: subscription.id,
        stripePriceId: priceId,
        subscriptionType: subscriptionType.toLowerCase() as 'standard' | 'premium',
        stripeSubscriptionStatus: subscription.status
      });
      
      // Return subscription info from user data
      return {
        id: subscription.id,
        userId,
        type: subscriptionType,
        status: subscription.status as any,
        startDate: new Date(subscription.current_period_start * 1000).toISOString(),
        endDate: new Date(subscription.current_period_end * 1000).toISOString(),
        stripeSubscriptionId: subscription.id,
        stripePriceId: priceId,
        stripeCustomerId: subscription.customer as string
      };
    } catch (error) {
      console.error('Error syncing subscription:', error);
      throw error;
    }
  }
  
  /**
   * Validate a user's subscription status using both Neo4j and Stripe
   */
  static async validateSubscription(email: string): Promise<{
    isValid: boolean;
    subscriptionType?: 'Standard' | 'Premium';
    source: 'neo4j' | 'stripe';
    customerId?: string;
    priceId?: string;
    userId?: string;
  }> {
    try {
      console.log(`Validating subscription for email: ${email}`);
      
      // First, check if user exists in Neo4j
      const user = await UserService.getUserByEmail(email);
      
      // If user exists in Neo4j, check for subscription there first
      if (user) {
        const subscription = await SubscriptionService.getUserSubscription(user.id);
        if (subscription && subscription.status === 'active') {
          return {
            isValid: true,
            subscriptionType: subscription.type,
            source: 'neo4j',
            customerId: subscription.stripeCustomerId,
            priceId: subscription.stripePriceId,
            userId: user.id
          };
        }
      }

      // No valid subscription in Neo4j, try Stripe and sync if found
      const customers = await stripe.customers.list({ email, limit: 1 });
      
      if (customers.data.length > 0) {
        const customer = customers.data[0];
        console.log(`Found Stripe customer: ${customer.id} for email: ${email}`);
        
        const subscriptions = await stripe.subscriptions.list({
          customer: customer.id,
          status: 'all',
          limit: 10
        });

        // Filter for active or trialing subscriptions
        const activeSubscriptions = subscriptions.data.filter(
          sub => sub.status === 'active' || sub.status === 'trialing'
        );

        if (activeSubscriptions.length > 0) {
          const subscription = activeSubscriptions[0];
          const priceId = (subscription.items.data[0].price as Stripe.Price).id;
          
          // Determine subscription type from price ID
          const subscriptionType = priceId === process.env.STRIPE_PREMIUM_PRICE_ID ? 'Premium' : 'Standard';
          
          // Create or update user in Neo4j
          let userId = user?.id;
          let isNewUser = false;
          
          if (!user) {
            // Create user if doesn't exist
            const createResult = await this.createUserFromStripeData(email, {
              stripeCustomerId: customer.id,
              stripePriceId: priceId,
              subscriptionType
            });
            
            if (createResult.success && createResult.userId) {
              userId = createResult.userId;
              isNewUser = true;
            }
          } else {
            // Update existing user with subscription info
            await UserService.updateUserSubscriptionDetails(user.id, {
              stripeCustomerId: customer.id,
              stripeSubscriptionId: subscription.id,
              stripePriceId: priceId,
              subscriptionType: subscriptionType.toLowerCase() as 'standard' | 'premium',
              stripeSubscriptionStatus: subscription.status
            });
          }
          
          // Sync the subscription to Neo4j if we have a user ID
          if (userId) {
            // Skip creating subscription node if this is a new user we just created
            await this.syncSubscription(subscription, userId, { skipIfUserHasData: isNewUser });
          }

          return {
            isValid: true,
            subscriptionType,
            source: 'stripe',
            customerId: customer.id,
            priceId,
            userId
          };
        }
      }

      return { isValid: false, source: 'stripe' };
    } catch (error) {
      console.error('Error validating subscription:', error);
      return { isValid: false, source: 'stripe' };
    }
  }

  /**
   * Enhanced Google login handling with subscription validation
   */
  static async handleGoogleLogin(email: string, googleUserId: string) {
    console.log(`Handling Google login for ${email} with Google ID ${googleUserId}`);
    
    try {
      // First check if user exists in Neo4j by email
      let user = await UserService.getUserByEmail(email);
      
      // If user exists, just update Google auth data and return
      if (user) {
        console.log(`User already exists in Neo4j: ${user.id}`);
        
        // Update Google ID in user metadata
        await UserService.updateUserMetadata(user.id, {
          googleUserId,
          lastGoogleLogin: new Date().toISOString()
        });
        
        // Link any Stripe subscription if it exists
        if (!user.stripeCustomerId) {
          await this.linkSubscriptionsByEmail(email, user.id);
        }
        
        return user;
      }
      
      // User doesn't exist, check Stripe for subscription data
      const customers = await stripe.customers.list({ email, limit: 1 });
      let stripeCustomerId = '';
      let priceId = '';
      let subscriptionType: 'standard' | 'premium' | undefined = undefined; // Default to undefined (free)
      let stripeSubscriptionId = '';
      let stripeSubscriptionStatus = '';
      
      if (customers.data.length > 0) {
        const customer = customers.data[0];
        stripeCustomerId = customer.id;
        
        // Check for active subscriptions
        const subscriptions = await stripe.subscriptions.list({
          customer: customer.id,
          status: 'active',
          limit: 1
        });
        
        if (subscriptions.data.length > 0) {
          const subscription = subscriptions.data[0];
          priceId = (subscription.items.data[0].price as Stripe.Price).id;
          stripeSubscriptionId = subscription.id;
          stripeSubscriptionStatus = subscription.status;
          
          // Set subscription type based on price ID
          subscriptionType = priceId === process.env.STRIPE_PREMIUM_PRICE_ID ? 'premium' : 'standard';
          console.log(`Found active ${subscriptionType} subscription for Google user: ${email}`);
        } else {
          console.log(`No active subscription found for Google user: ${email}`);
        }
      }
      
      // Create a new user
      console.log(`Creating new Neo4j user for Google login ${email} with subscription: ${subscriptionType || 'free'}`);
      
      // Generate random password
      const tempPassword = crypto.randomBytes(16).toString('hex');
      
      // Create user with Google info and any Stripe data found
      user = await UserService.createUser({
        email,
        password: tempPassword,
        name: email.split('@')[0],
        priceId,
        stripeCustomerId,
        stripeSubscriptionId,
        stripeSubscriptionStatus,
        subscriptionType, // Pass as is - undefined means free
        isGuest: true
      });
      
      if (!user) {
        throw new Error(`Failed to create user for Google login ${email}`);
      }
      
      // Store Google ID in metadata
      await UserService.updateUserMetadata(user.id, {
        googleUserId,
        lastGoogleLogin: new Date().toISOString()
      });
      
      return user;
    } catch (error) {
      console.error('Error in handleGoogleLogin:', error);
      throw error;
    }
  }
  
  /**
   * Link any Stripe subscriptions to a user by email
   */
  static async linkSubscriptionsByEmail(email: string, userId: string) {
    try {
      // Find Stripe customer by email
      const customers = await stripe.customers.list({ email, limit: 1 });
      
      if (customers.data.length === 0) {
        console.log(`No Stripe customer found for email ${email}`);
        return [];
      }
      
      const customer = customers.data[0];
      console.log(`Found Stripe customer ${customer.id} for email ${email}`);
      
      // Update user's Stripe customer ID if needed
      await UserService.updateUserStripeCustomerId(userId, customer.id);
      
      // Get all subscriptions for this customer
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all'
      });
      
      const linkedSubscriptions = [];
      
      // Link each subscription, prioritizing active and trialing ones
      const prioritySubscriptions = subscriptions.data.filter(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );
      
      // Process priority subscriptions first (active and trialing)
      for (const subscription of prioritySubscriptions) {
        const result = await this.syncSubscription(subscription, userId);
        if (result) {
          linkedSubscriptions.push(result);
        }
      }
      
      // If no active/trialing subscriptions were found, process others
      if (linkedSubscriptions.length === 0) {
        for (const subscription of subscriptions.data) {
          // Skip if already processed above
          if (prioritySubscriptions.includes(subscription)) continue;
          
          const result = await this.syncSubscription(subscription, userId);
          if (result) {
            linkedSubscriptions.push(result);
          }
        }
      }
      
      return linkedSubscriptions;
    } catch (error) {
      console.error('Error linking subscriptions by email:', error);
      throw error;
    }
  }

  /**
   * Direct validation and registration for Google users
   * This should be called for any Google user to ensure they have Neo4j accounts and subscriptions
   */
  static async validateAndRegisterGoogleUser(email: string): Promise<{
    isValid: boolean;
    subscriptionType?: 'Standard' | 'Premium' | 'Free';
    userId?: string;
  }> {
    try {
      console.log(`Direct validation for Google user with email: ${email}`);
      
      // First check if user already exists in Neo4j
      let user = await UserService.getUserByEmail(email);
      
      if (user) {
        console.log(`User already exists in Neo4j: ${user.id}`);
        
        // If user already has a subscription type in Neo4j, trust that
        if (user.subscriptionType && 
           (user.subscriptionType.toLowerCase() === 'standard' || 
            user.subscriptionType.toLowerCase() === 'premium')) {
          
          // Use proper casing for the response
          const subscriptionType = user.subscriptionType.toLowerCase() === 'standard' ? 'Standard' : 'Premium';
          console.log(`Using existing Neo4j subscription data: ${subscriptionType}`);
          
          return { 
            isValid: true, 
            subscriptionType,
            userId: user.id 
          };
        }
      }
      
      // Check Stripe for subscription data
      const customers = await stripe.customers.list({ email, limit: 1 });
      if (customers.data.length === 0) {
        console.log(`No Stripe customer found for Google user email: ${email}`);
        
        // If user exists but has no Stripe subscription, allow access as Free
        if (user) {
          return { 
            isValid: true, 
            subscriptionType: 'Free',
            userId: user.id 
          };
        }
        
        return { isValid: false };
      }
      
      const customer = customers.data[0];
      console.log(`Found Stripe customer ${customer.id} for Google user email: ${email}`);
      
      // Check for active subscriptions
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'active',
        limit: 1
      });
      
      if (subscriptions.data.length === 0) {
        console.log(`No active subscription found for Google user email: ${email}`);
        
        // If user exists but has no active subscription, allow access as Free
        if (user) {
          return { 
            isValid: true, 
            subscriptionType: 'Free',
            userId: user.id 
          };
        }
        
        return { isValid: false };
      }
      
      const subscription = subscriptions.data[0];
      const priceId = (subscription.items.data[0].price as Stripe.Price).id;
      
      // Determine subscription type based on price ID
      let subscriptionType: 'Standard' | 'Premium';
      if (priceId === process.env.STRIPE_PREMIUM_PRICE_ID) {
        subscriptionType = 'Premium';
      } else {
        subscriptionType = 'Standard';
      }
      
      console.log(`Found ${subscriptionType} subscription for Google user with email: ${email}`);
      
      // If user exists, update their subscription info
      if (user) {
        console.log(`Updating existing user ${user.id} with Stripe subscription data`);
        
        // Update user metadata with Stripe info
        await UserService.updateUserSubscriptionDetails(user.id, {
          stripeCustomerId: customer.id,
          stripeSubscriptionId: subscription.id,
          stripePriceId: priceId,
          subscriptionType: subscriptionType.toLowerCase() as 'standard' | 'premium',
          stripeSubscriptionStatus: subscription.status
        });
        
        return { 
          isValid: true, 
          subscriptionType,
          userId: user.id 
        };
      }
      
      // User doesn't exist, create one
      console.log(`Creating Neo4j user for Google user with email: ${email}`);
      
      // Generate temporary password
      const tempPassword = crypto.randomBytes(16).toString('hex');
      
      // Create the user
      user = await UserService.createUser({
        email,
        password: tempPassword,
        name: email.split('@')[0],
        priceId,
        stripeCustomerId: customer.id,
        stripeSubscriptionId: subscription.id,
        subscriptionType: subscriptionType.toLowerCase() as 'standard' | 'premium',
        stripeSubscriptionStatus: subscription.status,
        isGuest: true
      });
      
      if (!user) {
        console.error(`Failed to create Neo4j user for Google user with email: ${email}`);
        return { isValid: false };
      }
      
      return { 
        isValid: true, 
        subscriptionType,
        userId: user.id 
      };
    } catch (error) {
      console.error('Error in validateAndRegisterGoogleUser:', error);
      return { isValid: false };
    }
  }

  /**
   * Get users by their Stripe customer ID
   */
  static async getUsersByStripeCustomerId(stripeCustomerId: string) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx: ManagedTransaction) =>
        tx.run(
          `MATCH (u:User {stripeCustomerId: $stripeCustomerId})
           RETURN u`,
          { stripeCustomerId }
        )
      );
      
      return result.records.map((record: Record) => {
        const user = record.get('u').properties;
        return user;
      });
    } catch (error) {
      console.error('Error getting users by Stripe customer ID:', error);
      return null;
    } finally {
      await session.close();
    }
  }

  /**
   * Synchronize paid user with immediate access
   * This ensures users get immediate access to their subscription after payment
   */
  static async syncPaidUserWithImmediateAccess(
    email: string,
    stripeCustomerId: string,
    subscriptionId: string = ''
  ): Promise<boolean> {
    console.log(`Syncing paid user with immediate access: ${email}, Customer: ${stripeCustomerId}`);
    
    try {
      // If no subscription ID provided, try to find active subscription
      let subscription;
      if (!subscriptionId) {
        console.log(`No subscription ID provided, looking up active subscriptions for customer: ${stripeCustomerId}`);
        const subscriptions = await stripe.subscriptions.list({
          customer: stripeCustomerId,
          status: 'active',
          limit: 1
        });
        
        if (subscriptions.data.length === 0) {
          console.log(`No active subscriptions found for customer: ${stripeCustomerId}`);
          return false;
        }
        
        subscription = subscriptions.data[0];
        subscriptionId = subscription.id;
        console.log(`Found active subscription: ${subscriptionId}`);
      } else {
        // Get subscription details from Stripe
        subscription = await stripe.subscriptions.retrieve(subscriptionId);
      }
      
      if (subscription.status !== 'active') {
        console.log(`Subscription ${subscriptionId} is not active: ${subscription.status}`);
        return false;
      }
      
      // Get user from Neo4j
      let user = await UserService.getUserByEmail(email);
      
      // If no user exists, try to find by stripeCustomerId
      if (!user) {
        const usersWithStripeId = await this.getUsersByStripeCustomerId(stripeCustomerId);
        if (usersWithStripeId && usersWithStripeId.length > 0) {
          user = usersWithStripeId[0];
        }
      }
      
      // Get customer info from Stripe
      const customerData = await stripe.customers.retrieve(stripeCustomerId);
      const customerName = typeof customerData !== 'string' && !customerData.deleted 
        ? customerData.name || email.split('@')[0] 
        : email.split('@')[0];
      
      // Create user if they don't exist (handles case of payment before registration)
      if (!user) {
        console.log(`Creating new user for paid subscription: ${email}`);
        
        // Generate a secure random password
        const tempPassword = crypto.randomBytes(16).toString('hex');
        
        // Get price info
        const priceId = (subscription.items.data[0].price as Stripe.Price).id;
        const subscriptionType = priceId === process.env.STRIPE_PREMIUM_PRICE_ID ? 'Premium' : 'Standard';
        
        // Create user with subscription info
        user = await UserService.createUser({
          email,
          password: tempPassword,
          name: customerName,
          priceId,
          stripeCustomerId,
          subscriptionType: subscriptionType.toLowerCase() as 'standard' | 'premium',
          stripeSubscriptionId: subscription.id,
          stripeSubscriptionStatus: subscription.status,
          stripePriceId: priceId
        });
        
        if (!user) {
          console.error(`Failed to create user for paid subscription: ${email}`);
          return false;
        }
      } else {
        // Update user metadata with string values only
        await UserService.updateUserMetadata(user.id, {
          subscriptionSynced: 'true',
          stripeCustomerId,
          lastSyncedAt: new Date().toISOString()
        });
        
        // Update Stripe customer ID if needed
        if (!user.stripeCustomerId || user.stripeCustomerId !== stripeCustomerId) {
          await UserService.updateUserStripeCustomerId(user.id, stripeCustomerId);
        }
      }
      
      // Sync subscription to Neo4j
      await this.syncSubscription(subscription, user.id);
      
      // Update Stripe customer metadata with Neo4j user ID
      if (typeof customerData !== 'string' && !customerData.deleted) {
        await stripe.customers.update(stripeCustomerId, {
          metadata: {
            ...customerData.metadata,
            neoUserId: user.id
          }
        });
      }
      
      return true;
    } catch (error) {
      console.error('Error in syncPaidUserWithImmediateAccess:', error);
      return false;
    }
  }

  /**
   * Improved Google user registration with immediate subscription access
   */
  static async registerGoogleUserWithSubscription(
    email: string,
    googleUserId: string,
    googleName: string = '',
    stripeCustomerId: string = '',
    subscriptionType: string = '',
    priceId: string = ''
  ): Promise<{
    success: boolean;
    userId?: string;
  }> {
    console.log(`Registering Google user with subscription: ${email}`);
    
    try {
      // Check if user already exists in Neo4j by email
      let user = await UserService.getUserByEmail(email);
      
      // If user exists, just link Google provider and update data
      if (user) {
        console.log(`User already exists in Neo4j: ${user.id}`);
        
        // Link Google provider if not already linked
        await UserService.linkAuthProvider(
          user.id, 
          'google', 
          googleUserId, 
          email
        );
        
        // Update Google metadata
        await UserService.updateUserMetadata(user.id, {
          googleUserId,
          lastGoogleLogin: new Date().toISOString()
        });
        
        // Check for Stripe subscription and sync it to Neo4j if needed
        if (!user.stripeCustomerId && stripeCustomerId) {
          await UserService.updateUserStripeCustomerId(user.id, stripeCustomerId);
        }
        
        return { success: true, userId: user.id };
      }
      
      // User doesn't exist - determine subscription info
      let finalSubscriptionType: 'standard' | 'premium' | undefined = undefined; // Default to undefined (free)
      let finalPriceId = '';
      let finalStripeCustomerId = stripeCustomerId;
      let stripeSubscriptionId = '';
      let stripeSubscriptionStatus = '';
      
      // If no customer ID provided, check Stripe for one
      if (!finalStripeCustomerId) {
        const customers = await stripe.customers.list({ email, limit: 1 });
        
        if (customers.data.length > 0) {
          finalStripeCustomerId = customers.data[0].id;
          
          // Check for active subscriptions
          const subscriptions = await stripe.subscriptions.list({
            customer: finalStripeCustomerId,
            status: 'active',
            limit: 1
          });
          
          if (subscriptions.data.length > 0) {
            const subscription = subscriptions.data[0];
            finalPriceId = (subscription.items.data[0].price as Stripe.Price).id;
            stripeSubscriptionId = subscription.id;
            stripeSubscriptionStatus = subscription.status;
            
            // Set subscription type based on price ID
            finalSubscriptionType = finalPriceId === process.env.STRIPE_PREMIUM_PRICE_ID ? 'premium' : 'standard';
          }
        }
      } else if (subscriptionType && priceId) {
        // Use provided values if they exist
        finalSubscriptionType = subscriptionType.toLowerCase() as 'standard' | 'premium';
        finalPriceId = priceId;
      }
      
      // Create the user
      console.log(`Creating new Neo4j user for Google user: ${email} with subscription: ${finalSubscriptionType || 'free'}`);
      const tempPassword = crypto.randomBytes(16).toString('hex');
      const name = googleName || email.split('@')[0];
      
      user = await UserService.createUser({
        email,
        password: tempPassword,
        name,
        priceId: finalPriceId,
        stripeCustomerId: finalStripeCustomerId,
        stripeSubscriptionId,
        stripeSubscriptionStatus,
        subscriptionType: finalSubscriptionType // Pass as is - undefined means free
      });
      
      if (!user) {
        console.error(`Failed to create Neo4j user for Google: ${email}`);
        return { success: false };
      }
      
      // Link Google auth provider
      await UserService.linkAuthProvider(
        user.id, 
        'google', 
        googleUserId, 
        email
      );
      
      // Store Google data in metadata
      await UserService.updateUserMetadata(user.id, {
        googleUserId,
        lastGoogleLogin: new Date().toISOString()
      });
      
      return { success: true, userId: user.id };
    } catch (error) {
      console.error('Error in registerGoogleUserWithSubscription:', error);
      return { success: false };
    }
  }

  /**
   * Create or update a user from Stripe subscription data
   * This ensures users found in Stripe are registered in Neo4j
   */
  static async createUserFromStripeData(
    email: string, 
    options: {
      checkExisting?: boolean;
      stripeCustomerId?: string;
      stripePriceId?: string;
      subscriptionType?: 'Standard' | 'Premium';
    } = {}
  ): Promise<{success: boolean; userId?: string}> {
    try {
      console.log(`Creating/updating user from Stripe data for ${email}`);
      
      // Default options
      const {
        checkExisting = true,
        stripeCustomerId,
        stripePriceId,
        subscriptionType = 'Standard'
      } = options;
      
      // Check if the user already exists in Neo4j
      const user = checkExisting ? await UserService.getUserByEmail(email) : null;
      
      // If user exists, update subscription details
      if (user) {
        console.log(`User ${email} exists, updating subscription details`);
        
        const subscriptionDetails: any = {
          updatedAt: new Date().toISOString()
        };
        
        if (stripeCustomerId) {
          subscriptionDetails.stripeCustomerId = stripeCustomerId;
        }
        
        if (stripePriceId) {
          subscriptionDetails.stripePriceId = stripePriceId;
        }
        
        if (subscriptionType) {
          subscriptionDetails.subscriptionType = subscriptionType.toLowerCase();
        }
        
        // Update the user with direct subscription properties
        await UserService.updateUserSubscriptionDetails(user.id, subscriptionDetails);
        
        // If we have a customer ID but no subscription data, try to get it from Stripe
        if (stripeCustomerId && !user.stripeSubscriptionId) {
          // Get subscription data from Stripe
          const subscriptions = await stripe.subscriptions.list({
            customer: stripeCustomerId,
            status: 'active',
            limit: 1
          });
          
          if (subscriptions.data.length > 0) {
            const subscription = subscriptions.data[0];
            
            // Update with subscription details
            await UserService.updateUserSubscriptionDetails(user.id, {
              stripeSubscriptionId: subscription.id,
              stripeSubscriptionStatus: subscription.status,
              stripePriceId: (subscription.items.data[0].price as Stripe.Price).id
            });
          }
        }
        
        return { success: true, userId: user.id };
      }
      
      // User doesn't exist, create a new one
      console.log(`User ${email} doesn't exist, creating new user`);
      
      // Generate temporary password
      const tempPassword = crypto.randomBytes(16).toString('hex');
      
      // Get customer info from Stripe if we have a customer ID
      let customerName = email.split('@')[0];
      if (stripeCustomerId) {
        try {
          const customer = await stripe.customers.retrieve(stripeCustomerId);
          if (typeof customer !== 'string' && !customer.deleted && customer.name) {
            customerName = customer.name;
          }
        } catch (err) {
          console.error('Error fetching customer data:', err);
        }
      }
      
      // Get subscription details if we have a customer ID
      let subscriptionId = null;
      let subscriptionStatus = null;
      let actualPriceId = stripePriceId;
      
      if (stripeCustomerId) {
        try {
          const subscriptions = await stripe.subscriptions.list({
            customer: stripeCustomerId,
            status: 'active',
            limit: 1
          });
          
          if (subscriptions.data.length > 0) {
            const subscription = subscriptions.data[0];
            subscriptionId = subscription.id;
            subscriptionStatus = subscription.status;
            
            // If no price ID was provided, get it from the subscription
            if (!actualPriceId) {
              actualPriceId = (subscription.items.data[0].price as Stripe.Price).id;
            }
          }
        } catch (err) {
          console.error('Error fetching subscription data:', err);
        }
      }
      
      // Create the user with direct subscription properties
      const newUser = await UserService.createUser({
        email,
        password: tempPassword,
        name: customerName,
        stripeCustomerId: stripeCustomerId || undefined,
        stripeSubscriptionId: subscriptionId || undefined,
        stripeSubscriptionStatus: subscriptionStatus || undefined,
        stripePriceId: actualPriceId || undefined,
        priceId: actualPriceId || undefined,
        subscriptionType: subscriptionType.toLowerCase() as 'standard' | 'premium' | undefined,
        isGuest: true,
        pendingPasswordSetup: 'true'
      });
      
      if (!newUser) {
        throw new Error(`Failed to create user for ${email}`);
      }
      
      console.log(`Created new user ${newUser.id} for ${email}`);
      return { success: true, userId: newUser.id };
    } catch (error) {
      console.error('Error creating user from Stripe data:', error);
      return { success: false };
    }
  }
} 