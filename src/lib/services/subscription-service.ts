import { getDriver } from '@/lib/neo4j/neo4j-client';
import neo4j, { Record, ManagedTransaction } from 'neo4j-driver';
import {
  createSubscriptionQuery,
  getUserSubscriptionQuery,
  updateSubscriptionStatusQuery,
  updateSubscriptionEndDateQuery,
  cancelSubscriptionQuery,
  getAllUserSubscriptionsQuery,
  getSubscriptionCountsByTypeQuery,
  deleteSubscriptionQuery
} from '@/lib/neo4j/subscription-queries';

// Subscription interface for Neo4j results
interface Subscription {
  id: string;
  type: 'Standard' | 'Premium';
  status: 'active' | 'canceled' | 'trialing' | 'incomplete' | 'past_due';
  startDate: string;
  endDate: string;
  stripeSubscriptionId: string;
  stripePriceId: string;
  stripeCustomerId: string;
  metadata?: {[key: string]: string | number | boolean | null};
  userId?: string; // User ID that owns this subscription
}

interface CreateSubscriptionParams {
  userId: string;
  subscriptionType: 'Standard' | 'Premium';
  status: 'active' | 'canceled' | 'trialing' | 'incomplete' | 'past_due';
  startDate: string; // ISO date string
  endDate?: string; // ISO date string, optional
  stripeSubscriptionId: string;
  stripePriceId: string;
  stripeCustomerId: string;
}

import { stripe } from '@/lib/stripe/stripe';
import type Stripe from 'stripe';

// Server-side cache for subscription validation
// This reduces calls to Stripe API
const validationCache = new Map<string, {
  data: Subscription | null,
  timestamp: number,
  ttl: number
}>();

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

// Limpiar la caché al iniciar para asegurar datos actualizados
validationCache.clear();

export class SubscriptionService {
  /**
   * Retrieve a user's Stripe subscription details
   * @param stripeCustomerId Stripe customer ID
   * @returns Stripe subscription details or null
   */
  static async getStripeSubscription(stripeCustomerId: string): Promise<Stripe.Subscription | null> {
    try {
      const subscriptions = await stripe.subscriptions.list({
        customer: stripeCustomerId,
        status: 'active',
        limit: 1
      });

      return subscriptions.data.length > 0 ? subscriptions.data[0] : null;
    } catch (error) {
      console.error('Error retrieving Stripe subscription:', error);
      return null;
    }
  }

  /**
   * Create a new subscription for a user
   */
  static async createSubscription(params: CreateSubscriptionParams) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(createSubscriptionQuery, {
          userId: params.userId,
          subscriptionType: params.subscriptionType,
          status: params.status,
          startDate: params.startDate,
          endDate: params.endDate ?? null,
          stripeSubscriptionId: params.stripeSubscriptionId,
          stripePriceId: params.stripePriceId,
          stripeCustomerId: params.stripeCustomerId
        })
      );
      
      return result.records[0]?.get('s').properties;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Get a user's current active subscription
   */
  static async getUserSubscription(userId: string) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx: ManagedTransaction) => 
        tx.run(getUserSubscriptionQuery, { userId })
      );
      
      const subscription = result.records[0]?.get('s')?.properties;
      return subscription || null;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Update a subscription's status
   */
  static async updateSubscriptionStatus(subscriptionId: string, status: string) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(updateSubscriptionStatusQuery, { subscriptionId, status })
      );
      
      return result.records[0]?.get('s').properties;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Update a subscription's end date
   */
  static async updateSubscriptionEndDate(subscriptionId: string, endDate: string) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(updateSubscriptionEndDateQuery, { subscriptionId, endDate })
      );
      
      return result.records[0]?.get('s').properties;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Cancel a subscription
   */
  static async cancelSubscription(subscriptionId: string) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(cancelSubscriptionQuery, { subscriptionId })
      );
      
      return result.records[0]?.get('s').properties;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Get all subscriptions for a user
   */
  static async getAllUserSubscriptions(userId: string) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx: ManagedTransaction) => 
        tx.run(getAllUserSubscriptionsQuery, { userId })
      );
      
      return result.records.map((record: Record) => record.get('s').properties);
    } finally {
      await session.close();
    }
  }
  
  /**
   * Get subscription counts by plan type
   */
  static async getSubscriptionCountsByType() {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx: ManagedTransaction) => 
        tx.run(getSubscriptionCountsByTypeQuery)
      );
      
      return result.records.map((record: Record) => ({
        type: record.get('type'),
        count: record.get('count').toNumber()
      }));
    } finally {
      await session.close();
    }
  }
  
  /**
   * Delete a subscription (admin only)
   */
  static async deleteSubscription(subscriptionId: string) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(deleteSubscriptionQuery, { subscriptionId })
      );
      
      return true;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Update the metadata of a subscription
   */
  static async updateSubscriptionMetadata(
    subscriptionId: string, 
    metadata: {[key: string]: string | number | boolean | null}
  ): Promise<boolean> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      // First get the existing metadata
      const getResult = await session.executeRead((tx: ManagedTransaction) => 
        tx.run(
          `
          MATCH (s:Subscription {id: $subscriptionId})
          RETURN s.metadata as metadata
          `,
          { subscriptionId }
        )
      );
      
      // Merge new metadata with existing
      const existingMetadata = getResult.records[0]?.get('metadata') || {};
      const mergedMetadata = {
        ...existingMetadata,
        ...metadata
      };
      
      // Update the metadata
      await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(
          `
          MATCH (s:Subscription {id: $subscriptionId})
          SET s.metadata = $metadata,
              s.updatedAt = datetime()
          RETURN s
          `,
          { 
            subscriptionId,
            metadata: mergedMetadata
          }
        )
      );
      
      return true;
    } catch (error) {
      console.error('Error updating subscription metadata:', error);
      return false;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Get subscription by Stripe subscription ID
   */
  static async getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<Subscription | null> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx: ManagedTransaction) => 
        tx.run(
          `
          MATCH (s:Subscription {stripeSubscriptionId: $stripeSubscriptionId})
          MATCH (u:User)-[:HAS_SUBSCRIPTION]->(s)
          RETURN s, u.id as userId
          `,
          { stripeSubscriptionId }
        )
      );
      
      if (result.records.length === 0) {
        return null;
      }
      
      const subscription = result.records[0].get('s').properties;
      const userId = result.records[0].get('userId');
      
      return {
        ...subscription,
        userId
      };
    } catch (error) {
      console.error('Error getting subscription by Stripe ID:', error);
      return null;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Update the user associated with a subscription
   */
  static async updateSubscriptionUser(subscriptionId: string, newUserId: string): Promise<boolean> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(
          `
          MATCH (s:Subscription {id: $subscriptionId})
          MATCH (s)<-[r:HAS_SUBSCRIPTION]-(:User)
          DELETE r
          WITH s
          MATCH (u:User {id: $userId})
          CREATE (u)-[:HAS_SUBSCRIPTION]->(s)
          RETURN s
          `,
          { 
            subscriptionId,
            userId: newUserId
          }
        )
      );
      
      return true;
    } catch (error) {
      console.error('Error updating subscription user:', error);
      return false;
    } finally {
      await session.close();
    }
  }

  /**
   * Synchronize subscription data from Stripe for a user
   * This ensures validation works in guard components
   */
  static async synchronizeSubscriptionFromStripe(email: string): Promise<Subscription | null> {
    try {
      console.log(`Synchronizing subscription from Stripe for ${email}`);
      
      // Find Stripe customer by email
      const customers = await stripe.subscriptions.list({
        limit: 1,
        expand: ['data.customer']
      });
      
      // Filter to find the customer with matching email
      const subscription = customers.data.find(sub => {
        const customer = sub.customer as Stripe.Customer;
        return customer.email === email;
      });
      
      if (!subscription) {
        console.log(`No subscription found in Stripe for ${email}`);
        return null;
      }
      
      const customer = subscription.customer as Stripe.Customer;
      console.log(`Found Stripe subscription for ${email} with customer ID ${customer.id}`);
      
      // Get or create user in Neo4j
      const { UserService } = await import('./user-service');
      let user = await UserService.getUserByEmail(email);
      
      if (!user) {
        console.log(`User not found in Neo4j for ${email}, creating new user`);
        const crypto = await import('crypto');
        const tempPassword = crypto.randomBytes(16).toString('hex');
        
        user = await UserService.createUser({
          email,
          password: tempPassword,
          name: email.split('@')[0],
          stripeCustomerId: customer.id,
          isGuest: true
        });
        
        if (!user) {
          console.error(`Failed to create user for ${email}`);
          return null;
        }
      } else {
        // Update stripeCustomerId if needed
        if (!user.stripeCustomerId || user.stripeCustomerId !== customer.id) {
          await UserService.updateUserStripeCustomerId(user.id, customer.id);
        }
      }
      
      // Check if subscription already exists in Neo4j
      const existingSubscription = await this.getSubscriptionByStripeId(subscription.id);
      
      if (existingSubscription) {
        console.log(`Found existing subscription in Neo4j for ${email}`);
        return existingSubscription;
      }
      
      // Create new subscription in Neo4j
      const priceId = (subscription.items.data[0].price as Stripe.Price).id;
      
      // Debug log para verificar los IDs
      console.log(`Comparing price IDs - Current: ${priceId}, Premium: ${process.env.STRIPE_PREMIUM_PRICE_ID}`);
      
      // Aseguramos que la comparación se haga correctamente, convirtiendo ambos a string y recortando espacios
      const isPremium = String(priceId).trim() === String(process.env.STRIPE_PREMIUM_PRICE_ID).trim();
      console.log(`Is Premium subscription: ${isPremium}`);
      
      const subscriptionType = isPremium ? 'Premium' : 'Standard';
      
      const newSubscription = await this.createSubscription({
        userId: user.id,
        subscriptionType,
        status: subscription.status as any,
        startDate: new Date(subscription.current_period_start * 1000).toISOString(),
        endDate: new Date(subscription.current_period_end * 1000).toISOString(),
        stripeSubscriptionId: subscription.id,
        stripePriceId: priceId,
        stripeCustomerId: customer.id
      });
      
      return {
        id: newSubscription.id,
        type: subscriptionType,
        status: subscription.status as any,
        startDate: new Date(subscription.current_period_start * 1000).toISOString(),
        endDate: new Date(subscription.current_period_end * 1000).toISOString(),
        stripeSubscriptionId: subscription.id,
        stripePriceId: priceId,
        stripeCustomerId: customer.id,
        userId: user.id
      };
    } catch (error) {
      console.error('Error synchronizing subscription from Stripe:', error);
      return null;
    }
  }

  /**
   * Validate subscription directly from Stripe
   * This provides a fallback when Neo4j data is not available
   */
  static async validateSubscriptionFromStripe(email: string): Promise<Subscription | null> {
    try {
      // Find customer by email
      const customers = await stripe.customers.list({ email, limit: 1 });
      
      if (customers.data.length === 0) {
        console.log(`No Stripe customer found for email: ${email}`);
        return null;
      }
      
      const customer = customers.data[0];
      
      // Get active subscriptions for this customer
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all', // Get all subscriptions and filter below
        limit: 10
      });
      
      // Filter for active or trialing subscriptions
      const activeSubscriptions = subscriptions.data.filter(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );
      
      if (activeSubscriptions.length === 0) {
        console.log(`No active or trialing subscriptions found for customer: ${customer.id}`);
        return null;
      }
      
      const subscription = activeSubscriptions[0];
      const priceId = (subscription.items.data[0].price as Stripe.Price).id;
      
      // Debug log para verificar los IDs
      console.log(`Comparing price IDs - Current: ${priceId}, Premium: ${process.env.STRIPE_PREMIUM_PRICE_ID}`);
      
      // Aseguramos que la comparación se haga correctamente, convirtiendo ambos a string y recortando espacios
      const isPremium = String(priceId).trim() === String(process.env.STRIPE_PREMIUM_PRICE_ID).trim();
      console.log(`Is Premium subscription: ${isPremium}`);
      
      const subscriptionType = isPremium ? 'Premium' : 'Standard';
      
      // Create a Subscription object from Stripe data
      return {
        id: subscription.id,
        type: subscriptionType,
        status: subscription.status as 'active' | 'canceled' | 'trialing' | 'incomplete' | 'past_due',
        startDate: new Date(subscription.current_period_start * 1000).toISOString(),
        endDate: new Date(subscription.current_period_end * 1000).toISOString(),
        stripeSubscriptionId: subscription.id,
        stripePriceId: priceId,
        stripeCustomerId: customer.id
      };
    } catch (error) {
      console.error('Error validating subscription from Stripe:', error);
      return null;
    }
  }
  
  /**
   * Get a combined subscription check (Neo4j + Stripe) with caching
   * This provides the most reliable subscription check
   * Prioritizing Stripe validation over Neo4j validation
   */
  static async getValidatedSubscription(userId: string, email: string): Promise<Subscription | null> {
    try {
      // Check cache first
      const cacheKey = `${userId}:${email}`;
      const cachedData = validationCache.get(cacheKey);
      const now = Date.now();
      
      // Return cached data if valid
      if (cachedData && (now - cachedData.timestamp < cachedData.ttl)) {
        console.log(`Using cached subscription data for ${email}`);
        return cachedData.data;
      }
      
      console.log(`Validating subscription for ${email} (userId: ${userId}), prioritizing Stripe`);
      
      // First try Stripe directly as it's the source of truth
      const stripeSubscription = await this.validateSubscriptionFromStripe(email);
      
      // If found and active in Stripe, return it
      if (stripeSubscription && (stripeSubscription.status === 'active' || stripeSubscription.status === 'trialing')) {
        console.log(`Found valid subscription in Stripe for ${email}: ${stripeSubscription.type}`);
        
        // If we have a valid Stripe subscription, ensure it's synced to Neo4j for future checks
        const { StripeSyncService } = await import('./stripe-sync-service');
        
        // Update user in Neo4j with Stripe subscription data if we have a user ID
        if (userId) {
          // Get user service
          const { UserService } = await import('./user-service');
          
          // Update user with subscription details
          await UserService.updateUserSubscriptionDetails(userId, {
            stripeCustomerId: stripeSubscription.stripeCustomerId,
            stripeSubscriptionId: stripeSubscription.stripeSubscriptionId,
            stripePriceId: stripeSubscription.stripePriceId,
            subscriptionType: stripeSubscription.type.toLowerCase() as 'standard' | 'premium',
            stripeSubscriptionStatus: stripeSubscription.status
          });
          
          console.log(`Updated Neo4j user ${userId} with Stripe subscription data`);
        }
        
        // Cache the result
        validationCache.set(cacheKey, {
          data: stripeSubscription,
          timestamp: now,
          ttl: CACHE_TTL
        });
        
        return stripeSubscription;
      }
      
      // If not found in Stripe, try Neo4j as a fallback
      console.log(`No valid subscription found in Stripe for ${email}, checking Neo4j`);
      const neoSubscription = await this.getUserSubscription(userId);
      
      // Only trust Neo4j data if it's active
      if (neoSubscription && neoSubscription.status === 'active') {
        console.log(`Found valid subscription in Neo4j for ${userId}: ${neoSubscription.type}`);
        
        // Cache the result
        validationCache.set(cacheKey, {
          data: neoSubscription,
          timestamp: now,
          ttl: CACHE_TTL
        });
        
        return neoSubscription;
      }
      
      // No valid subscription found anywhere
      console.log(`No valid subscription found for ${email} in either Stripe or Neo4j`);
      
      // Cache the negative result too (with shorter TTL)
      validationCache.set(cacheKey, {
        data: null,
        timestamp: now,
        ttl: CACHE_TTL / 2 // Shorter TTL for negative results
      });
      
      return null;
    } catch (error) {
      console.error('Error in combined subscription validation:', error);
      return null;
    }
  }

  /**
   * Synchronize a user's subscription data from Stripe
   * This updates the User properties with subscription data without creating Subscription nodes
   */
  static async synchronizeUserSubscriptionFromStripe(
    userId: string, 
    email: string
  ): Promise<Subscription | null> {
    try {
      console.log(`Synchronizing user subscription from Stripe for ${email} (userId: ${userId})`);
      
      // Get subscription data from Stripe
      const stripeSubscription = await this.validateSubscriptionFromStripe(email);
      
      if (!stripeSubscription) {
        console.log(`No active subscription found in Stripe for ${email}`);
        return null;
      }
      
      console.log(`Found Stripe subscription for ${email}:`, stripeSubscription.type);
      
      // Get necessary services
      const { UserService } = await import('./user-service');
      const { StripeSyncService } = await import('./stripe-sync-service');
      
      // Verify the user exists, create if not
      const userCheck = await UserService.getUserByEmail(email);
      
      if (!userCheck) {
        console.log(`User ${email} not found in Neo4j, creating from Stripe data`);
        
        // Create the user with Stripe data
        const createResult = await StripeSyncService.createUserFromStripeData(email, {
          checkExisting: false, // Skip the check since we already know the user doesn't exist
          stripeCustomerId: stripeSubscription.stripeCustomerId,
          stripePriceId: stripeSubscription.stripePriceId,
          subscriptionType: stripeSubscription.type
        });
        
        if (!createResult.success || !createResult.userId) {
          console.error(`Failed to create user for ${email}`);
          return null;
        }
        
        // Use the new user ID
        userId = createResult.userId;
      }
      
      // Update the user record with Stripe subscription details
      await UserService.updateUserSubscriptionDetails(userId, {
        stripeCustomerId: stripeSubscription.stripeCustomerId,
        stripeSubscriptionId: stripeSubscription.stripeSubscriptionId,
        stripePriceId: stripeSubscription.stripePriceId,
        subscriptionType: stripeSubscription.type.toLowerCase() as 'standard' | 'premium',
        stripeSubscriptionStatus: stripeSubscription.status
      });
      
      // Check if there's an existing subscription node (for backward compatibility)
      const existingSubscription = await this.getSubscriptionByStripeId(stripeSubscription.stripeSubscriptionId);
      
      if (existingSubscription) {
        console.log(`Found existing subscription record for ${stripeSubscription.stripeSubscriptionId}, updating it`);
        
        // Update subscription if needed
        if (existingSubscription.status !== stripeSubscription.status) {
          await this.updateSubscriptionStatus(existingSubscription.id, stripeSubscription.status);
        }
        
        // Update end date if needed
        await this.updateSubscriptionEndDate(existingSubscription.id, stripeSubscription.endDate);
        
        // Make sure subscription is linked to the correct user
        if (existingSubscription.userId !== userId) {
          await this.updateSubscriptionUser(existingSubscription.id, userId);
        }
        
        return { ...existingSubscription, userId };
      }
      
      // Return the subscription data based on user properties (no node creation)
      console.log(`Returning subscription data without creating node for user ${userId}`);
      return {
        id: stripeSubscription.stripeSubscriptionId,
        type: stripeSubscription.type,
        status: stripeSubscription.status,
        startDate: stripeSubscription.startDate,
        endDate: stripeSubscription.endDate,
        stripeSubscriptionId: stripeSubscription.stripeSubscriptionId,
        stripePriceId: stripeSubscription.stripePriceId,
        stripeCustomerId: stripeSubscription.stripeCustomerId,
        userId
      };
    } catch (error) {
      console.error('Error in synchronizeUserSubscriptionFromStripe:', error);
      return null;
    }
  }
} 