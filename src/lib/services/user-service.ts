import { getDriver } from '@/lib/neo4j/neo4j-client';
import { hash } from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import type { ManagedTransaction } from 'neo4j-driver';

export interface CreateUserParams {
  email: string;
  password: string;
  priceId?: string;
  name?: string;
  isGuest?: boolean;
  stripeCustomerId?: string;
  source?: string;
  subscriptionType?: 'standard' | 'premium' | null;
  pendingPasswordSetup?: string;
  stripeSubscriptionId?: string;
  stripeSubscriptionStatus?: string;
  stripePriceId?: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  createdAt: string;
  isGuest?: boolean;
  stripeCustomerId?: string;
  source?: string;
  subscriptionType?: 'standard' | 'premium' | null;
  pendingPasswordSetup?: string;
  stripeSubscriptionId?: string;
  stripeSubscriptionStatus?: string;
  stripePriceId?: string;
}

export class UserService {
  /**
   * Create a new user
   */
  static async createUser(params: CreateUserParams): Promise<User | null> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      // Check if user with this email already exists
      const existingUser = await this.getUserByEmail(params.email);
      
      if (existingUser) {
        // If the user was created from a guest checkout but hasn't set up their password
        if (existingUser.isGuest && existingUser.pendingPasswordSetup === 'true') {
          console.log(`Found existing guest user with email ${params.email}, returning existing user`);
          // Just return the existing user
          return existingUser;
        }
        
        // Otherwise, email is already taken
        throw new Error(`User with email ${params.email} already exists`);
      }
      
      // Hash the password
      const hashedPassword = await hash(params.password, 10);
      
      // Create the user
      const result = await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(
          `
          CREATE (u:User {
            id: $id,
            email: $email,
            password: $hashedPassword,
            name: $name,
            isGuest: $isGuest,
            stripeCustomerId: $stripeCustomerId,
            source: $source,
            priceId: $priceId,
            subscriptionType: $subscriptionType,
            pendingPasswordSetup: $pendingPasswordSetup,
            createdAt: datetime(),
            updatedAt: datetime(),
            stripeSubscriptionId: $stripeSubscriptionId,
            stripeSubscriptionStatus: $stripeSubscriptionStatus,
            stripePriceId: $stripePriceId
          })
          RETURN u
          `,
          {
            id: uuidv4(),
            email: params.email,
            hashedPassword,
            name: params.name ?? params.email.split('@')[0],
            isGuest: params.isGuest ?? false,
            stripeCustomerId: params.stripeCustomerId ?? null,
            source: params.source ?? null,
            subscriptionType: params.subscriptionType ?? (params.priceId === process.env.STRIPE_PREMIUM_PRICE_ID ? 'premium' : 'standard'),
            pendingPasswordSetup: params.pendingPasswordSetup ?? null,
            stripeSubscriptionId: params.stripeSubscriptionId ?? null,
            stripeSubscriptionStatus: params.stripeSubscriptionStatus ?? null,
            stripePriceId: params.stripePriceId ?? params.priceId ?? null,
            priceId: params.priceId ?? null
          }
        )
      );
      
      if (!result.records || result.records.length === 0) {
        console.error('No records returned when creating user');
        return null;
      }
      
      const user = result.records[0]?.get('u')?.properties;
      
      if (!user) {
        console.error('User properties not found in record');
        return null;
      }
      
      console.log(`Successfully created user with ID: ${user.id}, email: ${user.email}`);
      
      // Convert to the expected type
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        createdAt: user.createdAt.toString(),
        isGuest: user.isGuest,
        stripeCustomerId: user.stripeCustomerId,
        source: user.source,
        subscriptionType: user.subscriptionType,
        pendingPasswordSetup: user.pendingPasswordSetup,
        stripeSubscriptionId: user.stripeSubscriptionId,
        stripeSubscriptionStatus: user.stripeSubscriptionStatus,
        stripePriceId: user.stripePriceId
      };
    } catch (error) {
      console.error('Error creating user:', error);
      // Re-throw the error so the calling function can handle it
      throw error;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Get a user by email
   * @throws {Error} If multiple users are found with the same email
   */
  static async getUserByEmail(email: string): Promise<User | null> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx: ManagedTransaction) => 
        tx.run(
          `
          MATCH (u:User {email: $email})
          RETURN u
          `,
          { email }
        )
      );
      
      // Check for multiple users with the same email
      if (result.records.length > 1) {
        throw new Error(`Multiple users found with email: ${email}`);
      }
      
      // No users found
      if (result.records.length === 0) {
        return null;
      }
      
      const user = result.records[0].get('u')?.properties;
      
      if (!user) {
        return null;
      }
      
      // Convert to the expected type, omitting metadata
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        createdAt: user.createdAt.toString(),
        isGuest: user.isGuest,
        stripeCustomerId: user.stripeCustomerId,
        source: user.source,
        subscriptionType: user.subscriptionType,
        pendingPasswordSetup: user.pendingPasswordSetup,
        stripeSubscriptionId: user.stripeSubscriptionId,
        stripeSubscriptionStatus: user.stripeSubscriptionStatus,
        stripePriceId: user.stripePriceId
      };
    } catch (error) {
      console.error(`Error getting user by email (${email}):`, error);
      throw error; // Re-throw to allow caller to handle specific errors
    } finally {
      await session.close();
    }
  }
  
  /**
   * Update user password
   */
  static async updateUserPassword(userId: string, newPassword: string): Promise<boolean> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      // Hash the password
      const hashedPassword = await hash(newPassword, 10);
      
      // Update the user
      const result = await session.executeWrite((tx: ManagedTransaction) => 
        tx.run(
          `
          MATCH (u:User {id: $userId})
          SET u.password = $hashedPassword,
              u.isGuest = false,
              u.metadata = CASE
                WHEN u.metadata IS NULL THEN {}
                ELSE apoc.map.removeKey(u.metadata, 'pendingPasswordSetup')
              END,
              u.updatedAt = datetime()
          RETURN u
          `,
          {
            userId,
            hashedPassword
          }
        )
      );
      
      return result.records.length > 0;
    } catch (error) {
      console.error('Error updating user password:', error);
      return false;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Get user by checkout session ID
   */
  static async getUserByCheckoutSessionId(sessionId: string): Promise<User | null> {
    console.log(`Searching for user with checkout session ID: ${sessionId}`);
    
    const driver = getDriver();
    const session = driver.session();
    
    try {
      // First try direct match in metadata field
      let result = await session.executeRead((tx: ManagedTransaction) => 
        tx.run(
          `
          MATCH (u:User)
          WHERE u.metadata.checkoutSessionId = $sessionId
          RETURN u
          `,
          { sessionId }
        )
      );
      
      // If no results, try using a case-insensitive string comparison
      if (result.records.length === 0) {
        console.log('No exact match found, trying case-insensitive search');
        result = await session.executeRead((tx: ManagedTransaction) => 
          tx.run(
            `
            MATCH (u:User)
            WHERE toLower(u.metadata.checkoutSessionId) = toLower($sessionId)
            RETURN u
            `,
            { sessionId }
          )
        );
      }
      
      const user = result.records[0]?.get('u')?.properties;
      
      if (!user) {
        console.log(`No user found with checkout session ID: ${sessionId}`);
        return null;
      }
      
      console.log(`Found user with ID: ${user.id} for checkout session ID: ${sessionId}`);
      
      // Convert to the expected type
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        createdAt: user.createdAt.toString(),
        isGuest: user.isGuest,
        stripeCustomerId: user.stripeCustomerId,
        source: user.source,
        subscriptionType: user.subscriptionType,
        pendingPasswordSetup: user.pendingPasswordSetup,
        stripeSubscriptionId: user.stripeSubscriptionId,
        stripeSubscriptionStatus: user.stripeSubscriptionStatus,
        stripePriceId: user.stripePriceId
      };
    } catch (error) {
      console.error('Error getting user by checkout session ID:', error);
      return null;
    } finally {
      await session.close();
    }
  }
  
  /**
   * Get a user by ID
   */
  static async getUserById(userId: string): Promise<User | null> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx: ManagedTransaction) => 
        tx.run(
          `
          MATCH (u:User {id: $userId})
          RETURN u
          `,
          { userId }
        )
      );
      
      const user = result.records[0]?.get('u')?.properties;
      
      if (!user) {
        return null;
      }
      
      // Convert to the expected type
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        createdAt: user.createdAt.toString(),
        isGuest: user.isGuest,
        stripeCustomerId: user.stripeCustomerId,
        source: user.source,
        subscriptionType: user.subscriptionType,
        pendingPasswordSetup: user.pendingPasswordSetup,
        stripeSubscriptionId: user.stripeSubscriptionId,
        stripeSubscriptionStatus: user.stripeSubscriptionStatus,
        stripePriceId: user.stripePriceId
      };
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    } finally {
      await session.close();
    }
  }

  /**
   * Get a user by metadata value
   */
  static async getUserByMetadata(key: string, value: string): Promise<User | null> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx) => 
        tx.run(
          `MATCH (u:User)
           WHERE u.metadata.${key} = $value
           RETURN u`,
          { value }
        )
      );
      
      const record = result.records[0];
      if (!record) return null;
      
      const user = record.get('u').properties;
      return {
        id: user.id,
        name: user.name,
        email: user.email,
        createdAt: user.createdAt.toString(),
        isGuest: user.isGuest,
        stripeCustomerId: user.stripeCustomerId,
        source: user.source,
        subscriptionType: user.subscriptionType,
        pendingPasswordSetup: user.pendingPasswordSetup,
        stripeSubscriptionId: user.stripeSubscriptionId,
        stripeSubscriptionStatus: user.stripeSubscriptionStatus,
        stripePriceId: user.stripePriceId
      };
    } catch (error) {
      console.error('Error finding user by metadata:', error);
      return null;
    } finally {
      await session.close();
    }
  }

  /**
   * Update user metadata (Using updateUserSubscriptionDetails for properties)
   * @deprecated Use updateUserSubscriptionDetails instead for subscription properties
   */
  static async updateUserMetadata(userId: string, metadata: Record<string, any>): Promise<boolean> {
    console.log(`Updating user metadata for user ${userId}`);
    
    try {
      // Extract any subscription-related fields to direct properties
      const subscriptionDetails: Record<string, any> = {};
      const otherProperties: Record<string, any> = {};
      
      // Separate subscription properties from general metadata
      for (const [key, value] of Object.entries(metadata)) {
        if (['stripeCustomerId', 'stripeSubscriptionId', 'stripePriceId', 
             'subscriptionType', 'subscriptionSynced', 'lastSyncedAt'].includes(key)) {
          subscriptionDetails[key] = value;
        } else {
          otherProperties[key] = value;
        }
      }
      
      // Update subscription details if present using dedicated method
      if (Object.keys(subscriptionDetails).length > 0) {
        try {
          const result = await this.updateUserSubscriptionDetails(userId, subscriptionDetails as any);
          if (!result) {
            console.warn(`Failed to update subscription details for user ${userId}`);
          }
        } catch (error) {
          console.error(`Error updating subscription details for user ${userId}:`, error);
          // Continue with other properties even if this fails
        }
      }
      
      // Only try to update other properties if there are any
      if (Object.keys(otherProperties).length > 0) {
        try {
          // For direct property updates, use a simpler query that doesn't use metadata
          const driver = getDriver();
          const session = driver.session();
          
          try {
            const query = `
              MATCH (u:User {id: $userId})
              SET u.updatedAt = datetime()
              RETURN u
            `;
            
            await session.executeWrite((tx) => tx.run(query, { userId }));
          } finally {
            await session.close();
          }
        } catch (error) {
          console.error(`Error updating other properties for user ${userId}:`, error);
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error in updateUserMetadata:', error);
      return false;
    }
  }

  /**
   * Link a Neo4j user account with an OAuth provider
   */
  static async linkAuthProvider(
    userId: string, 
    provider: string, 
    providerId: string, 
    providerEmail: string
  ): Promise<boolean> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      // Create or update the auth provider link
      await session.executeWrite((tx) => 
        tx.run(
          `MATCH (u:User {id: $userId})
           MERGE (a:AuthProvider {provider: $provider, providerId: $providerId})
           ON CREATE SET a.email = $email, a.createdAt = datetime()
           ON MATCH SET a.email = $email, a.updatedAt = datetime()
           MERGE (u)-[:HAS_AUTH_PROVIDER]->(a)
           RETURN a`,
          { 
            userId,
            provider, 
            providerId,
            email: providerEmail
          }
        )
      );
      
      // Update user metadata to indicate merged account
      await this.updateUserMetadata(userId, {
        linkedWith: provider,
        linkedProviderId: providerId,
        linkedAt: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      console.error('Error linking auth provider:', error);
      return false;
    } finally {
      await session.close();
    }
  }

  /**
   * Get user by Stripe customer ID
   */
  static async getUserByStripeCustomerId(stripeCustomerId: string) {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      const result = await session.executeRead((tx) =>
        tx.run(
          `MATCH (u:User {stripeCustomerId: $stripeCustomerId})
           RETURN u.id AS id, u.email AS email, u.name AS name,
                  u.stripeCustomerId AS stripeCustomerId, u.role AS role`,
          { stripeCustomerId }
        )
      );
      
      if (result.records.length === 0) {
        return null;
      }
      
      return {
        id: result.records[0].get('id'),
        email: result.records[0].get('email'),
        name: result.records[0].get('name'),
        stripeCustomerId: result.records[0].get('stripeCustomerId'),
        role: result.records[0].get('role')
      };
    } catch (error) {
      console.error('Error getting user by Stripe customer ID:', error);
      return null;
    } finally {
      await session.close();
    }
  }

  /**
   * Update user's Stripe customer ID
   */
  static async updateUserStripeCustomerId(userId: string, stripeCustomerId: string): Promise<boolean> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      await session.executeWrite((tx) =>
        tx.run(
          `MATCH (u:User {id: $userId})
           SET u.stripeCustomerId = $stripeCustomerId,
               u.updatedAt = datetime()
           RETURN u`,
          { userId, stripeCustomerId }
        )
      );
      
      return true;
    } catch (error) {
      console.error('Error updating user Stripe customer ID:', error);
      return false;
    } finally {
      await session.close();
    }
  }

  /**
   * Update user's subscription details
   */
  static async updateUserSubscriptionDetails(
    userId: string, 
    details: {
      stripeCustomerId?: string;
      stripeSubscriptionId?: string;
      stripePriceId?: string;
      subscriptionType?: 'standard' | 'premium' | null;
      stripeSubscriptionStatus?: string;
    }
  ): Promise<boolean> {
    const driver = getDriver();
    const session = driver.session();
    
    try {
      // Build the update query dynamically based on provided details
      let setClause = 'SET u.updatedAt = datetime()';
      const params: Record<string, any> = { userId };
      
      if (details.stripeCustomerId) {
        setClause += ', u.stripeCustomerId = $stripeCustomerId';
        params.stripeCustomerId = details.stripeCustomerId;
      }
      
      if (details.stripeSubscriptionId) {
        setClause += ', u.stripeSubscriptionId = $stripeSubscriptionId';
        params.stripeSubscriptionId = details.stripeSubscriptionId;
      }
      
      if (details.stripePriceId) {
        setClause += ', u.stripePriceId = $stripePriceId';
        params.stripePriceId = details.stripePriceId;
      }
      
      if (details.subscriptionType) {
        setClause += ', u.subscriptionType = $subscriptionType';
        params.subscriptionType = details.subscriptionType;
      }
      
      if (details.stripeSubscriptionStatus) {
        setClause += ', u.stripeSubscriptionStatus = $stripeSubscriptionStatus';
        params.stripeSubscriptionStatus = details.stripeSubscriptionStatus;
      }
      
      // Execute the update
      await session.executeWrite((tx) => 
        tx.run(
          `MATCH (u:User {id: $userId})
           ${setClause}
           RETURN u`,
          params
        )
      );
      
      return true;
    } catch (error) {
      console.error('Error updating user subscription details:', error);
      return false;
    } finally {
      await session.close();
    }
  }
} 