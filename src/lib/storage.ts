import { createClient } from '@supabase/supabase-js';
import { 
  uploadToS3, 
  getSignedS3Url, 
  deleteFromS3, 
  listS3Files,
  FileType as S3FileType 
} from './s3';

// Using environment variables for security - these should be in your .env.local file
// DO NOT hardcode sensitive credentials in your code
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const s3Endpoint = process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://gntleknxbxbmzwizidgv.supabase.co/storage/v1/s3';
const s3AccessKeyId = process.env.NEXT_PUBLIC_S3_ACCESS_KEY_ID || 'af73fb512d54487ad6cbefc17df357b7';
const s3SecretAccessKey = process.env.NEXT_PUBLIC_S3_SECRET_ACCESS_KEY || '0a0de5e4f9fc5752870e965e3d5c18f9c369988f6b50561bc8bf009daaceb51c';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// File types we support
export type FileType = 'audio' | 'image' | 'video' | 'podcast' | 'voice';

// Storage providers we support
export type StorageProvider = 'supabase' | 's3' | 'aws-s3';

// Default storage provider
const defaultStorageProvider: StorageProvider = 'aws-s3';

// Bucket map - maps file types to their respective buckets
const bucketMap: Record<FileType, string> = {
  audio: 'audio-files',
  podcast: 'podcasts',
  voice: 'voice-samples',
  image: 'images',
  video: 'videos'
};

// Get the bucket name for a file type
const getBucketName = (type: FileType): string => {
  return bucketMap[type];
};

// Function to directly upload to Supabase S3-compatible API
const uploadToS3Api = async (
  file: File,
  bucket: string,
  filePath: string
): Promise<{ url: string; path: string } | null> => {
  try {
    // Using the Supabase storage API directly 
    const url = `${s3Endpoint}/${bucket}/${filePath}`;
    
    const headers = new Headers();
    headers.append('Content-Type', file.type);
    headers.append('x-amz-acl', 'public-read');
    headers.append('Authorization', `Bearer ${supabaseAnonKey}`);
    
    const response = await fetch(url, {
      method: 'PUT',
      headers: headers,
      body: file
    });
    
    if (!response.ok) {
      throw new Error(`Failed to upload file: ${response.statusText}`);
    }
    
    // Generate public URL for the file
    const publicUrl = `${s3Endpoint}/${bucket}/${filePath}`;
    
    return {
      url: publicUrl,
      path: filePath
    };
  } catch (error) {
    console.error('S3 upload error:', error);
    return null;
  }
};

// Function to upload a file to storage (either Supabase or S3)
export const uploadFile = async (
  file: File,
  type: FileType,
  userId: string,
  folder?: string,
  customFileName?: string,
  provider: StorageProvider = defaultStorageProvider
): Promise<{ url: string; path: string } | null> => {
  try {
    // Generate a unique filename if not provided
    const fileName = customFileName || `${Date.now()}-${file.name}`;
    const filePath = folder ? `${folder}/${fileName}` : fileName;
    
    if (provider === 'aws-s3') {
      return await uploadToAwsS3(file, filePath, file.type, userId, type, folder);
    } else if (provider === 's3') {
      return await uploadToS3Api(file, getBucketName(type), filePath);
    } else {
      return await uploadToSupabase(file, getBucketName(type), filePath);
    }
  } catch (error) {
    console.error('Error uploading file:', error);
    return null;
  }
};

// Extended downloadFile function
export const downloadFile = async (
  path: string,
  type: FileType,
  provider: StorageProvider = defaultStorageProvider
): Promise<string | null> => {
  try {
    if (provider === 'aws-s3') {
      return await getSignedS3Url(path, type as S3FileType);
    } else if (provider === 's3') {
      // Existing S3 code...
      // ...existing code...
      return null;
    } else {
      // Existing Supabase code...
      // ...existing code...
      return null;
    }
  } catch (error) {
    console.error('Error downloading file:', error);
    return null;
  }
};

// Extended listFiles function
export const listFiles = async (
  userId: string,
  type: FileType,
  folder?: string,
  provider: StorageProvider = defaultStorageProvider
): Promise<{ name: string; url: string; size: number; createdAt: string }[] | null> => {
  try {
    if (provider === 'aws-s3') {
      const files = await listS3Files(userId, type as S3FileType, folder);
      
      const results = await Promise.all(
        files.map(async file => {
          const url = await getSignedS3Url(file.key, type as S3FileType);
          const name = file.key.split('/').pop() || '';
          
          return {
            name,
            url,
            size: file.size,
            createdAt: file.lastModified.toISOString(),
          };
        })
      );
      
      return results;
    } else if (provider === 's3') {
      // Existing S3 code...
      // ...existing code...
      return null;
    } else {
      // Existing Supabase code...
      // ...existing code...
      return null;
    }
  } catch (error) {
    console.error('Error listing files:', error);
    return null;
  }
};

// Extended deleteFile function
export const deleteFile = async (
  path: string,
  type: FileType,
  provider: StorageProvider = defaultStorageProvider
): Promise<boolean> => {
  try {
    if (provider === 'aws-s3') {
      return await deleteFromS3(path, type as S3FileType);
    } else if (provider === 's3') {
      // Existing S3 code...
      // ...existing code...
      return false;
    } else {
      // Existing Supabase code...
      // ...existing code...
      return false;
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

// Add AWS S3 integration
export const uploadToAwsS3 = async (
  file: File,
  filePath: string,
  fileType: string,
  userId: string,
  type: FileType,
  folder?: string
): Promise<{ url: string; path: string } | null> => {
  try {
    // Convert the File to an ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Use our S3 service to upload the file
    const result = await uploadToS3(
      buffer,
      file.name,
      file.type,
      userId,
      type as S3FileType,
      folder
    );
    
    return {
      url: result.url,
      path: result.key
    };
  } catch (error) {
    console.error('Error uploading to AWS S3:', error);
    return null;
  }
};

// Function to upload a file to Supabase
const uploadToSupabase = async (
  file: File,
  bucket: string,
  filePath: string
): Promise<{ url: string; path: string } | null> => {
  try {
    // Use the Supabase storage API to upload the file
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) {
      console.error('Error uploading file to Supabase:', error);
      throw error;
    }

    // Generate a public URL for the file
    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(data?.path || '');

    return {
      url: publicUrl,
      path: data?.path || ''
    };
  } catch (error) {
    console.error('Error uploading file to Supabase:', error);
    return null;
  }
}; 