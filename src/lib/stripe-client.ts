// Client-safe subscription types (no server code)
export const SUBSCRIPTION_TYPES = {
  STANDARD: 'standard',
  PREMIUM: 'premium',
};

// Initialize empty price IDs (will be populated via API call)
export const STRIPE_PRICE_IDS = {
  STANDARD: '',
  PREMIUM: ''
};

// Function to fetch price IDs from the server
export async function fetchPriceIDs(): Promise<boolean> {
  try {
    const response = await fetch('/api/pricing');
    if (!response.ok) {
      throw new Error('Failed to fetch pricing information');
    }
    
    const data = await response.json();
    
    // Update the price IDs
    STRIPE_PRICE_IDS.STANDARD = data.standard;
    STRIPE_PRICE_IDS.PREMIUM = data.premium;
    
    return true;
  } catch (error) {
    console.error('Error loading price IDs:', error);
    return false;
  }
}

// Subscription plan definitions for the UI
export const SUBSCRIPTION_PLANS = [
  {
    id: SUBSCRIPTION_TYPES.STANDARD, // Use the plan type as the ID for consistent identification
    priceId: '', // Will be populated at runtime
    name: 'Standard Plan',
    price: '$60.00/yearly',
    planType: SUBSCRIPTION_TYPES.STANDARD,
    features: [
      'AI Image Generation',
      'Speech to Text Conversion',
      'Music Generation',
      'AI Text Assistant',
      'Get AI writing assistance',
    ],
  },
  {
    id: SUBSCRIPTION_TYPES.PREMIUM, // Use the plan type as the ID for consistent identification
    priceId: '', // Will be populated at runtime
    name: 'Premium Plan',
    price: '$180.00/yearly',
    planType: SUBSCRIPTION_TYPES.PREMIUM,
    features: [
      'Everything in Standard',
      'Video Generation',
      'Podcast Creation',
      'Priority Processing',
      'Advanced Settings',
      'Higher Resolution Output',
      'Longer Content Generation'
    ],
    recommended: true,
  }
];

// Function to update plan priceIds
export function updatePlanPriceIds() {
  SUBSCRIPTION_PLANS.forEach(plan => {
    if (plan.planType === SUBSCRIPTION_TYPES.STANDARD) {
      plan.priceId = STRIPE_PRICE_IDS.STANDARD;
    } else if (plan.planType === SUBSCRIPTION_TYPES.PREMIUM) {
      plan.priceId = STRIPE_PRICE_IDS.PREMIUM;
    }
  });
}

// Subscription status types
export type SubscriptionStatus = 
  | 'active'
  | 'canceled'
  | 'incomplete'
  | 'incomplete_expired'
  | 'past_due'
  | 'trialing'
  | 'unpaid';

// Subscription interface
export interface Subscription {
  id: string;
  type: string;
  status: string;
  startDate?: string;
  currentPeriodEnd: string;
  stripeCustomerId: string;
  stripeSubscriptionId: string;
  stripePriceId?: string;
} 