'use server';

import <PERSON><PERSON> from 'stripe';

// Initialize <PERSON><PERSON> with the secret key - SERVER SIDE ONLY
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

// Function to construct a webhook event
export async function constructEvent(payload: string, signature: string) {
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;
  
  try {
    return stripe.webhooks.constructEvent(
      payload,
      signature,
      webhookSecret
    );
  } catch (err) {
    const error = err as Error;
    console.error(`Webhook signature verification failed: ${error.message}`);
    throw error;
  }
}

// Create a setup intent for saving payment method
export async function createSetupIntent(customerId: string) {
  return await stripe.setupIntents.create({
    customer: customerId,
    payment_method_types: ['card'],
  });
}

// Create a payment intent for one-time payments
export async function createPaymentIntent(
  amount: number,
  currency: string,
  customerId: string,
  metadata: Record<string, string> = {}
) {
  return await stripe.paymentIntents.create({
    amount,
    currency,
    customer: customerId,
    metadata,
  });
}

// Get an instance of the Stripe client
export async function getStripe() {
  return stripe;
}

// Create a subscription with a payment method
export async function createSubscription(
  customerId: string,
  priceId: string,
  paymentMethodId: string,
  metadata: Record<string, string> = {}
) {
  // Attach the payment method to the customer
  await stripe.paymentMethods.attach(paymentMethodId, {
    customer: customerId,
  });
  
  // Set it as the default payment method
  await stripe.customers.update(customerId, {
    invoice_settings: {
      default_payment_method: paymentMethodId,
    },
  });
  
  // Create the subscription
  return await stripe.subscriptions.create({
    customer: customerId,
    items: [{ price: priceId }],
    expand: ['latest_invoice.payment_intent'],
    metadata,
  });
}

// Retrieve subscription details
export async function getSubscription(subscriptionId: string) {
  return await stripe.subscriptions.retrieve(subscriptionId);
}

// Cancel a subscription
export async function cancelSubscription(subscriptionId: string) {
  return await stripe.subscriptions.cancel(subscriptionId);
}

// Get all payment methods for a customer
export async function getPaymentMethods(customerId: string) {
  return await stripe.paymentMethods.list({
    customer: customerId,
    type: 'card',
  });
}