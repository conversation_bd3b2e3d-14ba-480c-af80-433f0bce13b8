import Stripe from 'stripe';

let stripeClient: Stripe | null = null;

// Initialize a minimal Stripe client suitable for Edge Runtime
function getStripeClient() {
  if (!stripeClient) {
    stripeClient = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
      apiVersion: '2025-02-24.acacia', // Use the current API version
      typescript: true,
      appInfo: {
        name: 'AstroStudio AI',
        version: '1.0.0',
      },
    });
  }
  return stripeClient;
}

const client = getStripeClient();
export default client; 