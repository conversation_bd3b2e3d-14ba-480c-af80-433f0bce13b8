import Stripe from 'stripe';

// Price IDs for the subscription plans
export const STANDARD_PRICE_ID = process.env.STRIPE_STANDARD_PRICE_ID;
export const PREMIUM_PRICE_ID = process.env.STRIPE_PREMIUM_PRICE_ID;

// Validate Stripe key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
if (!stripeSecretKey) {
  console.warn('WARNING: STRIPE_SECRET_KEY is not set. Payment features will not work properly.');
}

// Initialize Stripe with proper error handling
export const stripe = new Stripe(stripeSecretKey || 'dummy_key_for_initialization', {
  apiVersion: '2025-02-24.acacia',
  typescript: true,
  appInfo: {
    name: 'AstroStudio AI',
    version: '1.0.0',
  },
}); 