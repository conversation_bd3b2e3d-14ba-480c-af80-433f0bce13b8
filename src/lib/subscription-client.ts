/**
 * Client-side subscription validation utilities
 * Provides efficient caching to reduce API calls
 */

// Cache for subscription validation results
const validationCache = new Map<string, {
  data: any;
  timestamp: number;
  hasAccess: boolean;
}>();

// Cache TTL in milliseconds (15 minutes)
const CACHE_TTL = 15 * 60 * 1000;

/**
 * Check if the current user has access to a specific feature
 * Uses caching to reduce API calls
 */
export async function checkFeatureAccess(feature: string): Promise<boolean> {
  try {
    // Check cache first
    const cacheKey = `feature:${feature}`;
    const cachedData = validationCache.get(cacheKey);
    const now = Date.now();
    
    if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
      return cachedData.hasAccess;
    }
    
    // Make API request to check access
    const response = await fetch('/api/check-access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ feature }),
      credentials: 'include',
    });
    
    if (!response.ok) {
      // On error, allow access by default to prevent blocking legitimate users
      console.error('Error checking feature access:', response.status);
      return true;
    }
    
    const data = await response.json();
    
    // Cache the result
    validationCache.set(cacheKey, {
      data: data.subscription,
      timestamp: now,
      hasAccess: data.hasAccess
    });
    
    return data.hasAccess;
  } catch (error) {
    console.error('Error checking feature access:', error);
    // On error, allow access by default to prevent blocking legitimate users
    return true;
  }
}

/**
 * Get the current user's subscription details
 * Uses caching to reduce API calls
 */
export async function getSubscriptionDetails() {
  try {
    // Check cache first
    const cacheKey = 'subscription:details';
    const cachedData = validationCache.get(cacheKey);
    const now = Date.now();
    
    if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
      return cachedData.data;
    }
    
    // Make API request to get subscription details
    const response = await fetch('/api/check-access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
      credentials: 'include',
    });
    
    if (!response.ok) {
      console.error('Error getting subscription details:', response.status);
      return null;
    }
    
    const data = await response.json();
    
    // Cache the result
    validationCache.set(cacheKey, {
      data: data.subscription,
      timestamp: now,
      hasAccess: data.isSubscribed
    });
    
    return data.subscription;
  } catch (error) {
    console.error('Error getting subscription details:', error);
    return null;
  }
}

/**
 * Clear the subscription validation cache
 * Call this when the user logs out or when subscription status changes
 */
export function clearSubscriptionCache() {
  validationCache.clear();
}

/**
 * Check if the current user is subscribed (has any active subscription)
 * Uses caching to reduce API calls
 */
export async function isUserSubscribed(): Promise<boolean> {
  try {
    // Check cache first
    const cacheKey = 'subscription:status';
    const cachedData = validationCache.get(cacheKey);
    const now = Date.now();
    
    if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
      return cachedData.hasAccess;
    }
    
    // Make API request to check subscription status
    const response = await fetch('/api/check-access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
      credentials: 'include',
    });
    
    if (!response.ok) {
      console.error('Error checking subscription status:', response.status);
      return false;
    }
    
    const data = await response.json();
    
    // Cache the result
    validationCache.set(cacheKey, {
      data: data.subscription,
      timestamp: now,
      hasAccess: data.isSubscribed
    });
    
    return data.isSubscribed;
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return false;
  }
} 