import { cookies } from 'next/headers';
import { getUserSubscription } from './neo4j';

// Cookie name constants
const SUBSCRIPTION_COOKIE = 'subscription_status';
const SUBSCRIPTION_TYPE_COOKIE = 'subscription_type';

/**
 * Check subscription status from cookies on the server-side
 * If cookies don't exist, it will fetch from the database
 * @param userId User ID to check subscription for
 */
export async function checkSubscriptionStatus(userId: string): Promise<{
  isSubscribed: boolean;
  subscriptionType: string;
}> {
  try {
    // Try to get subscription from cookies first
    const cookieStore = await cookies();
    const statusCookie = cookieStore.get(SUBSCRIPTION_COOKIE);
    const typeCookie = cookieStore.get(SUBSCRIPTION_TYPE_COOKIE);
    
    // If both cookies exist, use them
    if (statusCookie && typeCookie) {
      return {
        isSubscribed: statusCookie.value === 'active',
        subscriptionType: typeCookie.value
      };
    }
    
    // Otherwise, fetch from database
    const subscription = await getUserSubscription(userId);
    
    if (!subscription) {
      return {
        isSubscribed: false,
        subscriptionType: 'free'
      };
    }
    
    // Check if subscription is active
    const isActive = (
      subscription.status === 'active' || 
      subscription.status === 'trialing'
    ) && new Date(subscription.endDate) > new Date();
    
    return {
      isSubscribed: isActive,
      subscriptionType: subscription.type || 'free'
    };
  } catch (error) {
    console.error('Error checking subscription from cookies:', error);
    // Default to no subscription on error
    return {
      isSubscribed: false,
      subscriptionType: 'free'
    };
  }
}

/**
 * Check if a user has access to a specific feature based on their subscription
 * @param userId User ID to check
 * @param feature Feature to check access for
 */
export async function checkFeatureAccess(userId: string, feature: string): Promise<boolean> {
  const { isSubscribed, subscriptionType } = await checkSubscriptionStatus(userId);
  
  if (!isSubscribed) {
    return false;
  }
  
  // Basic access logic based on subscription type
  if (subscriptionType === 'Premium') {
    return true; // Premium has access to everything
  } else if (subscriptionType === 'Standard') {
    // Standard has access to most features
    // Could implement more specific logic based on feature
    return true;
  }
  
  // Free tier or unknown subscription type has no access
  return false;
} 