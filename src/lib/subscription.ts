'use server';

import { executeQuery } from './neo4j';
import { 
  createSubscription, 
  cancelSubscription,
  getPaymentMethods
} from './stripe';;

export interface SubscriptionDetails {
  id: string;
  status: string;
  type: string;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  createdAt: number;
  userId: string;
  paymentMethodId?: string;
  priceId?: string;
}

/**
 * Create a user's subscription record in the database
 */
export async function createUserSubscription(
  userId: string,
  subscriptionId: string,
  subscriptionType: string,
  status: string,
  currentPeriodEnd: number,
  cancelAtPeriodEnd: boolean
): Promise<boolean> {
  try {
    const query = `
      MATCH (u:User {id: $userId})
      MERGE (s:Subscription {id: $subscriptionId})
      SET s.type = $subscriptionType,
          s.status = $status,
          s.currentPeriodEnd = $currentPeriodEnd,
          s.cancelAtPeriodEnd = $cancelAtPeriodEnd,
          s.createdAt = timestamp()
      MERGE (u)-[:HAS_SUBSCRIPTION]->(s)
      RETURN s.id as id
    `;

    const result = await executeQuery(query, {
      userId,
      subscriptionId,
      subscriptionType,
      status,
      currentPeriodEnd,
      cancelAtPeriodEnd
    });

    return result.length > 0;
  } catch (error) {
    console.error('Error creating subscription record:', error);
    return false;
  }
}

/**
 * Update a user's subscription record in the database
 */
export async function updateUserSubscription(
  subscriptionId: string,
  status: string,
  currentPeriodEnd: number,
  cancelAtPeriodEnd: boolean
): Promise<boolean> {
  try {
    const query = `
      MATCH (s:Subscription {id: $subscriptionId})
      SET s.status = $status,
          s.currentPeriodEnd = $currentPeriodEnd,
          s.cancelAtPeriodEnd = $cancelAtPeriodEnd,
          s.updatedAt = timestamp()
      RETURN s.id as id
    `;

    const result = await executeQuery(query, {
      subscriptionId,
      status,
      currentPeriodEnd,
      cancelAtPeriodEnd
    });

    return result.length > 0;
  } catch (error) {
    console.error('Error updating subscription record:', error);
    return false;
  }
}

/**
 * Get a user's subscription from the database
 */
export async function getUserSubscription(userId: string): Promise<SubscriptionDetails | null> {
  try {
    const query = `
      MATCH (u:User {id: $userId})-[:HAS_SUBSCRIPTION]->(s:Subscription)
      RETURN s.id as id, 
             s.status as status, 
             s.type as type, 
             s.currentPeriodEnd as currentPeriodEnd,
             s.cancelAtPeriodEnd as cancelAtPeriodEnd,
             s.createdAt as createdAt,
             u.id as userId
    `;

    const result = await executeQuery(query, { userId });

    if (result.length === 0) {
      return null;
    }

    const record = result[0].toObject();
    
    // Convert to the expected type
    const subscription: SubscriptionDetails = {
      id: record.id,
      status: record.status,
      type: record.type,
      currentPeriodEnd: record.currentPeriodEnd,
      cancelAtPeriodEnd: record.cancelAtPeriodEnd,
      createdAt: record.createdAt,
      userId: record.userId
    };

    return subscription;
  } catch (error) {
    console.error('Error getting user subscription:', error);
    return null;
  }
}

/**
 * Subscribe a user to a plan
 */
export async function subscribeUser(
  userId: string,
  customerId: string,
  priceId: string,
  paymentMethodId: string,
  subscriptionType: string
): Promise<{ success: boolean; subscriptionId?: string; error?: string }> {
  try {
    // Create the subscription in Stripe
    const subscription = await createSubscription(
      customerId,
      priceId,
      paymentMethodId,
      { userId }
    );

    if (!subscription || !subscription.id) {
      return { success: false, error: 'Failed to create subscription' };
    }

    // Store the subscription in our database
    const result = await createUserSubscription(
      userId,
      subscription.id,
      subscriptionType,
      subscription.status,
      subscription.current_period_end,
      subscription.cancel_at_period_end
    );

    if (!result) {
      return { success: false, error: 'Failed to record subscription' };
    }

    return { success: true, subscriptionId: subscription.id };
  } catch (error) {
    console.error('Error subscribing user:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Cancel a user's subscription
 */
export async function cancelUserSubscription(
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Get the user's subscription from our database
    const subscription = await getUserSubscription(userId);

    if (!subscription) {
      return { success: false, error: 'No active subscription found' };
    }

    // Cancel the subscription in Stripe
    await cancelSubscription(subscription.id);

    // Update our database
    const result = await updateUserSubscription(
      subscription.id,
      'canceled',
      subscription.currentPeriodEnd,
      true
    );

    if (!result) {
      return { success: false, error: 'Failed to update subscription status' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Check if a subscription is active
 */
export async function isSubscriptionActive(userId: string): Promise<boolean> {
  try {
    const subscription = await getUserSubscription(userId);

    if (!subscription) {
      return false;
    }

    // Check if subscription is active and not expired
    const isActive = 
      subscription.status === 'active' && 
      subscription.currentPeriodEnd * 1000 > Date.now();

    return isActive;
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return false;
  }
}

/**
 * Get user's subscription tier
 */
export async function getUserSubscriptionTier(userId: string): Promise<string> {
  try {
    const subscription = await getUserSubscription(userId);

    if (!subscription || subscription.status !== 'active') {
      return 'free';
    }

    return subscription.type;
  } catch (error) {
    console.error('Error getting subscription tier:', error);
    return 'free';
  }
}

/**
 * Get available payment methods for a user
 */
export async function getUserPaymentMethods(customerId: string): Promise<any[]> {
  try {
    const methods = await getPaymentMethods(customerId);
    return methods?.data || [];
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    return [];
  }
}

/**
 * Process Stripe webhook event for subscription updates
 */
export async function processSubscriptionEvent(event: any): Promise<boolean> {
  try {
    const subscription = event.data.object;
    const subscriptionId = subscription.id;
    const status = subscription.status;
    const currentPeriodEnd = subscription.current_period_end;
    const cancelAtPeriodEnd = subscription.cancel_at_period_end;

    // Update our database with the new subscription status
    const result = await updateUserSubscription(
      subscriptionId,
      status,
      currentPeriodEnd,
      cancelAtPeriodEnd
    );

    return result;
  } catch (error) {
    console.error('Error processing subscription event:', error);
    return false;
  }
} 