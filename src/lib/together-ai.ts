import Together from 'together-ai';

// Initialize Together AI client with the API key
const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY || '',
});

/**
 * Send a message to the DeepSeek model on Together AI
 */
export async function sendMessageToDeepSeek(
  message: string,
  options: {
    temperature?: number;
    max_tokens?: number;
  } = {}
) {
  try {
    // Default options
    const temperature = options.temperature ?? 0.7;
    const max_tokens = options.max_tokens ?? 4000;
    
    // Format the messages for the chat completion API
    const messages = [
      { role: 'user', content: message }
    ];
    
    // Call the Together AI chat completions API using dynamic access to avoid type issues
    // @ts-ignore - Together types may not be up to date
    const response = await together.chat.completions.create({
      messages: messages,
      model: 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free',
      max_tokens: max_tokens,
      temperature: temperature,
      top_p: 0.7,
      top_k: 50,
      repetition_penalty: 1,
      stop: ["<human>:", "\n<human>:"],
      stream: false
    });
    
    // Format the response
    return {
      message: response.choices[0].message.content,
      model: 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free',
      provider: 'Together AI'
    };
  } catch (error) {
    console.error('Error in Together AI completion:', error);
    throw error;
  }
}

/**
 * Send a message to the Llama 3.3 model on Together AI
 */
export async function sendMessageToLlama(
  message: string,
  options: {
    temperature?: number;
    max_tokens?: number;
  } = {}
) {
  try {
    // Default options
    const temperature = options.temperature ?? 0.7;
    const max_tokens = options.max_tokens ?? 512;
    
    // Format the messages for the chat completion API
    const messages = [
      { role: 'user', content: message }
    ];
    
    // Call the Together AI chat completions API using dynamic access to avoid type issues
    // @ts-ignore - Together types may not be up to date
    const response = await together.chat.completions.create({
      messages: messages,
      model: 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free',
      max_tokens: max_tokens,
      temperature: temperature,
      top_p: 0.7,
      top_k: 50,
      repetition_penalty: 1,
      stop: ["<human>:", "\n<human>:"],
      stream: false
    });
    
    // Format the response
    return {
      message: response.choices[0].message.content,
      model: 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free',
      provider: 'Together AI'
    };
  } catch (error) {
    console.error('Error in Together AI Llama completion:', error);
    throw error;
  }
}

export default {
  sendMessageToDeepSeek,
  sendMessageToLlama
}; 