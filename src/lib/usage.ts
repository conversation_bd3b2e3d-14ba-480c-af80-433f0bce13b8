import { FeatureAccess } from './permissions';
import { createClient } from '@vercel/kv';

// Create KV client for usage tracking
let kvClient: ReturnType<typeof createClient> | null = null;
let mockKvEnabled = false;

// Mock KV implementation for when real KV isn't available
class MockKvClient {
  private store: Map<string, { value: any, expiry?: number }>;
  
  constructor() {
    this.store = new Map();
    console.warn('Using mock KV store for usage tracking - limits will not be enforced across instances');
  }
  
  async get<T>(key: string): Promise<T | null> {
    const item = this.store.get(key);
    if (!item) return null;
    
    // Check if expired
    if (item.expiry && item.expiry < Date.now()) {
      this.store.delete(key);
      return null;
    }
    
    return item.value as T;
  }
  
  async set(key: string, value: any, options?: { ex?: number }): Promise<string> {
    let expiry: number | undefined = undefined;
    if (options?.ex) {
      expiry = Date.now() + (options.ex * 1000);
    }
    
    this.store.set(key, { value, expiry });
    return 'OK';
  }
  
  async incr(key: string): Promise<number> {
    const current = await this.get<number>(key) || 0;
    const newValue = current + 1;
    await this.set(key, newValue);
    return newValue;
  }
  
  async expire(key: string, seconds: number): Promise<number> {
    const item = this.store.get(key);
    if (!item) return 0;
    
    item.expiry = Date.now() + (seconds * 1000);
    this.store.set(key, item);
    return 1;
  }
}

// Initialize the KV client
function getKvClient() {
  if (!kvClient) {
    // Check for required env variables
    if (!process.env.REDIS_URL || !process.env.REDIS_PASSWORD) {
      console.warn('Missing KV store credentials - usage limits will not be strictly enforced');
      mockKvEnabled = true;
      kvClient = new MockKvClient() as any; // Cast to any to satisfy type checking
    } else {
      try {
        // Ensure the URL has a proper scheme and format
        let redisUrl = process.env.REDIS_URL;
        
        // If URL starts with redis:// we should keep it as is for Upstash Redis
        if (redisUrl.startsWith('redis://')) {
          // Already has the correct scheme
          console.log('Using Redis URL with redis:// scheme:', redisUrl);
        }
        // If it's just a hostname without a scheme, properly format it
        else if (!redisUrl.match(/^[a-zA-Z]+:\/\//)) {
          // Check if it contains a valid hostname (not just "redis")
          if (redisUrl === 'redis' || redisUrl.trim() === '') {
            console.error('Invalid Redis URL: Just "redis" or empty is not a valid hostname');
            throw new Error('Invalid Redis hostname');
          }
          
          // Add the proper Redis protocol
          redisUrl = `redis://${redisUrl}`;
          console.log('Added redis:// scheme to Redis URL:', redisUrl);
        }
        
        kvClient = createClient({
          url: redisUrl,
          token: process.env.REDIS_PASSWORD,
        });
      } catch (error) {
        console.error('Error initializing KV client:', error);
        console.warn('Falling back to mock KV client');
        mockKvEnabled = true;
        kvClient = new MockKvClient() as any;
      }
    }
  }
  
  return kvClient;
}

/**
 * Get the user's current usage for a specific feature today
 * @param userId User ID to check usage for
 * @param feature Feature to check usage for
 * @returns The current usage count
 */
export async function getUserDailyUsage(userId: string, feature: FeatureAccess): Promise<number> {
  try {
    const kv = getKvClient();
    if (!kv) {
      console.error('Failed to initialize KV client');
      return 0;
    }
    
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const key = `usage:${userId}:${feature}:${today}`;
    
    const usage = await kv.get<number>(key);
    return usage || 0;
  } catch (error) {
    console.error('Error getting user usage:', error);
    return 0; // Fail open to not block users if tracking fails
  }
}

/**
 * Increment user usage for a specific feature
 * @param userId User ID to increment usage for
 * @param feature Feature to increment usage for
 * @returns The new usage count
 */
export async function incrementUserUsage(userId: string, feature: FeatureAccess): Promise<number> {
  try {
    const kv = getKvClient();
    if (!kv) {
      console.error('Failed to initialize KV client');
      return 0;
    }
    
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const key = `usage:${userId}:${feature}:${today}`;
    
    // Set expiry for 48 hours to ensure we keep today's data through tomorrow
    const newValue = await kv.incr(key);
    await kv.expire(key, 60 * 60 * 48); // 48 hours in seconds
    
    return newValue;
  } catch (error) {
    console.error('Error incrementing user usage:', error);
    return 0; // Fail open
  }
}

/**
 * Check if a user can use a feature and increment their usage if allowed
 * @param userId User ID
 * @param userPlan User's subscription plan
 * @param feature Feature to check and use
 * @returns Object with allowed status and usage info
 */
export async function checkAndIncrementUsage(
  userId: string,
  userPlan: string | undefined,
  feature: FeatureAccess
): Promise<{ allowed: boolean; currentUsage: number; newUsage?: number }> {
  try {
    // Import here to avoid circular dependencies
    const { hasFeatureAccess, isWithinUsageLimits } = await import('./permissions');
    
    // First check if the feature is available for this plan
    if (!hasFeatureAccess(userPlan, feature)) {
      return { allowed: false, currentUsage: 0 };
    }
    
    // Get current usage
    const currentUsage = await getUserDailyUsage(userId, feature);
    
    // If we're using mock KV and in development, be more lenient with limits
    if (mockKvEnabled && process.env.NODE_ENV === 'development') {
      const newUsage = await incrementUserUsage(userId, feature);
      return {
        allowed: true,
        currentUsage,
        newUsage
      };
    }
    
    // Check if within limits
    if (!isWithinUsageLimits(userPlan, feature, currentUsage)) {
      return { allowed: false, currentUsage };
    }
    
    // If allowed, increment usage
    const newUsage = await incrementUserUsage(userId, feature);
    
    return {
      allowed: true,
      currentUsage,
      newUsage
    };
  } catch (error) {
    console.error('Error checking user usage:', error);
    // In case of error, allow access to avoid blocking users
    return { allowed: true, currentUsage: 0 };
  }
} 