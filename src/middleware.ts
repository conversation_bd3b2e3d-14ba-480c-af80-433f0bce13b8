import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Define paths that don't need subscription validation
const PUBLIC_PATHS = [
  '/api/auth',
  '/api/webhook',
  '/api/stripe',
  '/api/check-payment-success',
  '/api/init-db',
  '/api/test-db-connection',
];

// Define paths that need minimal validation (just authentication)
const AUTH_ONLY_PATHS = [
  '/api/user',
  '/api/storage',
];

// Cache for subscription validation results
// This significantly reduces database load
const validationCache = new Map<string, {
  result: boolean;
  timestamp: number;
  features: string[];
}>();

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // Handle API routes with subscription validation
  if (path.startsWith('/api')) {
    return handleApiRoute(request, path);
  }

  // Handle non-API routes (just pass through for now)
  return NextResponse.next();
}

async function handleApiRoute(request: NextRequest, path: string) {
  // Skip middleware for public API paths
  if (PUBLIC_PATHS.some(p => path.startsWith(p))) {
    return NextResponse.next();
  }
  
  try {
    // Get the token from the request
    const token = await getToken({ req: request });
    
    // If no token, the user is not authenticated
    if (!token) {
      return new NextResponse(
        JSON.stringify({ success: false, message: 'Authentication required' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // For auth-only paths, just check authentication
    if (AUTH_ONLY_PATHS.some(p => path.startsWith(p))) {
      return NextResponse.next();
    }
    
    // For feature-specific paths, check subscription
    // Extract the feature from the path
    const feature = getFeatureFromPath(path);
    
    // If no feature mapping, allow access
    if (!feature) {
      return NextResponse.next();
    }
    
    // Check cache first
    const userId = token.id as string;
    const cacheKey = `${userId}:${feature}`;
    const cachedValidation = validationCache.get(cacheKey);
    const now = Date.now();
    
    if (cachedValidation && now - cachedValidation.timestamp < CACHE_TTL) {
      // Use cached validation result
      if (cachedValidation.result) {
        return NextResponse.next();
      } else {
        return new NextResponse(
          JSON.stringify({ success: false, message: 'Subscription required for this feature' }),
          { status: 403, headers: { 'Content-Type': 'application/json' } }
        );
      }
    }
    
    // Check subscription from token first (fastest)
    if (token.subscription) {
      const hasAccess = checkFeatureAccess(feature, token.subscription.type, token.subscription.status);
      
      // Cache the result
      validationCache.set(cacheKey, {
        result: hasAccess,
        timestamp: now,
        features: [feature]
      });
      
      if (hasAccess) {
        return NextResponse.next();
      } else {
        return new NextResponse(
          JSON.stringify({ success: false, message: 'Subscription required for this feature' }),
          { status: 403, headers: { 'Content-Type': 'application/json' } }
        );
      }
    }
    
    // Allow access by default if we can't determine subscription
    // This prevents API failures due to auth issues
    return NextResponse.next();
    
  } catch (error) {
    console.error('Middleware error:', error);
    
    // Allow the request to proceed to avoid blocking legitimate requests
    // The API route can handle more detailed validation if needed
    return NextResponse.next();
  }
}

// Map API paths to features
function getFeatureFromPath(path: string): string | null {
  if (path.includes('/api/generate-text') || path.includes('/api/chat')) {
    return 'TEXT_GENERATION';
  }
  if (path.includes('/api/generate') && path.includes('/image')) {
    return 'IMAGE_GENERATION';
  }
  if (path.includes('/api/generate-music')) {
    return 'MUSIC_GENERATION';
  }
  if (path.includes('/api/generate-video')) {
    return 'VIDEO_GENERATION';
  }
  if (path.includes('/api/generate-podcast')) {
    return 'PODCAST_GENERATION';
  }
  
  // Default to null (no feature-specific check)
  return null;
}

// Check if a user has access to a specific feature based on subscription
function checkFeatureAccess(feature: string, subscriptionType?: string, subscriptionStatus?: string): boolean {
  // If no subscription info, assume no access
  if (!subscriptionType || !subscriptionStatus) {
    return false;
  }
  
  // Subscription must be active or in trial
  if (subscriptionStatus !== 'active' && subscriptionStatus !== 'trialing') {
    return false;
  }
  
  // Free tier features
  const freeFeatures = ['FREE_TEXT_GENERATION', 'FREE_IMAGE_GENERATION'];
  if (freeFeatures.includes(feature)) {
    return true;
  }
  
  // Standard tier features
  if (subscriptionType === 'Standard' || subscriptionType === 'standard') {
    const standardFeatures = [
      'TEXT_GENERATION',
      'IMAGE_GENERATION',
      'IMAGE_EDITING',
    ];
    return standardFeatures.includes(feature);
  }
  
  // Premium tier has access to all features
  if (subscriptionType === 'Premium' || subscriptionType === 'premium') {
    return true;
  }
  
  return false;
}

// Configure which paths should be processed by this middleware
export const config = {
  matcher: [
    // Match all API routes for subscription validation
    '/api/:path*',
    // Match all non-API routes for internationalization
    '/((?!api|_next|_vercel|.*\\..*).*)'
  ],
};