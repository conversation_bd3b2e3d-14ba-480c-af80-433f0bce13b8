import { AIModel } from '@/types/models';

export interface Conversation {
  id: string;
  userId: string;
  title: string;
  model?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  model?: string;
  // For tracking streaming progress
  pending?: boolean;
  error?: boolean;
}

export interface ChatState {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  messages: Message[];
  loading: boolean;
  error: string | null;
  pending: boolean;
  selectedModel: AIModel | null;
  showModelSelector: boolean;
}

export type ChatAction = 
  | { type: 'SET_CONVERSATIONS'; payload: Conversation[] }
  | { type: 'SET_ACTIVE_CONVERSATION'; payload: Conversation }
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PENDING'; payload: boolean }
  | { type: 'SET_SELECTED_MODEL'; payload: AIModel | null }
  | { type: 'TOGGLE_MODEL_SELECTOR'; payload?: boolean }
  | { type: 'UPDATE_MESSAGE'; payload: { id: string; updates: Partial<Message> } }
  | { type: 'CLEAR_CONVERSATION' }; 