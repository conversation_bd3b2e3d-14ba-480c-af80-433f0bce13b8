// Base model interface that all tools will use
export interface AIModel {
  id: string;
  name: string;
  description?: string;
  provider: string;
  capabilities?: string[];
  maxDuration?: number;
  [key: string]: any; // For additional tool-specific properties
}

// Categories of models
export interface ModelCategories {
  [category: string]: AIModel[];
}

// Text model constants
export const TEXT_MODELS: AIModel[] = [
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    description: 'OpenAI\'s most powerful model with image understanding',
    provider: 'OpenAI',
    capabilities: ['Fast', 'Accurate', 'Image understanding']
  },
  {
    id: 'claude-3-opus',
    name: 'Claude 3 Opus',
    description: 'Anthropic\'s most capable model, excellent for complex tasks',
    provider: 'Anthropic',
    capabilities: ['Powerful', 'Nuanced responses', 'Thoughtful']
  },
  {
    id: 'claude-3-sonnet',
    name: 'Claude 3 Sonnet',
    description: 'Good balance of intelligence and speed',
    provider: 'Anthropic',
    capabilities: ['Balanced', 'Fast', 'Cost-effective']
  },
  {
    id: 'gemini-pro',
    name: 'Gemini Pro',
    description: 'Google\'s advanced reasoning model',
    provider: 'Google',
    capabilities: ['Reasoning', 'Creative', 'Integrations']
  },
  {
    id: 'mistral-large',
    name: 'Mistral Large',
    description: 'Mistral\'s most powerful model',
    provider: 'Mistral',
    capabilities: ['Efficient', 'Cost-effective', 'Multilingual']
  }
];

// Default model categories for chat
export const DEFAULT_CHAT_MODEL_CATEGORIES: ModelCategories = {
  'Featured': TEXT_MODELS.slice(0, 3),
  'All Models': TEXT_MODELS
}; 