declare module 'together-ai' {
  interface TogetherOptions {
    apiKey?: string;
  }

  interface ImageGenerationOptions {
    model: string;
    prompt: string;
    negative_prompt?: string;
    width?: number;
    height?: number;
    steps?: number;
    n?: number;
    response_format?: 'url' | 'b64_json';
  }

  interface ImageGenerationResult {
    data: {
      b64_json?: string;
      url?: string;
    }[];
  }

  class TogetherClient {
    constructor(options?: TogetherOptions);
    
    images: {
      create: (options: ImageGenerationOptions) => Promise<ImageGenerationResult>;
    };
  }

  export default TogetherClient;
} 