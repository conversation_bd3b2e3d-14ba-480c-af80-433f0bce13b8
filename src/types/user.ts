export interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
  subscription?: Subscription;
}

export interface Subscription {
  id: string;
  userId: string;
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  plan: 'free' | 'basic' | 'pro' | 'enterprise';
  priceId: string;
  startDate: Date;
  endDate: Date;
  trialEnd?: Date;
}

export interface UserSession {
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  subscription?: Subscription;
  expires: string;
} 