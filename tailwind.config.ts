import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: 'rgb(var(--primary))',
          50: 'rgba(var(--primary), 0.05)',
          100: 'rgba(var(--primary), 0.1)',
          200: 'rgba(var(--primary), 0.2)',
          300: 'rgba(var(--primary), 0.3)',
          400: 'rgba(var(--primary), 0.4)',
          500: 'rgba(var(--primary), 0.5)',
          600: 'rgba(var(--primary), 0.6)',
          700: 'rgba(var(--primary), 0.7)',
          800: 'rgba(var(--primary), 0.8)',
          900: 'rgba(var(--primary), 0.9)',
        },
        secondary: {
          DEFAULT: 'rgb(var(--secondary))',
          50: 'rgba(var(--secondary), 0.05)',
          100: 'rgba(var(--secondary), 0.1)',
          200: 'rgba(var(--secondary), 0.2)',
          300: 'rgba(var(--secondary), 0.3)',
          400: 'rgba(var(--secondary), 0.4)',
          500: 'rgba(var(--secondary), 0.5)',
          600: 'rgba(var(--secondary), 0.6)',
          700: 'rgba(var(--secondary), 0.7)',
          800: 'rgba(var(--secondary), 0.8)',
          900: 'rgba(var(--secondary), 0.9)',
        },
        accent: {
          DEFAULT: 'rgb(var(--accent))',
          50: 'rgba(var(--accent), 0.05)',
          100: 'rgba(var(--accent), 0.1)',
          200: 'rgba(var(--accent), 0.2)',
          300: 'rgba(var(--accent), 0.3)',
          400: 'rgba(var(--accent), 0.4)',
          500: 'rgba(var(--accent), 0.5)',
          600: 'rgba(var(--accent), 0.6)',
          700: 'rgba(var(--accent), 0.7)',
          800: 'rgba(var(--accent), 0.8)',
          900: 'rgba(var(--accent), 0.9)',
        },
      },
      animation: {
        'gradient-x': 'gradient-x 15s ease infinite',
        'gradient-y': 'gradient-y 15s ease infinite',
        'gradient-xy': 'gradient-xy 15s ease infinite',
      },
      keyframes: {
        'gradient-x': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center',
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center',
          },
        },
        'gradient-y': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'center top',
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'center bottom',
          },
        },
        'gradient-xy': {
          '0%, 100%': {
            'background-size': '400% 400%',
            'background-position': 'left top',
          },
          '25%': {
            'background-size': '400% 400%',
            'background-position': 'right top',
          },
          '50%': {
            'background-size': '400% 400%',
            'background-position': 'right bottom',
          },
          '75%': {
            'background-size': '400% 400%',
            'background-position': 'left bottom',
          },
        },
      },
    },
  },
  plugins: [],
};
export default config; 